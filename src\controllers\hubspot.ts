/* eslint-disable prettier/prettier */
import { HUBSPOT_TOKEN, HUBSPOT_URL, hubspot } from '../constants';
import { AsyncController } from '../types&interfaces/types';
import { sendHilos } from '../services/onboarding/sendHilos';
import Hu<PERSON>pot from '../models/hubspot';
import { AdmissionRequestMongo } from '../models/admissionRequestSchema';
import { changeProperties } from '../services/hubspot';
import axios from 'axios';

import { HILOS_API_KEY } from '../constants';

const cityTags: { [key: string]: string } = {
  cdmx: "CDMX/EDOMEX",
  gdl: "Guadalajara",
  mty: "Monterrey",
  pue: "Puebla",
  pbc: "Puebla",
  pbe: "Puebla",
  tij: "Tijuana",
  qro: "Queretaro",
  cvj: "Cuernavaca",
}

export const createContact: AsyncController = async (req, res) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { phone, firstname, city, email, fuente_de_contacto, plan, personal_payment } =
    req.body;

  const obj = {
    phone: phone,
    firstname: firstname,
    ciudad__con_selector_: cityTags[city],
    email: email,
    fuente_de_contacto: fuente_de_contacto,
    plan: plan,
    personal_payment: personal_payment,
  };

  try {
    const response = await axios.post(HUBSPOT_URL, { properties: obj }, {
      headers: {
        Authorization: `Bearer ${HUBSPOT_TOKEN}`,
      },
    });

    return res.status(200).send({ message: hubspot.succes.created, data: response.data });
  } catch (error: any) {
    console.error(error)
    if (error.response.data.message.includes("already exists"))
      return res.status(409).send(
        { message: hubspot.errors.existingEmail, response: error.response.data.message })
    return res.status(500).send({ message: hubspot.errors.genericError, error });
  }
};

export const createElectricContact: AsyncController = async (req, res) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const { phone, firstname, city, email, fuente_de_contacto, electric_car, electric_car_usa } =
    req.body;

  const obj = electric_car_usa ? {
    phone: phone,
    firstname: firstname,
    email: email,
    fuente_de_contacto: fuente_de_contacto,
    electric_car_usa : electric_car_usa,
  } :
    {
    phone: phone,
    firstname: firstname,
    ciudad__con_selector_: cityTags[city],
    email: email,
    fuente_de_contacto: fuente_de_contacto,
    electric_car: electric_car,
  };

  try {
    const response = await axios.post(HUBSPOT_URL, { properties: obj }, {
      headers: {
        Authorization: `Bearer ${HUBSPOT_TOKEN}`,
      },
    });

    return res.status(200).send({ message: hubspot.succes.created, data: response.data });
  } catch (error: any) {
    console.error(error)
    if (error.response.data.message.includes("already exists"))
      return res.status(409).send(
        { message: hubspot.errors.existingEmail, response: error.response.data.message })
    return res.status(500).send({ message: hubspot.errors.genericError, error });
  }
};

export const webhook: AsyncController = async (req, res) => {
  const { body } = req;
  try {
    await Hubspot.create({ body });
    const event = body.properties.hs_lead_status.value || null;
    const phone = body.properties?.hs_calculated_phone_number?.value || null;
    const firstName = body.properties?.firstname.value;
    const lastName = body.properties?.lastname.value;
    const email = body.properties?.email.value;
    const source = body.source;
    const clientIpAddress = body.clientIpAddress;

    const personalData = {
      firstName,
      lastName,
      email,
      country :'mx',
    };

    if (event === "NEW" && phone) {
      const sendMessage = await sendHilos({ phone, personalData, type: 'newLead', hubspotId: body.id, source: source, clientIpAddress });
      // console.log(sendMessage);
      if (sendMessage.error) {
        return res.status(500).send({ message: hubspot.errors.genericError, error: sendMessage.error });
      }
      return res.status(200).send({ message: hubspot.succes, sendMessage });
    }
    return res.status(200).send({ message: hubspot.succes });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: hubspot.errors.genericError, error });
  }
}

interface HubspotData {
  id: string;
  // Otras propiedades de hubspot si las hay
}

interface AdmissionRequest {
  _id: string;
  hubspot?: HubspotData; // El signo de interrogación indica que hubspot puede ser opcional
  hilos?: {
    id?: string;
    contact?: string;
  };
  // Otras propiedades de AdmissionRequest
}

export const updateContact: AsyncController = async (req, res) => {
  const { requestId } = req.params;
  const { properties } = req.body;

  if (!requestId || properties) return res.status(400).send({ message: 'Faltan datos' });
  const request = await AdmissionRequestMongo.findById(requestId).lean<AdmissionRequest>();

  if (!request) return res.status(404).send({ message: 'Faltan datos' });
  const hubspotId = request?.hubspot?.id;

  if (!hubspotId) return res.status(404).send({ message: 'No hay cuenta de hubspot en la request' });

  try {
    await changeProperties({ hubspotId, properties });
    return res.status(200).send({ message: 'Contacto actualizado' });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: 'Error al actualizar contacto' });
  }
}

export const noDocumentsTrigger: AsyncController = async (req, res) => {
  const noDocuments = await AdmissionRequestMongo.find({ "documentsAnalysis.documents": { $not: { $elemMatch: { "mediaId": { $ne: null } } } } }).lean();
  return res.status(200).send(noDocuments);
}

export const linkOpened: AsyncController = async (req, res) => {
  const requestId = req.params.requestId;
  const request = await AdmissionRequestMongo.findByIdAndUpdate(
     requestId,
    { $push: { openLinkDates: new Date() }, isRequestLinkOpened: true  }
  ).lean();

  if(!request) return res.status(404).send({ message: 'No se encontró la solicitud' });
  return res.status(200).send({
    message: 'Link abierto',
    date: new Date(),
  });
}

export const noContactTrigger: AsyncController = async (_, res) => {
  try {
    const noContact = await AdmissionRequestMongo.find({
      "hubspot.id": { $exists: true },
      isRequestLinkOpened: false,
    }).lean();


    const results = await Promise.all(noContact.map(async (request) => {
      const hubspotId = (request.hubspot as HubspotData).id;
      try {
        await changeProperties({ hubspotId, properties: { "hs_lead_status": "ATTEMPTED_TO_CONTACT" } });
        return { success: true, message: 'Contacto actualizado', request };
      } catch (error) {
        console.error(`Error actualizando contacto ${hubspotId}:`, error);
        return { success: false, message: 'Error al actualizar contacto', request, error };
      }
    }));

    const successfulUpdates = results.filter(result => result.success);
    const failedUpdates = results.filter(result => !result.success);

    return res.status(200).send({
      message: 'Proceso completado',
      successfulUpdates: successfulUpdates.length,
      failedUpdates: failedUpdates.map(result => ({ request: result.request, error: result.error })),
      total: noContact.length,
    });

  } catch (error) {
    console.error("Error general en noContactTrigger:", error);
    return res.status(500).send({ message: 'Error general al procesar los contactos' });
  }
};

export const hilosRequestID: AsyncController = async (_, res) => {

  const admission = await AdmissionRequestMongo.find({ 'hilos.contact': { $exists: true } }).sort({ createdAt: -1 }).lean();
  const resp: Response[] = [];
  console.log(admission.length);

  for (const admissionReq of admission) {
    try {
      // Espera 5 segundos antes de realizar cada solicitud
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const response = await fetch(`https://api.hilos.io/api/contact/${admissionReq.hilos.contact}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${HILOS_API_KEY}`,
        },
        body: JSON.stringify({
          "meta": {
            "request_id": admissionReq._id.toString(),
          },
        }),
      });

      resp.push(response);
    } catch (error) {
      console.error(error);
    }
  }

  return res.status(200).send({ message: 'Proceso completado', resp });
}
