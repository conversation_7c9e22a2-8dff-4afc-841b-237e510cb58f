import { Types } from 'mongoose';
import FleetOrder, { IFleetOrder, FleetOrderStatus, IStatusHistory } from '../models/fleet-order.model';
import { generateOrderNumber } from '../utils/order-number-generator';
import { calculateSLADates } from '../utils/sla-calculator';
import { CreateFleetOrderDTO } from '../dtos/create-fleet-order.dto';
import { UpdateFleetOrderStatusDTO } from '../dtos/update-fleet-order-status.dto';
import { DispersionDTO } from '../dtos/dispersion.dto';

export class FleetOrdersService {
  
  /**
   * Crear una nueva orden de flota
   */
  async createFleetOrder(
    data: CreateFleetOrderDTO,
    createdBy: Types.ObjectId
  ): Promise<IFleetOrder> {
    // Verificar que no exista ya una orden para el mes/año
    const existingOrder = await FleetOrder.findOne({
      month: data.month,
      year: data.year,
    });

    if (existingOrder) {
      throw new Error(`Ya existe una orden para ${data.month}/${data.year}`);
    }

    // Generar número de orden
    const orderNumber = await generateOrderNumber(data.year, data.month);

    // Calcular fechas límite de SLA
    const slaDate = calculateSLADates(data.year, data.month);

    // Calcular totales
    const totalUnits = data.vehicles.reduce((sum, vehicle) => sum + vehicle.quantity, 0);
    const totalAmount = data.vehicles.reduce(
      (sum, vehicle) => sum + (vehicle.quantity * vehicle.unitPrice), 
      0
    );

    // Crear historial inicial
    const initialHistory: IStatusHistory = {
      status: FleetOrderStatus.CREATED,
      timestamp: new Date(),
      userId: createdBy,
      evidence: {
        type: 'log' as any,
        description: 'Orden de flota creada',
      },
    };

    // Crear la orden
    const fleetOrder = new FleetOrder({
      orderNumber,
      month: data.month,
      year: data.year,
      status: FleetOrderStatus.CREATED,
      vehicles: data.vehicles.map(vehicle => ({
        ...vehicle,
        totalAmount: vehicle.quantity * vehicle.unitPrice,
      })),
      totalUnits,
      totalAmount,
      notificationEmails: data.notificationEmails,
      sentDeadline: slaDate.sentDeadline,
      dispersionDeadline: slaDate.dispersionDeadline,
      invoiceLetterRequestDeadline: slaDate.invoiceLetterRequestDeadline,
      invoiceLetterArrivalDeadline: slaDate.invoiceLetterArrivalDeadline,
      statusHistory: [initialHistory],
      createdBy,
      updatedBy: createdBy,
    });

    return await fleetOrder.save();
  }

  /**
   * Obtener una orden por ID
   */
  async getFleetOrderById(id: Types.ObjectId): Promise<IFleetOrder | null> {
    return await FleetOrder.findById(id)
      .populate('creator', 'name email')
      .populate('updater', 'name email')
      .lean();
  }

  /**
   * Obtener orden por número de orden
   */
  async getFleetOrderByNumber(orderNumber: string): Promise<IFleetOrder | null> {
    return await FleetOrder.findOne({ orderNumber })
      .populate('creator', 'name email')
      .populate('updater', 'name email')
      .lean();
  }

  /**
   * Listar órdenes con filtros y paginación
   */
  async listFleetOrders(filters: {
    status?: FleetOrderStatus;
    month?: number;
    year?: number;
    page?: number;
    limit?: number;
  }): Promise<{
    orders: IFleetOrder[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const { status, month, year, page = 1, limit = 10 } = filters;

    // Construir query
    const query: any = {};
    if (status) query.status = status;
    if (month) query.month = month;
    if (year) query.year = year;

    // Ejecutar consulta con paginación
    const skip = (page - 1) * limit;
    
    const [orders, total] = await Promise.all([
      FleetOrder.find(query)
        .populate('creator', 'name email')
        .populate('updater', 'name email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      FleetOrder.countDocuments(query),
    ]);

    return {
      orders,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Actualizar estado de una orden
   */
  async updateFleetOrderStatus(
    id: Types.ObjectId,
    data: UpdateFleetOrderStatusDTO,
    updatedBy: Types.ObjectId
  ): Promise<IFleetOrder | null> {
    const order = await FleetOrder.findById(id);
    
    if (!order) {
      throw new Error('Orden no encontrada');
    }

    // Validar transición de estado
    this.validateStatusTransition(order.status, data.status);

    // Crear entrada en el historial
    const historyEntry: IStatusHistory = {
      status: data.status,
      timestamp: new Date(),
      userId: updatedBy,
      evidence: data.evidence,
      notes: data.notes,
    };

    // Actualizar la orden
    order.status = data.status;
    order.statusHistory.push(historyEntry);
    order.updatedBy = updatedBy;

    return await order.save();
  }

  /**
   * Actualizar dispersión de una orden
   */
  async updateDispersion(
    id: Types.ObjectId,
    dispersion: DispersionDTO[],
    updatedBy: Types.ObjectId
  ): Promise<IFleetOrder | null> {
    const order = await FleetOrder.findById(id);
    
    if (!order) {
      throw new Error('Orden no encontrada');
    }

    if (order.status !== FleetOrderStatus.SENT) {
      throw new Error('Solo se puede actualizar la dispersión cuando la orden está en estado "sent"');
    }

    // Convertir fechas de string a Date
    const processedDispersion = dispersion.map(d => ({
      ...d,
      deliveryDate: new Date(d.deliveryDate),
    }));

    // Actualizar la orden
    order.dispersion = processedDispersion;
    order.updatedBy = updatedBy;

    return await order.save();
  }

  /**
   * Actualizar cartas factura
   */
  async updateInvoiceLetters(
    id: Types.ObjectId,
    data: {
      requestedAt?: Date;
      arrivedAt?: Date;
      evidence?: {
        type: 'photo' | 'pdf';
        url: string;
      };
    },
    updatedBy: Types.ObjectId
  ): Promise<IFleetOrder | null> {
    const order = await FleetOrder.findById(id);
    
    if (!order) {
      throw new Error('Orden no encontrada');
    }

    // Actualizar cartas factura
    if (!order.invoiceLetters) {
      order.invoiceLetters = {} as any;
    }

    if (data.requestedAt) {
      order.invoiceLetters.requestedAt = data.requestedAt;
    }

    if (data.arrivedAt) {
      order.invoiceLetters.arrivedAt = data.arrivedAt;
    }

    if (data.evidence) {
      order.invoiceLetters.evidence = data.evidence;
    }

    order.updatedBy = updatedBy;

    return await order.save();
  }

  /**
   * Actualizar notificaciones a proveedores
   */
  async updateSupplierNotifications(
    id: Types.ObjectId,
    data: {
      sentAt: Date;
      evidence: {
        description: string;
        timestamp: Date;
      };
    },
    updatedBy: Types.ObjectId
  ): Promise<IFleetOrder | null> {
    const order = await FleetOrder.findById(id);
    
    if (!order) {
      throw new Error('Orden no encontrada');
    }

    // Actualizar notificaciones a proveedores
    order.supplierNotifications = data;
    order.updatedBy = updatedBy;

    return await order.save();
  }

  /**
   * Eliminar una orden (solo si está en estado CREATED)
   */
  async deleteFleetOrder(id: Types.ObjectId): Promise<boolean> {
    const order = await FleetOrder.findById(id);
    
    if (!order) {
      throw new Error('Orden no encontrada');
    }

    if (order.status !== FleetOrderStatus.CREATED) {
      throw new Error('Solo se pueden eliminar órdenes en estado "created"');
    }

    await FleetOrder.findByIdAndDelete(id);
    return true;
  }

  /**
   * Validar transición de estado
   */
  private validateStatusTransition(currentStatus: FleetOrderStatus, newStatus: FleetOrderStatus): void {
    const validTransitions: { [key in FleetOrderStatus]: FleetOrderStatus[] } = {
      [FleetOrderStatus.CREATED]: [FleetOrderStatus.SENT],
      [FleetOrderStatus.SENT]: [FleetOrderStatus.DISPERSION],
      [FleetOrderStatus.DISPERSION]: [FleetOrderStatus.INVOICE_LETTER_REQUEST],
      [FleetOrderStatus.INVOICE_LETTER_REQUEST]: [FleetOrderStatus.INVOICE_LETTER_ARRIVAL],
      [FleetOrderStatus.INVOICE_LETTER_ARRIVAL]: [FleetOrderStatus.SUPPLIER_NOTIFICATION],
      [FleetOrderStatus.SUPPLIER_NOTIFICATION]: [FleetOrderStatus.WAITING_FOR_CARS],
      [FleetOrderStatus.WAITING_FOR_CARS]: [FleetOrderStatus.DELIVERED],
      [FleetOrderStatus.DELIVERED]: [], // Estado final
    };

    const allowedTransitions = validTransitions[currentStatus];
    
    if (!allowedTransitions.includes(newStatus)) {
      throw new Error(
        `Transición de estado inválida: de "${currentStatus}" a "${newStatus}". ` +
        `Transiciones válidas: ${allowedTransitions.join(', ')}`
      );
    }
  }

  /**
   * Obtener órdenes que requieren atención por SLA
   */
  async getOrdersRequiringSLAAttention(): Promise<IFleetOrder[]> {
    const now = new Date();
    const twoDaysFromNow = new Date(now.getTime() + (2 * 24 * 60 * 60 * 1000));

    return await FleetOrder.find({
      status: { $in: [
        FleetOrderStatus.CREATED,
        FleetOrderStatus.SENT,
        FleetOrderStatus.DISPERSION,
        FleetOrderStatus.INVOICE_LETTER_REQUEST
      ]},
      $or: [
        { sentDeadline: { $lte: twoDaysFromNow }, status: FleetOrderStatus.CREATED },
        { dispersionDeadline: { $lte: twoDaysFromNow }, status: FleetOrderStatus.SENT },
        { invoiceLetterRequestDeadline: { $lte: twoDaysFromNow }, status: FleetOrderStatus.DISPERSION },
        { invoiceLetterArrivalDeadline: { $lte: twoDaysFromNow }, status: FleetOrderStatus.INVOICE_LETTER_REQUEST },
      ]
    })
    .populate('creator', 'name email')
    .lean();
  }
}
