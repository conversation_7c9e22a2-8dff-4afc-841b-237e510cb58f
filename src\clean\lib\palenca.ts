/**
 * Converts a time string into the equivalent number of days.
 *
 * The function expects the time string to be in formats like "5+ years",
 * "6 years", or "2+ years 3 months", where years and months are represented
 * as integers. It handles optional '+' signs and calculates the total
 * duration in days based on the given years and months. If the input
 * is null or undefined, it returns 0.
 *
 */
export function convertTimeStringToDays(timeStr: string | null): number {
  if (!timeStr) {
    return 0;
  }

  // Remove '+' and split the string into components
  const processedTime = timeStr.replace('+', '').trim();
  const components = processedTime.split(' ');

  let years = 0;
  let months = 0;

  // Iterate over the components to extract years and months
  for (let i = 0; i < components.length; i++) {
    // Check for year and month indicators
    if (components[i].includes('year')) {
      years = parseInt(components[i - 1]) || 0;
    } else if (components[i].includes('month')) {
      months = parseInt(components[i - 1]) || 0;
    }
  }

  // Convert years and months to days
  const days = years * 365 + months * 30;

  return days;
}
