import Axios from 'axios';
import { ValidRegion, tokenAssignGigstack } from './tokenAssignGigstack';

export default async function getGigPaymentById(gigId: string, region: string) {
  const gigToken = tokenAssignGigstack(region as ValidRegion);
  const config = {
    headers: {
      Authorization: `Bearer ${gigToken}`,
      'Content-Type': 'application/json',
    },
  };
  try {
    const response = await Axios.get(
      `https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/view?id=${gigId}`,
      config
    );
    return response.data;
  } catch (error) {
    console.error('error', error);
    return 'error';
  }
}
