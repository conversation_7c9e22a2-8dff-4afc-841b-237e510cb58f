import { Pagination } from '../domain/entities';

export const generatePagination = (page: number, totalItems: number, itemsPerPage: number) => {
  const skip = page * itemsPerPage;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const pagination = new Pagination({
    hasPrevious: totalItems === 0 ? false : page > 1,
    hasMore: skip < totalItems,
    page,
    totalItems,
    totalPages,
  });

  return pagination;
};
