import { CompanyUserRole } from '../models/company-user-permissions.model';
import { AsyncController } from '@/types&interfaces/types';
import { CompanyPermissionsService } from '../services/company-permissions.service';
import { z } from 'zod';

const inviteUserToCompanyDto = z.object({
  email: z.string().email(),
  name: z.string().min(3),
  role: z.nativeEnum(CompanyUserRole),
  allowedCities: z.array(z.string()).optional(),
  allowedCrews: z.array(z.string()).optional(),
});

export const inviteUserToCompany: AsyncController = async (req, res) => {
  // const { email, name, role, allowedCities, allowedCrews } = req.body;
  const { email, name, role, allowedCities, allowedCrews } = inviteUserToCompanyDto.parse(req.body);
  console.table({ email, name, role, allowedCities, allowedCrews });
  const { companyId } = req.params;

  const permissions = await CompanyPermissionsService.inviteUserToCompany({
    email,
    companyId,
    name,
    role: role as CompanyUserRole,
    allowedCities,
    allowedCrews,
    originUrl: req.headers.origin || '',
  });

  return res.status(200).json({
    message: 'User invited successfully',
    data: permissions,
  });
};

export const getUserPermissions: AsyncController = async (req, res) => {
  const { userId, companyId } = req.params;

  const permissions = await CompanyPermissionsService.getUserCompanyPermissions({
    userId,
    companyId,
  });

  return res.status(200).json({
    data: permissions,
  });
};

export const updateUserCompanyPermissions: AsyncController = async (req, res) => {
  const { userId, companyId } = req.params;
  const { role, allowedCities, allowedCrews } = req.body;

  const permissions = await CompanyPermissionsService.updateUserPermissions(userId, companyId, {
    role,
    allowedCities,
    allowedCrews,
  });

  return res.status(200).json({
    message: 'Permissions updated successfully',
    data: permissions,
  });
};

export const removeUserCompanyPermissions: AsyncController = async (req, res) => {
  const { userId, companyId } = req.params;

  await CompanyPermissionsService.removeUserPermissions(userId, companyId);

  return res.status(200).json({
    message: 'Permissions removed successfully',
  });
};
