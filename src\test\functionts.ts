import bcrypt from 'bcrypt';
import jwt, { SignOptions } from 'jsonwebtoken';
import User from '../models/userSchema';
import { accessTokenSecret, invitationSecret, recoverPasswordSecret } from '../constants';
import Associate from '../models/associateSchema';
import { Types } from 'mongoose';
import StockVehicle from '../models/StockVehicleSchema';
import RegionsPayments from '../models/regionPaymentsSchema';
import { AdmissionRequestMongo } from '@/models/admissionRequestSchema';
import { OCN_USER_EMAIL } from '@/constants/onboarding';
import OverHauling from '@/models/overHaulingSchema';
import ServiceStock from '@/models/serviceStockSchema';
import { Schedule } from '@/models/Schedules';
import { Slots } from '@/models/Slots';
import { Appointment, AppointmentStatus } from '@/models/appointment';

export const createAssociateTest = async () => {
  const newAssociateId = new Types.ObjectId();
  const associate = await Associate.create({
    _id: newAssociateId,
    email: '<EMAIL>',
    firstName: 'Prueba',
    lastName: 'OCN',
    birthDay: '121212',
    curp: '113445CURP',
    rfc: '00XXXXX',
    delegation: 'delegaTest',
    postalCode: '12345',
    phone: '1234567890',
    addressStreet: 'street',
    exterior: '12',
    interior: '12',
    colony: 'testColony',
    state: 'cdmx',
    city: 'cdmx',
  });

  return associate;
};

const password = 'mypassword';

export const createAgentTest = async () => {
  const hashedPassword = await bcrypt.hash(password, 12);
  const user = await User.create({
    role: 'agent',
    city: 'cdmx',
    email: '<EMAIL>',
    name: 'Usuario Prueba',
    password: hashedPassword,
    settings: {
      allowedRegions: [
        'cdmx',
        'gdl',
        'mty',
        'tij',
        'pbc',
        'pbe',
        'pue',
        'qro',
        'pbe',
        'tol',
        'ptv',
        'tep',
        'col',
        'sal',
        'torr',
        'dur',
        'mxli',
        'her',
        'chi',
        'leo',
        'ags',
        'slp',
        'mer',
      ],
    },
  });
  return user;
};

export const overHaulingTest = async (stockId: any) => {
  const overHaulingId = new Types.ObjectId();
  const overHauling = await OverHauling.create({
    _id: overHaulingId,
    stockId: stockId,
    dateIn: '2024-10-10',
  });

  return overHauling;
};

export const serviceStockTest = async (stockId: any, associateId: any) => {
  const serviceStockId = new Types.ObjectId();
  const newServiceStock = await ServiceStock.create({
    _id: serviceStockId,
    dateIn: '2024-10-10',
    dateOut: '2024-10-10',
    stockId: stockId,
    associateId: associateId,
  });

  return newServiceStock;
};

export const stockVehicleTest = async () => {
  const newStockId = new Types.ObjectId();
  const stockVehicle = await StockVehicle.create({
    _id: newStockId,
    model: 'Test model',
    carNumber: '1003',
    brand: 'brand test',
    color: 'purple',
    vin: '123456',
    vehicleState: 'cdmx',
    owner: 'OCN',
    billAmount: '123456',
    km: 12,
    region: '1',
    year: '2023',
    version: 'sedan',
  });

  return stockVehicle;
};

export const stockVehicleTestSureste = async () => {
  const newStockId = new Types.ObjectId();
  const stockVehicle = await StockVehicle.create({
    _id: newStockId,
    model: 'Test model',
    carNumber: '2005',
    brand: 'brand test',
    color: 'purple',
    vin: '123456',
    vehicleState: 'cdmx',
    owner: 'OCN',
    billAmount: '123456',
    km: 12,
    region: '2',
    year: '2023',
    version: 'sedan',
  });

  return stockVehicle;
};

export const createAdminTest = async () => {
  const hashedPassword = await bcrypt.hash(password, 12);
  const user = await User.create({
    email: '<EMAIL>',
    name: 'Admin Prueba',
    password: hashedPassword,
    role: 'superadmin',
    city: 'cdmx',
    settings: {
      allowedRegions: ['cdmx', 'gdl', 'mty', 'tij', 'pbc', 'pbe', 'qro', 'pue'],
    },
  });
  return user;
};

export const createAassociateTest = async () => {
  const hashedPassword = await bcrypt.hash(password, 12);
  const user = await User.create({
    email: '<EMAIL>',
    name: 'Agent Prueba',
    password: hashedPassword,
    role: 'agent',
    city: 'cdmx',
    settings: {
      allowedRegions: ['cdmx', 'gdl', 'mty', 'tij', 'pbc', 'pbe', 'qro', 'pue'],
    },
  });
  return user;
};

type SignTokenProps = (
  props: { [key: string]: any },
  nameSecret: 'accessToken' | 'invitationToken' | 'recoverToken',
  expiresIn?: SignOptions['expiresIn']
) => string;

export const signToken: SignTokenProps = (props, nameSecret, expiresIn) => {
  let nameToken: string | undefined = undefined;

  const tokens = [
    { key: 'accessToken', value: accessTokenSecret },
    { key: 'invitationToken', value: invitationSecret },
    { key: 'recoverToken', value: recoverPasswordSecret },
  ];

  for (let field of tokens) {
    if (nameSecret === field.key) {
      nameToken = field.value;
    }
  }

  if (nameToken === undefined) {
    throw new Error('Nombre de token no proporcionado');
  }

  const token = jwt.sign(props, nameToken, {
    expiresIn,
  });

  return token;
};

export const createRegionTest = async () => {
  const region = await RegionsPayments.create({
    region: 'TEST',
    models: {
      'Test model': {
        rentID: 'service_wLLzJcpFek',
        rentName: 'Renta Semanal',
        assistanceID: 'service_3GFgFi8e9P',
        assistanceName: 'Asistencia Test',
      },
    },
  });
  return region;
};

// EN DESARROLLO;

// type ConnectAndDisconectDBTestProps = (agent?: boolean, admin?: boolean, associate?: boolean) => void;

// export const connectAndDisconectDBTest: ConnectAndDisconectDBTestProps = (isAgent, isAdmin, isAssociate) => {
//   const variables: { [key: string]: any } = {};
//   beforeAll(async () => {
//     await connectTestDB();

//     const requireds = [
//       { key: isAgent, func: createAgentTest, let: 'agentId' },
//       { key: isAdmin, func: createAdminTest, let: 'userAdminId' },
//       { key: isAssociate, func: createAssociateTest, let: 'associateId' },
//     ];

//     for (const required of requireds) {
//       if (required.key) {
//         const result = await required.func();
//         variables[required.let] = result._id.toString();
//       }
//     }
//   });
//   afterAll(async () => {
//     await disconnectTestDB();
//     let server = app.listen(3030);
//     server.close();
//   });
//   return variables;
// };

export const createAdmissionRequestTest = async () => {
  const newAdmissionRequestId = new Types.ObjectId();
  const admissionRequest = await AdmissionRequestMongo.create({
    _id: newAdmissionRequestId,
    status: 'created',
    personalData: {
      status: 'pending',
      firstName: 'Test Admision Request',
      lastName: 'Test Admision Request',
      phone: '+************',
      email: '<EMAIL>',
      state: 'cdmx',
      city: 'cdmx',
    },
    palenca: {
      widgetId: '04d4f458-9991-4c44-9464-8786cb9e046e',
      externalId: '67738e4449f20af48711bdd9',
      accounts: [],
    },
    documentsAnalysis: {
      status: 'pending',
      documents: [
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'identity_card_front',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'identity_card_back',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'proof_of_address',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'bank_statement_month_1',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'bank_statement_month_2',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'bank_statement_month_3',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'proof_of_tax_situation',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'drivers_license_front',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'drivers_license_back',
        },
      ],
    },
    earningsAnalysis: { status: 'pending', earnings: [] },
    riskAnalysis: { status: 'pending', scorecardVersion: 'v1', scorecard: null },
  });

  return admissionRequest;
};

export const createOcnUserTest = async () => {
  const hashedPassword = await bcrypt.hash(password, 12);
  const user = await User.create({
    email: OCN_USER_EMAIL,
    name: 'OneCarNow!',
    password: hashedPassword,
    role: 'superadmin',
    city: 'cdmx',
    settings: {
      allowedRegions: ['cdmx', 'gdl', 'mty', 'tij', 'pbc', 'pbe', 'qro', 'pue'],
    },
  });
  return user;
};

export const createHomeVisitor = async ({ email, name }: any) => {
  const hashedPassword = await bcrypt.hash(password, 12);
  const user = await User.create({
    email: email,
    name: name,
    password: hashedPassword,
    role: 'superadmin',
    city: 'cdmx',
    settings: {
      allowedRegions: ['cdmx', 'gdl', 'mty', 'tij', 'pbc', 'pbe', 'qro', 'pue'],
    },
    homeVisitor: true,
    homeVisitorColor: '#ff7665',
  });
  return user;
};

export const createSchedule = async (homeVisitorId: string) => {
  const schedule = await Schedule.create({
    user: homeVisitorId,
    name: 'test',
    weeklySchedule: {
      monday: { start: '09:00', end: '18:00' },
      tuesday: { start: '09:00', end: '18:00' },
      wednesday: { start: '09:00', end: '18:00' },
      thursday: { start: '09:00', end: '18:00' },
      friday: { start: '09:00', end: '18:00' },
      saturday: { start: '09:00', end: '18:00' },
      sunday: { start: '09:00', end: '18:00' },
    },
    maxSimultaneousAppointments: 1,
    timezone: 'America/Mexico_City',
    breakTimes: [{ start: '13:00', end: '14:00' }],
    bufferTime: 0,
    duration: 30,
    maxAdvanceBookingDays: 5,
    minBookingNoticeHours: 4,
  });
  return schedule;
};

export const createSlot = async ({
  scheduleId,
  userId,
  startTime = '09:00',
  endTime = '09:30',
  isAvailable = true,
  timezone = 'America/Mexico_City',
  date = new Date(),
}: {
  scheduleId: string;
  userId: string;
  startTime?: string;
  endTime?: string;
  isAvailable?: boolean;
  timezone?: string;
  date?: Date;
}) => {
  const slot = await Slots.create({
    scheduleId: scheduleId,
    user: userId,
    date: date,
    startTime: startTime,
    endTime: endTime,
    isAvailable: isAvailable,
    maxAppointments: 1,
    currentAppointments: 0,
    timezone: timezone,
  });

  return slot;
};

export const createAppointment = async ({
  date,
  startTime,
  endTime,
  admissionRequestId,
  user,
  slot,
}: {
  date: Date;
  startTime: Date;
  endTime: Date;
  admissionRequestId: string;
  user: string;
  slot: string;
}) => {
  const appointment = await Appointment.create({
    title: 'Test Appointment',
    date: date,
    startTime: startTime,
    endTime: endTime,
    description: 'Test Appointment',
    status: AppointmentStatus.scheduled,
    duration: 30,
    admissionRequestId: admissionRequestId,
    user: user,
    slot: slot,
    meetingLink: 'https://meet.google.com/upp-eoik-ref',
  });
  return appointment;
};
