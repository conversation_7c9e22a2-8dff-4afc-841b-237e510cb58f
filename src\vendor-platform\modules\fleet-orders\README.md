# Fleet Orders Module 🚗

Módulo completo para la gestión de órdenes de flota vehicular con control de SLAs, evidencias y notificaciones automáticas.

## 📋 Características Principales

- ✅ **Gestión completa del ciclo de órdenes** - Desde creación hasta entrega
- ⏰ **Monitoreo automático de SLAs** - Alertas y notificaciones en tiempo real
- 📊 **Sistema de evidencias** - Upload y validación de documentos/fotos
- 🔔 **Integración con Slack** - Notificaciones automáticas y resúmenes
- 🔐 **Sistema de permisos granular** - Control de acceso por roles
- 📈 **Dashboard de estadísticas** - Métricas y reportes en tiempo real
- 🤖 **Automatización completa** - Cron jobs para monitoreo 24/7

## 🏗️ Arquitectura

```
fleet-orders/
├── controllers/          # Controladores de API
├── models/              # Modelos de base de datos
├── services/            # Lógica de negocio
├── routes/              # Definición de rutas
├── middlewares/         # Middlewares de validación
├── dtos/                # Objetos de transferencia de datos
├── utils/               # Utilidades y helpers
├── cron/                # Trabajos programados
├── config/              # Configuración del módulo
└── docs/                # Documentación
```

## 🔄 Estados de Orden

| Estado | Descripción | SLA | Evidencia Requerida |
|--------|-------------|-----|-------------------|
| `created` | Orden creada | Día 6 del mes | Log de creación |
| `sent` | Enviada a OEM | Día 22 del mes | Log de envío |
| `dispersion` | Dispersión registrada | Día 1 siguiente mes | Documento de dispersión |
| `invoice_letter_request` | Cartas solicitadas | Día 4 siguiente mes | Log de solicitud |
| `invoice_letter_arrival` | Cartas recibidas | 2 días | Foto/PDF de cartas |
| `supplier_notification` | Proveedores notificados | - | Log de notificación |
| `waiting_for_cars` | Esperando vehículos | Variable | - |
| `delivered` | Entregada | - | Log de entrega |

## 🚀 Instalación y Configuración

### 1. Variables de Entorno

```bash
# Slack Integration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
FLEET_ORDERS_SLACK_CHANNEL=#fleet-orders
FLEET_ORDERS_SLACK_ENABLED=true

# Vendor Platform Token
VENDOR_PLATFORM_ACCESS_TOKEN_SECRET=your_secret_here
```

### 2. Migración de Usuarios

```bash
# Migrar usuarios existentes al nuevo sistema de permisos
npm run migrate-permissions migrate

# Verificar estado de migración
npm run migrate-permissions status

# Revertir migración (si es necesario)
npm run migrate-permissions rollback
```

### 3. Inicialización

El módulo se inicializa automáticamente al arrancar el servidor. Los cron jobs se activan automáticamente.

## 📊 Monitoreo de SLA

### Alertas Automáticas

- **Advertencia**: 2 días antes del vencimiento
- **Crítica**: Cuando se excede el SLA
- **Notificaciones**: Slack automático + dashboard

### Cron Jobs

```javascript
// Verificación cada hora (horario laboral)
'0 8-18 * * 1-5' // 8 AM - 6 PM, Lun-Vie

// Resumen diario
'0 9 * * *' // 9:00 AM todos los días

// Alertas críticas
'*/30 * * * *' // Cada 30 minutos

// Limpieza
'0 2 * * 0' // 2:00 AM domingos
```

## 🔐 Sistema de Permisos

### Roles OCN (OneCarNow)
- `ocn_super_admin` - Acceso completo
- `ocn_admin` - Administración general
- `ocn_fleet_manager` - Gestión de flota
- `ocn_viewer` - Solo lectura

### Permisos por Módulo
```javascript
fleetOrders: {
  create: boolean,
  read: boolean,
  update: boolean,
  delete: boolean,
  manageAll: boolean,
  viewAllOrders: boolean,
  manageDispersion: boolean,
  uploadEvidence: boolean,
  manageSLAs: boolean
}
```

## 📱 Integración con Slack

### Tipos de Notificaciones

1. **Nueva Orden Creada**
   ```
   🚗 Nueva Orden de Flota Creada
   ✅ Orden FO-2024-03-001 creada exitosamente
   ```

2. **Alerta de SLA**
   ```
   ⚠️ ADVERTENCIA: SLA Fleet Order
   🚨 Orden FO-2024-03-001 - 2 días restantes
   ```

3. **Cambio de Estado**
   ```
   🔄 Cambio de Estado - Fleet Order
   📋 Orden FO-2024-03-001 - Estado Actualizado
   ```

4. **Resumen Diario**
   ```
   📊 Resumen Diario - Fleet Orders
   📈 Resumen del 15 de marzo, 2024
   ```

## 🛠️ Uso de la API

### Crear Orden
```javascript
POST /vendor-platform/fleet-orders
{
  "month": 3,
  "year": 2024,
  "vehicles": [
    {
      "brand": "BYD",
      "dealer": "BYD Mexico",
      "model": "Dolphin Mini",
      "version": "Comfort",
      "quantity": 50,
      "unitPrice": 350000
    }
  ],
  "notificationEmails": ["<EMAIL>"]
}
```

### Actualizar Estado
```javascript
PATCH /vendor-platform/fleet-orders/:id/status
{
  "status": "sent",
  "evidence": {
    "type": "log",
    "description": "Orden enviada por email"
  }
}
```

### Actualizar Dispersión
```javascript
PATCH /vendor-platform/fleet-orders/:id/dispersion
{
  "dispersion": [
    {
      "state": "CDMX",
      "city": "Ciudad de México",
      "quantity": 25,
      "deliveryDate": "2024-03-25T00:00:00.000Z",
      "amount": 8750000
    }
  ]
}
```

## 📈 Métricas y Estadísticas

### Dashboard de SLA
```javascript
GET /vendor-platform/fleet-orders/sla/statistics
{
  "totalActiveOrders": 5,
  "ordersOnTime": 3,
  "ordersWithWarnings": 1,
  "ordersExceeded": 1,
  "alertsByStatus": {
    "created": 2,
    "sent": 1
  }
}
```

### Resumen Diario
```javascript
GET /vendor-platform/fleet-orders/sla/daily-summary
{
  "date": "2024-03-15T00:00:00.000Z",
  "totalOrders": 5,
  "newWarnings": 1,
  "newExceeded": 0,
  "resolvedToday": 2,
  "criticalOrders": [...]
}
```

## 🧪 Testing

### Archivo HTTP
Usar `http/fleet-orders.http` para testing manual con VS Code REST Client.

### Casos de Prueba
- ✅ Creación de órdenes válidas
- ❌ Validación de datos inválidos
- ✅ Transiciones de estado válidas
- ❌ Transiciones de estado inválidas
- ✅ Actualización de dispersión
- ❌ Dispersión inconsistente
- ✅ Verificación de SLAs
- ✅ Notificaciones Slack

## 🔧 Mantenimiento

### Limpieza Automática
```javascript
// Elimina alertas resueltas > 30 días
DELETE /vendor-platform/fleet-orders/sla/cleanup
```

### Verificación Manual
```javascript
// Ejecutar verificación de SLA
POST /vendor-platform/fleet-orders/sla/check
```

### Logs
```bash
# Verificar logs de cron jobs
tail -f logs/fleet-orders.log

# Verificar logs de Slack
tail -f logs/slack-notifications.log
```

## 🚨 Troubleshooting

### Problemas Comunes

1. **Slack no funciona**
   - Verificar `SLACK_WEBHOOK_URL`
   - Verificar `FLEET_ORDERS_SLACK_ENABLED=true`

2. **SLAs no se verifican**
   - Verificar que los cron jobs estén activos
   - Revisar logs de cron

3. **Permisos insuficientes**
   - Verificar migración de usuarios
   - Verificar roles asignados

4. **Transiciones de estado inválidas**
   - Revisar flujo de estados permitidos
   - Verificar evidencia requerida

### Comandos de Diagnóstico

```bash
# Verificar estado de migración
npm run migrate-permissions status

# Verificar configuración
node -e "console.log(require('./src/vendor-platform/modules/fleet-orders/config/fleet-orders.config').validateFleetOrdersConfig())"

# Verificar conexión Slack
curl -X POST $SLACK_WEBHOOK_URL -d '{"text":"Test message"}'
```

## 📚 Documentación Adicional

- [API Documentation](./docs/API.md)
- [Configuración](./config/fleet-orders.config.ts)
- [Ejemplos HTTP](../../http/fleet-orders.http)

## 🤝 Contribución

1. Seguir la estructura de carpetas existente
2. Agregar tests para nuevas funcionalidades
3. Actualizar documentación
4. Verificar que los cron jobs funcionen correctamente
