import {
  MIN_EARNINGS_APPROVED,
  MIN_EARNINGS_APPROVED_WITH_CONDITIONS,
  MAX_EARNINGS_APPROVED_WITH_CONDITIONS,
  EarningsAnalysisStatus,
  MIN_WEEKS_TO_ANALYZE,
} from '../../constants';

export const determineEarningsAnalysisStatus = (totalEarnings: number): EarningsAnalysisStatus => {
  const totalEarningsRounded = Math.round(totalEarnings);
  const averange = totalEarningsRounded / MIN_WEEKS_TO_ANALYZE;
  if (averange >= MIN_EARNINGS_APPROVED) {
    return EarningsAnalysisStatus.approved;
  } else if (
    averange >= MIN_EARNINGS_APPROVED_WITH_CONDITIONS &&
    averange >= MAX_EARNINGS_APPROVED_WITH_CONDITIONS
  ) {
    return EarningsAnalysisStatus.approved_with_conditions;
  } else if (averange >= MIN_EARNINGS_APPROVED_WITH_CONDITIONS) {
    return EarningsAnalysisStatus.approved_with_conditions;
  } else {
    return EarningsAnalysisStatus.rejected;
  }
};

export const getWeeklyAverage = (totalEarnings: number): number => {
  const totalEarningsRounded = Math.round(totalEarnings);
  return totalEarningsRounded / MIN_WEEKS_TO_ANALYZE;
};
