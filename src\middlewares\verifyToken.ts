import { NextFunction, Response, Request } from 'express';
import jwt from 'jsonwebtoken';
import { blacklist } from '../conf/blackList';
import {
  accessTokenSecret,
  cronJobAccessTokenSecret,
  externalAccessTokenSecret,
  genericMessages,
} from '../constants';
import { logger } from '../clean/lib/logger';

interface MyRequest extends Request {
  [key: string]: any;
}

type Controller = (
  req: MyRequest,
  res: Response,
  next: NextFunction
) => Promise<Response<any, Record<string, any>>> | Promise<any>;

export const verifyToken: Controller = async (req, res, next) => {
  const authorization = req.headers.authorization;

  if (!authorization) {
    logger.error({
      message: '[verifyTokenMiddleware] Authorization Not Found in Headers',
    });
    return res.status(401).send({ message: 'No autorizado' });
  }

  const token = authorization.split(' ')[1];
  if (!token) {
    logger.error({
      message: '[verifyTokenMiddleware] Token Not Found in Headers',
    });
    return res.status(401).send({ message: 'No autorizado' });
  }

  if (blacklist[token]) {
    logger.error({
      message: '[verifyTokenMiddleware] Token is being black listed.',
    });
    return res.status(403).send({ message: 'Este token se ha invalidado' });
  }

  try {
    const result = jwt.verify(token, accessTokenSecret);
    req.userReq = result as Request['userReq'];
    req.userId = result;
    return next();
  } catch (error: any) {
    logger.error({
      message: '[verifyTokenMiddleware] Token is being black listed.',
      stack: error?.stack,
    });
    return res.status(400).send({ message: error.message });
  }
};
export const verifyExternalToken: Controller = async (req, res, next) => {
  const authorization = req.headers.authorization;
  if (!authorization) return res.status(401).send({ message: genericMessages.errors.unauthorized });
  const token = authorization.split(' ')[1];
  if (!token) return res.status(401).send({ message: genericMessages.errors.unauthorized });
  if (blacklist[token]) return res.status(403).send({ message: genericMessages.errors.tokens.invalidToken });

  try {
    const result = jwt.verify(token, externalAccessTokenSecret) as jwt.JwtPayload;
    req.userId = result;
    return next();
  } catch (error: any) {
    return res.status(400).send({ message: error.message });
  }
};

export const verifyCronJobToken: Controller = async (req, res, next) => {
  const authorization = req.headers.authorization;
  console.log('authorization', authorization);
  if (!authorization) return res.status(401).send({ message: 'No autorizado' });

  const token = authorization.split(' ')[1];
  if (!token) return res.status(401).send({ message: 'No autorizado' });

  if (blacklist[token]) return res.status(403).send({ message: 'Este token se ha invalidado' });

  try {
    const result = jwt.verify(token, cronJobAccessTokenSecret);
    req.userReq = result as Request['userReq'];
    req.userId = result;
    return next();
  } catch (error: any) {
    console.log('error', error);
    return res.status(400).send({ message: error.message });
  }
};
