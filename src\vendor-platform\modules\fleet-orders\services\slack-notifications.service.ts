import axios from 'axios';
import { IFleetOrderSLAAlert, SLAAlertType } from '../models/fleet-order-sla-alerts.model';
import { IFleetOrder, FleetOrderStatus } from '../models/fleet-order.model';
import { formatDeadlineDate, getStatusDisplayName } from '../utils/sla-calculator';

export class SlackNotificationsService {
  private webhookUrl: string;

  constructor() {
    this.webhookUrl = process.env.SLACK_WEBHOOK_URL || '';
  }

  /**
   * Enviar notificación de alerta de SLA a Slack
   */
  async sendSLAAlert(alert: IFleetOrderSLAAlert & { order: any }): Promise<string | null> {
    if (!this.webhookUrl) {
      console.warn('SLACK_WEBHOOK_URL not configured');
      return null;
    }

    const message = this.buildSLAAlertMessage(alert);
    
    try {
      const response = await axios.post(this.webhookUrl, message);
      return response.data?.ts || 'sent';
    } catch (error) {
      console.error('Error sending Slack notification:', error);
      throw error;
    }
  }

  /**
   * Enviar notificación de nueva orden creada
   */
  async sendOrderCreatedNotification(order: IFleetOrder): Promise<string | null> {
    if (!this.webhookUrl) {
      console.warn('SLACK_WEBHOOK_URL not configured');
      return null;
    }

    const message = this.buildOrderCreatedMessage(order);
    
    try {
      const response = await axios.post(this.webhookUrl, message);
      return response.data?.ts || 'sent';
    } catch (error) {
      console.error('Error sending Slack notification:', error);
      throw error;
    }
  }

  /**
   * Enviar notificación de cambio de estado
   */
  async sendStatusChangeNotification(
    order: IFleetOrder, 
    oldStatus: FleetOrderStatus, 
    newStatus: FleetOrderStatus
  ): Promise<string | null> {
    if (!this.webhookUrl) {
      console.warn('SLACK_WEBHOOK_URL not configured');
      return null;
    }

    const message = this.buildStatusChangeMessage(order, oldStatus, newStatus);
    
    try {
      const response = await axios.post(this.webhookUrl, message);
      return response.data?.ts || 'sent';
    } catch (error) {
      console.error('Error sending Slack notification:', error);
      throw error;
    }
  }

  /**
   * Enviar resumen diario
   */
  async sendDailySummary(summary: {
    date: Date;
    totalOrders: number;
    newWarnings: number;
    newExceeded: number;
    resolvedToday: number;
    criticalOrders: any[];
  }): Promise<string | null> {
    if (!this.webhookUrl) {
      console.warn('SLACK_WEBHOOK_URL not configured');
      return null;
    }

    const message = this.buildDailySummaryMessage(summary);
    
    try {
      const response = await axios.post(this.webhookUrl, message);
      return response.data?.ts || 'sent';
    } catch (error) {
      console.error('Error sending Slack notification:', error);
      throw error;
    }
  }

  /**
   * Construir mensaje de alerta de SLA
   */
  private buildSLAAlertMessage(alert: IFleetOrderSLAAlert & { order: any }) {
    const isWarning = alert.alertType === SLAAlertType.SLA_WARNING;
    const emoji = isWarning ? '⚠️' : '🚨';
    const urgency = isWarning ? 'ADVERTENCIA' : 'CRÍTICO';
    const color = isWarning ? '#ff9500' : '#ff0000';

    const statusName = getStatusDisplayName(alert.status);
    const deadlineFormatted = formatDeadlineDate(alert.deadline);

    return {
      text: `${emoji} ${urgency}: SLA Fleet Order`,
      attachments: [
        {
          color: color,
          title: `${emoji} Alerta de SLA - Orden ${alert.orderNumber}`,
          fields: [
            {
              title: 'Orden',
              value: alert.orderNumber,
              short: true,
            },
            {
              title: 'Estado Actual',
              value: statusName,
              short: true,
            },
            {
              title: 'Fecha Límite',
              value: deadlineFormatted,
              short: true,
            },
            {
              title: 'Días Restantes',
              value: alert.daysRemaining > 0 ? `${alert.daysRemaining} días` : 'VENCIDO',
              short: true,
            },
            {
              title: 'Total Unidades',
              value: alert.order?.totalUnits?.toString() || 'N/A',
              short: true,
            },
            {
              title: 'Monto Total',
              value: alert.order?.totalAmount ? `$${alert.order.totalAmount.toLocaleString()}` : 'N/A',
              short: true,
            },
          ],
          footer: 'Fleet Orders System',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };
  }

  /**
   * Construir mensaje de orden creada
   */
  private buildOrderCreatedMessage(order: IFleetOrder) {
    return {
      text: '🚗 Nueva Orden de Flota Creada',
      attachments: [
        {
          color: '#36a64f',
          title: `✅ Orden ${order.orderNumber} creada exitosamente`,
          fields: [
            {
              title: 'Período',
              value: `${order.month}/${order.year}`,
              short: true,
            },
            {
              title: 'Total Unidades',
              value: order.totalUnits.toString(),
              short: true,
            },
            {
              title: 'Monto Total',
              value: `$${order.totalAmount.toLocaleString()}`,
              short: true,
            },
            {
              title: 'Fecha Límite Envío',
              value: formatDeadlineDate(order.sentDeadline),
              short: true,
            },
            {
              title: 'Vehículos',
              value: order.vehicles.map(v => `${v.brand} ${v.model} (${v.quantity})`).join('\n'),
              short: false,
            },
          ],
          footer: 'Fleet Orders System',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };
  }

  /**
   * Construir mensaje de cambio de estado
   */
  private buildStatusChangeMessage(
    order: IFleetOrder, 
    oldStatus: FleetOrderStatus, 
    newStatus: FleetOrderStatus
  ) {
    const oldStatusName = getStatusDisplayName(oldStatus);
    const newStatusName = getStatusDisplayName(newStatus);

    return {
      text: '🔄 Cambio de Estado - Fleet Order',
      attachments: [
        {
          color: '#0099cc',
          title: `📋 Orden ${order.orderNumber} - Estado Actualizado`,
          fields: [
            {
              title: 'Estado Anterior',
              value: oldStatusName,
              short: true,
            },
            {
              title: 'Nuevo Estado',
              value: newStatusName,
              short: true,
            },
            {
              title: 'Período',
              value: `${order.month}/${order.year}`,
              short: true,
            },
            {
              title: 'Total Unidades',
              value: order.totalUnits.toString(),
              short: true,
            },
          ],
          footer: 'Fleet Orders System',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };
  }

  /**
   * Construir mensaje de resumen diario
   */
  private buildDailySummaryMessage(summary: {
    date: Date;
    totalOrders: number;
    newWarnings: number;
    newExceeded: number;
    resolvedToday: number;
    criticalOrders: any[];
  }) {
    const dateFormatted = summary.date.toLocaleDateString('es-MX', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    const criticalOrdersList = summary.criticalOrders.length > 0
      ? summary.criticalOrders.map(alert => `• ${alert.orderNumber} (${getStatusDisplayName(alert.order.status)})`).join('\n')
      : 'Ninguna orden crítica';

    return {
      text: '📊 Resumen Diario - Fleet Orders',
      attachments: [
        {
          color: '#36a64f',
          title: `📈 Resumen del ${dateFormatted}`,
          fields: [
            {
              title: 'Órdenes Activas',
              value: summary.totalOrders.toString(),
              short: true,
            },
            {
              title: 'Nuevas Advertencias',
              value: summary.newWarnings.toString(),
              short: true,
            },
            {
              title: 'Nuevas Críticas',
              value: summary.newExceeded.toString(),
              short: true,
            },
            {
              title: 'Resueltas Hoy',
              value: summary.resolvedToday.toString(),
              short: true,
            },
            {
              title: 'Órdenes Críticas',
              value: criticalOrdersList,
              short: false,
            },
          ],
          footer: 'Fleet Orders System - Resumen Diario',
          ts: Math.floor(Date.now() / 1000),
        },
      ],
    };
  }

  /**
   * Verificar si Slack está configurado
   */
  isConfigured(): boolean {
    return !!this.webhookUrl;
  }
}
