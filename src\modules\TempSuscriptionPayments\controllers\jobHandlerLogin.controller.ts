import { AsyncController } from '../../../types&interfaces/types';
import jwt from 'jsonwebtoken';
import { cronJobAccessTokenSecret, cronPassword } from '../../../constants';

type TimeUnit = 's' | 'm' | 'h' | 'd';

export const jobHandlerLogin: AsyncController = async (req, res) => {
  const { key } = req.body;

  if (!key) return res.status(400).send({ message: 'Error' });

  if (key !== cronPassword) return res.status(401).send({ message: 'Error' });

  const expirationNumber = 10;
  const expirationString: TimeUnit = process.env.TEST_DEV ? 'h' : 'm';

  const jobHandlerToken = jwt.sign({}, cronJobAccessTokenSecret, {
    expiresIn: `${expirationNumber}${expirationString}`,
  });

  return res.status(200).send({ message: 'Ok', jobHandlerToken });
};
