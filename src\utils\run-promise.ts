// Create a function that receives a callback with generic types, that returns [error, data]
// abstract the error handling in a try catch block

export async function runPromise<T>(
  callback: () => Promise<T> | T,
  ...args: any[]
): Promise<{ error: null; data: T } | { error: Record<string, any>; data: null }> {
  try {
    const result = await callback();
    return { error: null, data: result };
  } catch (error: any) {
    console.error(`Error in function ${args[0]}:`, error?.response?.data || error.message);
    return { error, data: null };
  }
}
