import { PAYMENTS_API_URL, PAYMENTS_URL } from '@/constants/payments-api';
import { genericMessages } from '../../../constants';
import AssociatePayments from '../../../models/associatePayments';
import { AsyncController } from '../../../types&interfaces/types';
import axios from 'axios';
import Associate from '@/models/associateSchema';
import { logger } from '@/clean/lib/logger';
import { setEvents } from '@/services/gpsActions';
import StockVehicle from '@/models/StockVehicleSchema';

export const getAssociatePaymentById: AsyncController = async (req, res) => {
  try {
    const { associatePaymentId } = req.params;
    const associatePayment = await AssociatePayments.findById(associatePaymentId);
    if (!associatePayment) return res.status(404).send({ message: 'Pagos de asociado no encontrados' });
    return res.status(200).send({ associatePayment });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

export const getAssociatePaymentByVehicleId: AsyncController = async (req, res) => {
  try {
    const { vehicleId } = req.params;
    if (!vehicleId) return res.status(404).send({ message: 'ID requerido' });
    let query: any = {
      vehiclesId: vehicleId,
    };
    Object.keys(req.query).forEach((key) => {
      query[key] = req.query[key];
    });
    // console.log(query);
    const associatePayment = await AssociatePayments.findOne(query);
    // console.log('associatePayment', associatePayment);
    if (!associatePayment) return res.status(404).send({ message: 'Pagos de asociado no encontrados' });
    return res.status(200).send({ associatePayment });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

export const reviewReactivationPayment: AsyncController = async (req, res) => {
  const { plates } = req.body;
  if (!plates) return res.status(400).send({ message: 'ID requerido' });

  try {
    const findAssociateByPlate = await StockVehicle.findOne({ 'carPlates.plates': plates });
    if (!findAssociateByPlate) return res.status(404).send({ message: 'Asociado no encontrado' });

    const associate = await Associate.findOne(
      { vehiclesId: findAssociateByPlate._id, active: true },
      {
        sort: {
          createdAt: 1,
        },
      }
    );
    if (!associate) return res.status(404).send({ message: 'Asociado no encontrado' });

    const { data } = await axios.get(`${PAYMENTS_API_URL}/payments/associateId/${associate._id}`, {
      headers: {
        Authorization: `Bearer ${process.env.PAYMENTS_API_KEY}`,
      },
    });

    if (!data.data) {
      return res.status(404).send({ message: 'No se encontraron pagos para este usuario' });
    }

    const paymentsData = data.data;

    interface PaymentProduct {
      name: string;
    }

    interface Payment {
      id: string;
      products: PaymentProduct[];
      createdAt: string;
      isPaid: boolean;
    }

    const getLastPaymentIfUnpaid = (payments: Payment[], productName: string): Payment | null => {
      // Filtrar pagos que contienen el producto especificado
      const paymentsWithProduct = payments.filter((payment) =>
        payment.products.some((product) => product.name.includes(productName))
      );

      // Ordenar los pagos por fecha de creación (el más reciente primero)
      const sortedPayments = paymentsWithProduct.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Obtener el último pago (el más reciente)
      const lastPayment = sortedPayments[0];

      // Si no hay pagos o el último pago no está pagado, devolver el último pago
      if (!lastPayment || !lastPayment.isPaid) {
        return lastPayment || null; // Si no hay pagos, devuelve null
      }

      // Devolver null si el último pago está pagado
      return null;
    };
    // Devuelve `false` porque el último pago de "Product A" está pagado.
    const lastRentingUnpaid = getLastPaymentIfUnpaid(paymentsData, 'Renting');
    const lastReactivationUnpaid = getLastPaymentIfUnpaid(paymentsData, 'Cargo por reactivación');

    if (!lastRentingUnpaid && !lastReactivationUnpaid) {
      // Buscar el vehículo asociado al conductor
      const gpsData = await StockVehicle.findOne({ 'drivers._id': associate._id });

      // Verificar si el vehículo existe y tiene un número de GPS
      if (!gpsData || !gpsData.gpsNumber) {
        return res.status(404).send({ message: 'No se encontró el vehículo asociado para desbloqueo' });
      }

      // Realizar la acción de desbloqueo
      try {
        const result = await setEvents(gpsData.gpsNumber, 'unlock');
        return res.status(200).send({ message: 'Desbloqueo exitoso', result });
      } catch (error) {
        // Manejar errores en la ejecución de setEvents
        return res.status(500).send({ message: 'Error al intentar desbloquear el vehículo', error });
      }
    }

    // Si hay pagos pendientes, devolver el pago pendiente
    const pendingPayment = [];
    if (lastRentingUnpaid)
      pendingPayment.push({
        id: lastRentingUnpaid?.id,
        url: `${PAYMENTS_URL}/pago/${lastRentingUnpaid?.id}`,
        type: 'Renting',
      });

    if (lastReactivationUnpaid)
      pendingPayment.push({
        id: lastReactivationUnpaid?.id,
        url: `${PAYMENTS_URL}/pago/${lastReactivationUnpaid?.id}`,
        type: 'Cargo por reactivación',
      });

    return res.status(402).send({
      message: 'Tiene pagos pendientes',
      payment: pendingPayment,
    });
  } catch (error: any) {
    console.log('error', error);
    logger.info(`[reviewReactivationPayment] Error: ${error}`);
    if (error.response?.status === 404) {
      return res.status(404).send({ message: 'No se encontraron pagos para este usuario' });
    }

    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};
