import { Router } from 'express';
import {
  getAdmissionRequests,
  blockLeadsAssignation,
  getBlockLeadAssignation,
  deleteBlockLeadAssignment,
  updateBlockLeadAssignation,
  getBlockLeadAssignationAuditLogs,
  reassignAdmissionRequest,
  getAvailableAgents,
  getAgents,
  getReassignAuditLogs,
} from '../controllers/leadAssignment';

const leadAssignment = Router();

leadAssignment.get('/search', getAdmissionRequests);
leadAssignment.post('/block-leads', blockLeadsAssignation);
leadAssignment.get('/block-leads-list', getBlockLeadAssignation);
leadAssignment.delete('/block-leads/:id/:userId', deleteBlockLeadAssignment);
leadAssignment.put('/block-leads/:id', updateBlockLeadAssignation);
leadAssignment.get('/block-lead/audit-logs', getBlockLeadAssignationAuditLogs);
leadAssignment.post('/reassign-lead', reassignAdmissionRequest);
leadAssignment.get('/available-agents', getAvailableAgents);
leadAssignment.get('/agents', getAgents);
leadAssignment.get('/reassignmentAuditLogs', getReassignAuditLogs);

export default leadAssignment;
