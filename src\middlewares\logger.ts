import { NextFunction, Request, Response } from 'express';
import { logger } from '../clean/lib/logger';

const getStatusColor = (statusCode: number): string => {
  if (statusCode >= 200 && statusCode < 399) {
    return '\x1b[32m'; // Green for success
  } else if (statusCode >= 400 && statusCode < 500) {
    return '\x1b[33m'; // Yellow for client errors
  } else {
    return '\x1b[31m'; // Red for other errors
  }
};

export const logRequestAndResponseMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = new Date().getTime();
  const requestDetails = `Request: ${req.method} ${req.url}`;

  const logResponse = () => {
    const duration = new Date().getTime() - startTime;
    const statusColor = getStatusColor(res.statusCode);
    logger.info(`${statusColor}${requestDetails} - Response: ${res.statusCode} - ${duration}ms\x1b[0m`);
  };

  res.on('finish', logResponse);

  next();
};
