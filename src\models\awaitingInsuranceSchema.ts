import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const awaitingInsuranceSchema = new Schema({
  date: {
    type: String,
    required: true,
  },

  dateFinished: {
    type: String,
  },

  description: String,

  comments: String,

  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
  },

  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
  },

  isCanceled: {
    type: Boolean,
    default: false,
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

const AwaitingInsuranceStock = model('awaiting-insurance', awaitingInsuranceSchema);

export default AwaitingInsuranceStock;
