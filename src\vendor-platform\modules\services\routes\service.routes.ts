import { errorHandlerV2 } from './../../../../clean/errors/errorHandler';
import { Router } from 'express';
import {
  completeService,
  createService,
  getServicesAndVendorDetailsByAssociateId,
  getServicesByAssociateId,
} from '../controllers/services.controller';
import { upload } from '@/multer/multer';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';

const servicesRouter = Router();

const servicesUrl = '/service';

servicesRouter.get(
  `${servicesUrl}/associate/:associateId`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getServicesByAssociateId)
);

servicesRouter.post(servicesUrl, upload.any(), verifyTokenVendorPlatform, errorHandlerV2(createService));
servicesRouter.patch(
  `${servicesUrl}/:serviceId/complete`,
  verifyTokenVendorPlatform,
  upload.any(),
  errorHandlerV2(completeService)
);
servicesRouter.get(
  `${servicesUrl}/servicesAndOrganization/:associateId/details`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getServicesAndVendorDetailsByAssociateId)
);

export default servicesRouter;
