# @baseUrl = https://dev-api.onecarnow.com
@baseUrl = http://localhost:3000
@authToken =
@vendorAuthToken = 

@organizationId = 676af5a9d0faf6880faf7b39
@userId = 67e702b79d547db84ea03a09
@workshopId = 6760a0454e836d1cb33a93b0
@serviceTypeId = 67a5843a918168848da39a98
@serviceId = 67e6f1a6e03dc33e296536ec
@appointmentId = 67f41be5c4703b9aa9e03a76

### ==================== AUTHENTICATION ====================

### Admin Platform Login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "your_password"
}

### Vendor Platform Login
POST {{baseUrl}}/vendor-platform/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "your_password"
}

### ==================== ORGANIZATIONS ====================

### Basic organization operations
### Create organization
POST {{baseUrl}}/vendor-platform/organizations
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Example Auto Shop",
    "contactInfo": {
        "email": "<EMAIL>",
        "phone": "5512345678"
    }
}

### Get all organizations
GET {{baseUrl}}/vendor-platform/organizations
Authorization: Bearer {{vendorAuthToken}}

### Get organization by ID
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}
Authorization: Bearer {{vendorAuthToken}}

### Organization schedules
### Get organization schedule
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/schedule
Authorization: Bearer {{vendorAuthToken}}

### Update organization schedule
PUT {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/schedule
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
     "weeklySchedule": {
        "monday": {
            "start": "09:00",
            "end": "18:00"
        },
        "tuesday": {
            "start": "09:00",
            "end": "18:00"
        },
        "wednesday": {
            "start": "09:00",
            "end": "18:00"
        },
        "thursday": {
            "start": "09:00",
            "end": "18:00"
        },
        "friday": {
            "start": "09:00",
            "end": "18:00"
        },
        "saturday": {
            "start": "09:00",
            "end": "14:00"
        },
    },
    "capacity": {
        "totalBays": 3,
        "techniciansPerBay": 2
    },
    "timezone": "America/Mexico_City",
    "breakTime": {
        "start": "12:00",
        "end": "13:00"
    }
}

### Organization Schedule Overrides
### Create schedule override for organization
POST {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "date": "2024-03-25",
    "type": "closed",
    "reason": "Holiday"
}

### Get organization schedule overrides
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}

### Organization Workshops
### Get organization workshops
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/workshops
Authorization: Bearer {{vendorAuthToken}}

### Organization Service Types
### Create service type for organization
POST {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/service-types
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Basic Maintenance",
    "description": "Basic maintenance service",
    "duration": 120
}

### Get organization service types
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/service-types
Authorization: Bearer {{vendorAuthToken}}

### Organization Services
### Get organization services
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/services
Authorization: Bearer {{vendorAuthToken}}

### Organization Appointments
### Get organization appointments
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/appointments?startDate=2025-03-20&endDate=2025-03-22
Authorization: Bearer {{vendorAuthToken}}

### ==================== WORKSHOPS ====================

### Basic workshop operations
### Create workshop
POST {{baseUrl}}/vendor-platform/workshops
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "North Branch",
    "organizationId": "{{organizationId}}",
    "address": {
        "street": "Main Street 123"
    },
    "color": "#000000"
}

### Get all workshops
GET {{baseUrl}}/vendor-platform/workshops
Authorization: Bearer {{vendorAuthToken}}

### Get workshop by ID
GET {{baseUrl}}/vendor-platform/workshops/{{workshopId}}
Authorization: Bearer {{vendorAuthToken}}

### Update workshop
PATCH {{baseUrl}}/vendor-platform/workshops/{{workshopId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "North Branch Updated"
}

### Workshop availability
### Get available slots for workshop
GET {{baseUrl}}/vendor-platform/workshops/{{workshopId}}/available-slots/2025-05-09/{{serviceTypeId}}
Authorization: Bearer {{vendorAuthToken}}

### Get available slots (public route)
GET {{baseUrl}}/vendor-platform/public/workshops/{{workshopId}}/available-slots/2025-05-09/{{serviceTypeId}}

### Workshop Schedule Overrides
### Create schedule override for workshop
POST {{baseUrl}}/vendor-platform/workshops/{{workshopId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "date": "2024-03-26",
    "type": "modified",
    "schedule": {
        "start": "10:00",
        "end": "15:00"
    },
    "reason": "Special schedule"
}

### Get workshop schedule overrides
GET {{baseUrl}}/vendor-platform/workshops/{{workshopId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}

### Workshop Appointments
### Create workshop appointment
POST {{baseUrl}}/vendor-platform/workshops/{{workshopId}}/appointments
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "serviceTypeId": "{{serviceTypeId}}",
    "startTime": "2025-05-09T09:30:00.000-06:00",
    "associateId": "67ab59dea4a734d375f0da9a",
    "stockId": "6513183dfbeb9ab9e3a62f48",
    "km": 44244
}

### Create workshop appointment (public route)
POST {{baseUrl}}/vendor-platform/public/workshops/{{workshopId}}/appointments
Content-Type: application/json

{
    "serviceTypeId": "{{serviceTypeId}}",
    "startTime": "2025-05-09T09:30:00.000-06:00",
    "associateId": "67ab59dea4a734d375f0da9a",
    "stockId": "6513183dfbeb9ab9e3a62f48",
    "km": 44244
}


### Get workshop appointments
GET {{baseUrl}}/vendor-platform/workshops/{{workshopId}}/appointments
Authorization: Bearer {{vendorAuthToken}}

### Get appointment info by plates and km (public route)
GET {{baseUrl}}/vendor-platform/public/appointment-info?plates=3513FED&km=49244

### ==================== SERVICE TYPES ====================

### Basic service type operations
### Get service type by ID
GET {{baseUrl}}/vendor-platform/service-types/{{serviceTypeId}}
Authorization: Bearer {{vendorAuthToken}}

### Update service type
PUT {{baseUrl}}/vendor-platform/service-types/{{serviceTypeId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Premium Maintenance"
}

### ==================== SERVICES ====================

### Basic service operations, workshop actions/operations
### Create service
POST {{baseUrl}}/vendor-platform/services
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "organizationId": "{{organizationId}}",
    "serviceTypeId": "{{serviceTypeId}}"
}

### Get all services
GET {{baseUrl}}/vendor-platform/services
Authorization: Bearer {{vendorAuthToken}}

### Get service by ID
GET {{baseUrl}}/vendor-platform/services/{{serviceId}}
Authorization: Bearer {{vendorAuthToken}}

### Get services by associate ID
GET {{baseUrl}}/vendor-platform/service/associate/64a8705709931026253a10cf
Authorization: Bearer {{authToken}}
adpt: true 

### ==================== APPOINTMENTS ====================

### Basic appointment operations
### Get appointment details
GET {{baseUrl}}/vendor-platform/appointments/{{appointmentId}}
Authorization: Bearer {{vendorAuthToken}}

### Update appointment status
PATCH {{baseUrl}}/vendor-platform/appointments/{{appointmentId}}/status
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "status": "completed",
    "notes": "Service completed successfully"
}

### Cancel appointment
DELETE {{baseUrl}}/vendor-platform/appointments/{{appointmentId}}
Authorization: Bearer {{vendorAuthToken}}

### ==================== SCHEDULE OVERRIDES ====================

### Create schedule override for organization
POST {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "date": "2024-03-25",
    "type": "closed",
    "reason": "Holiday"
}

### Get organization schedule overrides
GET {{baseUrl}}/vendor-platform/organizations/{{organizationId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}

### Create schedule override for workshop
POST {{baseUrl}}/vendor-platform/workshops/{{workshopId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "date": "2024-03-26",
    "type": "modified",
    "schedule": {
        "start": "10:00",
        "end": "15:00"
    },
    "reason": "Special schedule"
}

### Get workshop schedule overrides
GET {{baseUrl}}/vendor-platform/workshops/{{workshopId}}/schedule-overrides
Authorization: Bearer {{vendorAuthToken}}

