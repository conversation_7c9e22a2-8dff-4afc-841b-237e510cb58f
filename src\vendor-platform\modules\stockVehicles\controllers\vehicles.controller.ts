import { AsyncController } from '@/types&interfaces/types';
import { stockVehicleService } from '../services/vehicles.service';

export const getVehiclesVendor: AsyncController = async (req, res) => {
  const { q } = req.query;

  if (!q) {
    return res.status(400).send({ error: 'Missing query parameter' });
  }

  try {
    const vehicles = await stockVehicleService.getByQuery(q as string, req.userVendor.organizationId);

    return res.status(200).send({
      message: 'Vehicles found',
      data: vehicles,
    });
  } catch (error) {
    console.error(error);

    return res.status(500).send({ error: 'Internal server error' });
  }
};

export const getVehicleDataById: AsyncController = async (req, res) => {
  const { stockId } = req.params;

  if (!stockId) {
    return res.status(400).send({ error: 'Missing query parameter' });
  }

  try {
    const vehicle = await stockVehicleService.getById(stockId);

    return res.status(200).send({
      message: 'Vehicle found',
      data: vehicle,
    });
  } catch (error) {
    console.error(error);

    return res.status(500).send({ error: 'Internal server error' });
  }
};
