import { Types } from 'mongoose';
import type { Async<PERSON>ontroller, Controller } from '../types&interfaces/types';
import { stockVehiclesText, genericMessages, VEHICLES_KEY } from '../constants';
import Vehicle from '../models/vehicleSchema';
import Document from '../models/documentSchema';
import { uploadFile } from '../aws/s3';
import { removeEmptySpacesNameFile, removeEmptySpacesName } from '../services/removeEmptySpaces';
import { replaceDocWithUrl } from '../services/getPropertyWithUrls';
import { getLastAssociateOfActiveVehicles } from '../changes';

export const keyMiddleware: Controller = (req, res, next) => {
  const key = req.headers['vehicles-key'];
  try {
    if (!key || key !== VEHICLES_KEY) return res.send({ message: 'Not authorized' });
    return next();
  } catch (error: any) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

export const addCar: AsyncController = async (req, res) => {
  const {
    name,
    description,
    exterior,
    interior,
    payment,
    plan,
    seatsNumber,
    securityDetails,
    specialDetails,
    transmission,
    aditionalData,
    liters,
    paymentOptions,
    model,
  } = req.body;
  const images = req.files as { [fieldname: string]: Express.Multer.File[] };

  if (
    !name ||
    !description ||
    !exterior ||
    !interior ||
    !payment ||
    !plan ||
    !seatsNumber ||
    !securityDetails ||
    !specialDetails ||
    !transmission ||
    !aditionalData ||
    !liters ||
    !images ||
    !model
  )
    return res.status(400).send({ message: genericMessages.errors.missingBody });

  if (plan === 'Personal' && !paymentOptions)
    return res
      .status(400)
      .send({ message: genericMessages.errors.missingBody, error: 'Missing payment options' });

  try {
    const allphotos = [];
    const vehicleId = new Types.ObjectId();
    // const files = Object.values(images).flat();
    const orderFiles = Object.values(images).flat().reverse();

    for (const file of orderFiles) {
      const removeSpacesFileName = removeEmptySpacesNameFile(file);
      const removeSpacesName = removeEmptySpacesName(name);
      const documentPath = `vehicle/${removeSpacesName}/photos/${removeSpacesFileName}`;
      const s3Path = `vehicle/${removeSpacesName}/photos/`;
      const document = new Document({
        originalName: removeSpacesFileName,
        path: documentPath,
        vehicleId,
      });
      const documentSaved = await document.save();
      await uploadFile(file, removeSpacesFileName, s3Path);
      allphotos.push(documentSaved._id);
    }

    const newVehicle = new Vehicle({
      _id: vehicleId,
      name,
      description,
      exterior,
      interior,
      payment,
      plan,
      seatsNumber,
      securityDetails,
      specialDetails,
      transmission,
      aditionalData,
      liters,
      model,
      paymentOptions: paymentOptions ? JSON.parse(paymentOptions) : null,
      images: allphotos,
    });
    const vehicle = await newVehicle.save();
    return res.status(201).send({ message: stockVehiclesText.success.vehicleAdded, vehicle });
  } catch (error) {
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const getCatalogVehicles: AsyncController = async (req, res) => {
  try {
    const getAllVehicles = await Vehicle.find().lean();
    const vehicles = [];
    for (const vehicle of getAllVehicles) {
      const images = [];
      for (const image of vehicle.images) {
        const imageInfo = await replaceDocWithUrl(image.toString());
        images.push(imageInfo);
      }
      const result = {
        id: vehicle._id,
        name: vehicle.name,
        description: vehicle.description,
        exterior: vehicle.exterior,
        interior: vehicle.interior,
        payment: vehicle.payment,
        plan: vehicle.plan,
        seatsNumber: vehicle.seatsNumber,
        securityDetails: vehicle.securityDetails,
        specialDetails: vehicle.specialDetails,
        liters: vehicle.liters,
        transmission: vehicle.transmission,
        aditionalData: vehicle.aditionalData,
        images,
      };
      vehicles.push(result);
    }
    return res.status(200).send({ vehicles: vehicles });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const getCatalogVehiclesById: AsyncController = async (req, res) => {
  const { id } = req.params;
  const { plan } = req.query;
  try {
    const vehicle = await Vehicle.findOne({ _id: id, plan: plan }).lean();
    if (!vehicle) {
      return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
    }
    const images = [];
    for (const image of vehicle.images) {
      const imageInfo = await replaceDocWithUrl(image.toString());
      images.push(imageInfo);
    }
    const result = {
      id: vehicle._id,
      name: vehicle.name,
      description: vehicle.description,
      exterior: vehicle.exterior,
      interior: vehicle.interior,
      payment: vehicle.payment,
      plan: vehicle.plan,
      seatsNumber: vehicle.seatsNumber,
      securityDetails: vehicle.securityDetails,
      specialDetails: vehicle.specialDetails,
      liters: vehicle.liters,
      transmission: vehicle.transmission,
      aditionalData: vehicle.aditionalData,
      paymentOptions: vehicle.paymentOptions,
      images,
    };
    return res.status(200).send({ vehicle: result });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const getPlatformCatalogVehicles: AsyncController = async (req, res) => {
  try {
    const getAllVehicles = await Vehicle.find({ plan: 'Platform' }).lean();
    const vehicles = [];
    for (const vehicle of getAllVehicles) {
      const images = [];
      for (const image of vehicle.images) {
        const imageInfo = await replaceDocWithUrl(image.toString());
        images.push(imageInfo);
      }
      const result = {
        id: vehicle._id,
        name: vehicle.name,
        description: vehicle.description,
        exterior: vehicle.exterior,
        interior: vehicle.interior,
        payment: vehicle.payment,
        plan: vehicle.plan,
        seatsNumber: vehicle.seatsNumber,
        securityDetails: vehicle.securityDetails,
        specialDetails: vehicle.specialDetails,
        liters: vehicle.liters,
        transmission: vehicle.transmission,
        aditionalData: vehicle.aditionalData,
        images,
      };
      vehicles.push(result);
    }
    return res.status(200).send({ vehicles: vehicles });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const getPersonalCatalogVehicle: AsyncController = async (req, res) => {
  try {
    const personalVehicles = await Vehicle.find({ plan: 'Personal' }).lean();
    const vehicles = [];
    for (const vehicle of personalVehicles) {
      const images = [];
      for (const image of vehicle.images) {
        const imageInfo = await replaceDocWithUrl(image.toString());
        images.push(imageInfo);
      }
      const result = {
        id: vehicle._id,
        name: vehicle.name,
        description: vehicle.description,
        exterior: vehicle.exterior,
        interior: vehicle.interior,
        payment: vehicle.payment,
        plan: vehicle.plan,
        seatsNumber: vehicle.seatsNumber,
        securityDetails: vehicle.securityDetails,
        specialDetails: vehicle.specialDetails,
        liters: vehicle.liters,
        transmission: vehicle.transmission,
        paymentOptions: vehicle.paymentOptions,
        aditionalData: vehicle.aditionalData,
        images,
      };
      vehicles.push(result);
    }
    return res.status(200).send({ vehicles: vehicles });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const getLastAssociatesData: AsyncController = async (req, res) => {
  try {
    const data = await getLastAssociateOfActiveVehicles();

    return res.status(200).send({ message: stockVehiclesText.success.vehicleAdded, data });
  } catch (error) {
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};
