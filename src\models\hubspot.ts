import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const hubspot = new Schema({
  body: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
hubspot.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const Hubspot = model('Hubspot', hubspot);

export default Hubspot;
