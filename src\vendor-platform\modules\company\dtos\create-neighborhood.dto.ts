import { z } from 'zod';
import { timeRangeSchema } from './create-company.dto';

const weeklyScheduleSchema = z.object({
  monday: timeRangeSchema.optional(),
  tuesday: timeRangeSchema.optional(),
  wednesday: timeRangeSchema.optional(),
  thursday: timeRangeSchema.optional(),
  friday: timeRangeSchema.optional(),
  saturday: timeRangeSchema.optional(),
  sunday: timeRangeSchema.optional(),
});

export const createNeighborhoodDto = z.object({
  crewId: z.string(),
  name: z.string().min(3),
  scheduleConfig: z.object({
    weeklySchedule: weeklyScheduleSchema,
    installationDuration: z.number().min(30),
    timezone: z.string(),
    breakTime: timeRangeSchema,
    maxSimultaneousInstallations: z.number().min(1),
  }),
});

export type CreateNeighborhoodDto = z.infer<typeof createNeighborhoodDto>;
