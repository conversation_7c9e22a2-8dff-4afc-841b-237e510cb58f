import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { createWorkshop, getAllWorkshops, updateWorkshop } from '../controllers/workshop.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  createWorkshopOverride,
  getWorkshopOverrides,
} from '../../scheduleOverride/controllers/scheduleOverride.controller';
import {
  createAppointmentToWorkshop,
  getAvailableSlotsByWorkshop,
  getWorkshopAppointments,
  getAppointmentInfoByPlatesAndKm,
} from '../controllers/workshop-appointments.controller';

const workshopRouter = Router();

const workShopURL = '/workshops';

/*
 * Public routes
 */

workshopRouter.get(
  `/public${workShopURL}/:workshopId/available-slots/:date/:serviceTypeId`,
  errorHandlerV2(getAvailableSlotsByWorkshop)
);

// Nuevo endpoint público para obtener información de cita por placas y kilometraje
workshopRouter.get(`/public${workShopURL}/appointment-info`, errorHandlerV2(getAppointmentInfoByPlatesAndKm));

// Crear cita para un taller (público), que funcione igual que el privado
workshopRouter.post(
  `/public${workShopURL}/:workshopId/appointments`,
  errorHandlerV2(createAppointmentToWorkshop)
);

/**
 * Private routes
 */

workshopRouter.get(workShopURL, verifyTokenVendorPlatform, errorHandlerV2(getAllWorkshops));
workshopRouter.post(workShopURL, verifyTokenVendorPlatform, errorHandlerV2(createWorkshop));

workshopRouter.patch(`${workShopURL}/:workshopId`, verifyTokenVendorPlatform, errorHandlerV2(updateWorkshop));

workshopRouter.get(
  `${workShopURL}/:workshopId/available-slots/:date/:serviceTypeId`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getAvailableSlotsByWorkshop)
);
workshopRouter.post(
  `${workShopURL}/:workshopId/appointments`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createAppointmentToWorkshop)
);
workshopRouter.get(
  `${workShopURL}/:workshopId/appointments`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getWorkshopAppointments)
);

workshopRouter.post(
  `${workShopURL}/:workshopId/overrides`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createWorkshopOverride)
);
workshopRouter.get(
  `${workShopURL}/:workshopId/overrides`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getWorkshopOverrides)
);

export default workshopRouter;
