import { AsyncController } from '@/types&interfaces/types';
import { workshopService } from '../services/workshop.service';
import { createWorkshopDto } from '../dtos/create-workshop.dto';
import { Workshop } from '../models/workshops.model';

export const createWorkshop: AsyncController = async (req, res) => {
  const data = createWorkshopDto.parse(req.body);

  const workShop = await workshopService.createWorkshop(data);

  return res.status(200).send({
    message: 'Workshop created',
    data: workShop,
  });
};

export const getAllWorkshops: AsyncController = async (req, res) => {
  const workshops = await Workshop.find().lean();

  return res.status(200).send({ message: 'Talleres encontrados', data: workshops });
};

export const getOrganizationWorkshops: AsyncController = async (req, res) => {
  if (!req.params.organizationId) {
    return res.status(400).send({ error: 'ID de organización requerido' });
  }
  const workshops = await Workshop.find({ organization: req.params.organizationId }).lean();

  return res.status(200).send({ message: 'Talleres encontrados', data: workshops });
};

export const updateWorkshop: AsyncController = async (req, res) => {
  const data = createWorkshopDto.parse(req.body);

  const workshop = await workshopService.updateWorkshopById(req.params.workshopId, data);

  return res.status(200).send({
    message: 'Workshop updated',
    data: workshop,
  });
};
