import { /* StepName, StepProperties */ steps } from '../../../constants';

// export const stepFunc: Record<StepName, StepProperties> = {} as Record<StepName, StepProperties>;

// Object.entries(steps).forEach(([prop, value]) => {
//   stepFunc[value.name] = prop as StepProperties;
// });

/**
 * This is a map of step names to step properties
 * @example
 * {
 * Stock: "stock",
 * "Vehiculo listo": "vehicleReady",
 * "Conductor asignado": "driverAssigned",
 * "Contrato generado": "contractCreated",
 * Entregado: "delivered",
 * "Solicitud de reingreso": "readmissions",
 * }
 */
export const stepRelationQuery: Record<string, string> = Object.entries(steps).reduce(
  (acc, [key, value]) => {
    acc[value.name] = key;
    return acc;
  },
  {} as Record<string, string>
);
