import { AsyncController } from '@/types&interfaces/types';
import { RealTimeTrackingService } from '../services/real-time-tracking.service';

/**
 * Start service time tracking
 */
export const startServiceTracking: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    const { technicianId } = req.body;
    const organizationId = req.userVendor.organizationId;

    const service = await RealTimeTrackingService.startService(serviceId, organizationId, technicianId);

    return res.status(200).json({
      message: 'Service tracking started successfully',
      data: service,
    });
  } catch (error: any) {
    console.error('Error starting service tracking:', error);
    return res.status(500).json({
      message: 'Error starting service tracking',
      error: error.message,
    });
  }
};

/**
 * Update service phase with evidence
 */
export const updateServicePhase: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    const { phase, notes, technicianId } = req.body;
    const organizationId = req.userVendor.organizationId;

    if (!phase) {
      return res.status(400).json({
        message: 'Phase is required',
      });
    }

    // Extract files from request
    const files = req.files as Express.Multer.File[];
    const photos = files?.filter((file) => file.mimetype.startsWith('image/')) || [];
    const videos = files?.filter((file) => file.mimetype.startsWith('video/')) || [];

    const evidence = {
      photos: photos.length > 0 ? photos : undefined,
      videos: videos.length > 0 ? videos : undefined,
      notes,
    };

    const service = await RealTimeTrackingService.updateServicePhase(
      serviceId,
      organizationId,
      phase,
      evidence,
      technicianId
    );

    return res.status(200).json({
      message: 'Service phase updated successfully',
      data: service,
    });
  } catch (error: any) {
    console.error('Error updating service phase:', error);
    return res.status(500).json({
      message: 'Error updating service phase',
      error: error.message,
    });
  }
};

/**
 * Pause service
 */
export const pauseService: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    const { pauseReason } = req.body;
    const organizationId = req.userVendor.organizationId;

    if (!pauseReason) {
      return res.status(400).json({
        message: 'Pause reason is required',
      });
    }

    const service = await RealTimeTrackingService.pauseService(serviceId, organizationId, pauseReason);

    return res.status(200).json({
      message: 'Service paused successfully',
      data: service,
    });
  } catch (error: any) {
    console.error('Error pausing service:', error);
    return res.status(500).json({
      message: 'Error pausing service',
      error: error.message,
    });
  }
};

/**
 * Resume service
 */
export const resumeService: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    // const organizationId = req.userVendor.organizationId;

    const service = await RealTimeTrackingService.resumeService(serviceId);

    return res.status(200).json({
      message: 'Service resumed successfully',
      data: service,
    });
  } catch (error: any) {
    console.error('Error resuming service:', error);
    return res.status(500).json({
      message: 'Error resuming service',
      error: error.message,
    });
  }
};

/**
 * Complete service with evidence
 */
export const completeServiceWithEvidence: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    const { notes, qualityCheckPassed, actualCost, evidenceNotes } = req.body;
    const organizationId = req.userVendor.organizationId;

    // Extract files from request
    const files = req.files as Express.Multer.File[];
    const photos =
      files?.filter((file) => file.fieldname === 'completionPhotos' && file.mimetype.startsWith('image/')) ||
      [];
    const videos =
      files?.filter((file) => file.fieldname === 'completionVideos' && file.mimetype.startsWith('video/')) ||
      [];
    const qualityCheckPhotos =
      files?.filter(
        (file) => file.fieldname === 'qualityCheckPhotos' && file.mimetype.startsWith('image/')
      ) || [];

    const completionData = {
      notes,
      qualityCheckPassed: qualityCheckPassed !== undefined ? Boolean(qualityCheckPassed) : undefined,
      actualCost: actualCost ? parseFloat(actualCost) : undefined,
      evidence: {
        photos: photos.length > 0 ? photos : undefined,
        videos: videos.length > 0 ? videos : undefined,
        qualityCheckPhotos: qualityCheckPhotos.length > 0 ? qualityCheckPhotos : undefined,
        notes: evidenceNotes,
      },
    };

    const service = await RealTimeTrackingService.completeService(serviceId, organizationId, completionData);

    return res.status(200).json({
      message: 'Service completed successfully',
      data: service,
    });
  } catch (error: any) {
    console.error('Error completing service:', error);
    return res.status(500).json({
      message: 'Error completing service',
      error: error.message,
    });
  }
};

/**
 * Get real-time progress for an order
 */
export const getOrderProgress: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;
    const organizationId = req.userVendor.organizationId;

    const progress = await RealTimeTrackingService.getOrderProgress(orderId, organizationId);

    return res.status(200).json({
      message: 'Order progress retrieved successfully',
      data: progress,
    });
  } catch (error: any) {
    console.error('Error getting order progress:', error);
    return res.status(500).json({
      message: 'Error getting order progress',
      error: error.message,
    });
  }
};

/**
 * Get service details with time tracking
 */
export const getServiceDetails: AsyncController = async (req, res) => {
  try {
    const { serviceId } = req.params;
    const organizationId = req.userVendor.organizationId;

    // This would typically get detailed service information
    // For now, we'll get the progress for the order containing this service
    const service = await RealTimeTrackingService.getOrderProgress(
      serviceId, // This should be orderId, but we'll adapt
      organizationId
    );

    return res.status(200).json({
      message: 'Service details retrieved successfully',
      data: service,
    });
  } catch (error: any) {
    console.error('Error getting service details:', error);
    return res.status(500).json({
      message: 'Error getting service details',
      error: error.message,
    });
  }
};

/**
 * Get active services dashboard
 */
export const getActiveServicesDashboard: AsyncController = async (req, res) => {
  try {
    // const organizationId = req.userVendor.organizationId;
    // const { workshopId } = req.query;

    // This would typically aggregate data from multiple orders
    // For now, we'll return a basic structure
    const dashboard = {
      activeServices: 0,
      pausedServices: 0,
      completedToday: 0,
      averageServiceTime: 0,
      slaCompliance: 0,
      services: [], // This would contain actual service data
    };

    return res.status(200).json({
      message: 'Active services dashboard retrieved successfully',
      data: dashboard,
    });
  } catch (error: any) {
    console.error('Error getting active services dashboard:', error);
    return res.status(500).json({
      message: 'Error getting active services dashboard',
      error: error.message,
    });
  }
};
