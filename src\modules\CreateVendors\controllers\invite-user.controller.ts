import { Async<PERSON>ontroller } from '@/types&interfaces/types';
import { logger } from '@/clean/lib/logger';
import { organizationsService } from '@/vendor-platform/modules/organizations/services/organizations.service';
import { z } from 'zod';
import { userVendorTypes } from '@/vendor-platform/modules/users/models/user.model';

/* 
You can handle such conditional validation using .refine(validator: (data:T)=>any, params?: RefineParams) - here

Example implementation:

const schema = z.object({
  subject: z.number().default(0),
  role: z.object({
    owner: z.number(),
    stranger: z.number(),
  }),
}).refine(data => data.subject !== 1 || (data.subject === 1 && data.role), {
  message: "Role field is required when subject equals 1",
  path: ['role'] // Pointing out which field is invalid
});
*/

const inviteUserToVendorDto = z
  .object({
    email: z.string().email(),
    organizationId: z.string().optional(),
    // make organizationId required if user type is 'workshop'
    name: z.string().min(3),
    userType: z.enum(userVendorTypes).optional().default('workshop'),
  })
  .refine((data) => data.userType !== 'workshop' || (data.userType === 'workshop' && data.organizationId), {
    message: "organizationId is required when user type is 'workshop'",
    path: ['organizationId'], // Pointing out which field is invalid
  });

// Controlador para invitar usuarios a un vendor
export const inviteUserToVendor: AsyncController = async (req, res) => {
  try {
    const { email, organizationId, name, userType } = inviteUserToVendorDto.parse(req.body);
    const role = req.userReq.role;
    console.table({ email, organizationId, name, userType });
    const resend = req.query.resend === 'true';
    const originUrl = req.headers.origin!;

    // Llamada al servicio para manejar la invitación
    const result = await organizationsService.inviteUserToVendor({
      email,
      organizationId,
      name,
      resend,
      originUrl,
      role,
      userType,
    });

    return res.status(200).json(result);
  } catch (error: any) {
    logger.info({
      message: '[inviteUserToVendor] An unexpected error occurred',
      stack: error.stack,
    });

    return res.status(500).json({
      message: error.message || 'An unexpected error occurred',
      error,
    });
  }
};

// Controlador para aceptar una invitación de vendor
export const acceptVendorInvitation: AsyncController = async (req, res) => {
  try {
    const { password } = req.body;
    const image: Express.Multer.File | undefined = req.file;
    const { code } = req.query;

    // Validación inicial
    if (!code) return res.status(400).json({ message: 'Token not found' });

    // Llamada al servicio para manejar la aceptación de la invitación
    const result = await organizationsService.acceptVendorInvitation({
      code: code as string,
      password,
      image,
    });

    return res.status(200).json(result);
  } catch (error: any) {
    logger.info({
      message: '[acceptVendorInvitation] An unexpected error occurred',
      stack: error.stack,
    });

    return res.status(500).json({
      message: error.message || 'An unexpected error occurred',
      error,
    });
  }
};
