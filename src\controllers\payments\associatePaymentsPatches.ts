import moment from 'moment';
import {
  WIRE4_API_URL,
  WIRE4_SUCRIPTION,
  associatePaymentsConsts,
  associateText,
  stockVehiclesText,
  wire4Data,
} from '../../constants';
import StockVehicle from '../../models/StockVehicleSchema';
import AssociatePayments from '../../models/associatePayments';
import Associate from '../../models/associateSchema';
import MainContractSchema from '../../models/mainContractSchema';
import { AsyncController } from '../../types&interfaces/types';
import axios from 'axios';
import { ValidRegion, tokenAssignGigstack } from '../../services/tokenAssignGigstack';
import { createWire4BankAccount } from '../../services/createWire4BankAccount';
import MassiveGigPatchLogs from '../../models/massiveGigPatchLogs';
import gigUnnasigned from '../../models/gigUnnasignedUsers';
import { getWire4Token } from '../../services/getWire4Token';
import { saveGigErrors } from '../../services/saveGigError';
import gigHistory from '../../models/gigHistory';

export const existingAssociatePaymentFlow: AsyncController = async (req, res) => {
  const { associateID, model } = req.body;
  try {
    const associate = await Associate.findById(associateID);
    if (!associate) {
      return res.status(404).send({ message: associateText.errors.associateNotFound });
    }

    const mainContract = await MainContractSchema.findOne({ associatedId: associateID });
    if (!mainContract) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.mainContract404 });
    }

    const stockVehicle = await StockVehicle.findById(mainContract.stockId);
    if (!stockVehicle) {
      return res.status(404).send({ message: stockVehiclesText.errors.stockVehiclesNotFound });
    }

    const associatePayments = await AssociatePayments.findOne({ associateId: associateID });
    if (!associatePayments) {
      const region = stockVehicle.vehicleState;

      const deliveredDate = moment(mainContract.deliveredDate);
      const currentDate = moment();
      const weeksPassed = currentDate.diff(deliveredDate, 'weeks');

      const remainingWeeks = 156 - weeksPassed;

      const newPaymentsArray = Array.from({ length: remainingWeeks }, () => ({
        payed: false,
        weeklyCost: mainContract.weeklyRent,
        block: false,
      }));
      const gigToken = tokenAssignGigstack(region.toUpperCase() as ValidRegion);

      const gigConfig = {
        headers: {
          Authorization: `Bearer ${gigToken}`,
          'Content-Type': 'application/json',
        },
      };

      const { data } = await axios.get(
        `https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/clients/list?email=${associate.email}`,
        gigConfig
      );

      if (data.clients.length === 0) {
        return res.status(404).send({ message: associatePaymentsConsts.errors.gigstackExistent });
      }
      const dataToSend = {
        vehiclesId: mainContract.stockId,
        associateId: associateID,
        model: model,
        associateEmail: associate?.email.trim().toLocaleLowerCase(),
        contractId: mainContract._id,
        paymentsArray: newPaymentsArray,
        region: region.toUpperCase(),
        gigId: data.clients[0].id,
      };
      const newAssociatePayments = await AssociatePayments.create(dataToSend);
      return res.status(200).send({ message: associatePaymentsConsts.success.created, newAssociatePayments });
    }
    return res.status(200).send({ message: associatePaymentsConsts.errors.existingPaymentTable });
  } catch (error) {
    console.error(error);
    await saveGigErrors('existingAssociatePaymentFlow', req.body, error);
    return res.status(400).send({ error });
  }
};
//Careful with this function!!!!!!!
export const massiveImplementationToUsers: AsyncController = async (req, res) => {
  const { regionCode } = req.params;
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const gigConfig = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const { data } = await axios.get(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/clients/list',
      gigConfig
    );

    const limitedArray = data.clients;
    const promises = limitedArray.map(async (client: any) => {
      const associatePayment = await AssociatePayments.findOne({ associateEmail: client.email });
      if (!associatePayment) {
        return `${associatePaymentsConsts.errors.payment404} ${client.name} ${client.email}`;
      }
      console.log(client);
      if (associatePayment.monexClabe) {
        return `${associatePaymentsConsts.errors.existingMonexClabe} ${associatePayment.monexClabe}`;
      }
      const associate = await Associate.findById(associatePayment.associateId);
      if (!associate) {
        return `${associateText.errors.associateNotFound} ${client.name} ${client.email}`;
      }
      const mainContract = await MainContractSchema.findOne({ associatedId: associatePayment.associateId });
      if (!mainContract) {
        return `${associatePaymentsConsts.errors.mainContract404} ${associatePayment.associateId}`;
      }

      const stockVehicle = await StockVehicle.findById(mainContract.stockId);
      if (!stockVehicle) {
        return `${stockVehiclesText.errors.stockVehiclesNotFound} ${mainContract.stockId} `;
      }
      const deliveredDate = moment(mainContract.deliveredDate);
      const currentDate = moment();
      const weeksPassed = currentDate.diff(deliveredDate, 'weeks');

      const remainingWeeks = 156 - weeksPassed;

      const newPaymentsArray = Array.from({ length: remainingWeeks }, () => ({
        payed: false,
        weeklyCost: mainContract.weeklyRent,
        block: false,
      }));

      associatePayment.gigId = client.id;
      associatePayment.region = stockVehicle.vehicleState.toLocaleUpperCase();
      associatePayment.model = stockVehicle.model.toUpperCase();
      associatePayment.paymentsArray = newPaymentsArray;

      const userData = JSON.stringify({
        alias: stockVehicle.carNumber,
        currency_code: wire4Data.bankAccount.currency_code,
        email: [associatePayment.associateEmail.trim().toLocaleLowerCase()],
        name: `${associate.firstName} ${associate.lastName}`,
      });

      const responseMonex = await createWire4BankAccount(userData);
      if (responseMonex.error) {
        return responseMonex.error;
      }

      associatePayment.monexClabe = responseMonex.clabe;
      const gigData = {
        id: client.id,
        metadata: {
          clabe: responseMonex.clabe,
          internalId: associate._id,
        },
      };

      const gigClabe = await axios.put(
        'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/client',
        gigData,
        gigConfig
      );

      associatePayment.save();
      return `${JSON.stringify(gigClabe.data)} ${stockVehicle.carNumber} ${responseMonex}`;
    });
    const responses = await Promise.all(promises);
    const logs = new MassiveGigPatchLogs({
      responses: responses,
    });

    await logs.save();
    return res.status(200).send({ responses });
  } catch (error) {
    console.error(error);
    await saveGigErrors('massiveImplementationToUsers', req.body, error);
    return res.status(400).send({ error });
  }
};

export const paymentFixAssignation: AsyncController = async (req, res) => {
  const { regionCode } = req.params;
  const { url } = req.body;
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const gigConfig = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };
    const pendingPayments = await axios.get(url, gigConfig);

    const promises = pendingPayments.data.data.map(async (paymentGig: any) => {
      try {
        const associatePayment = await AssociatePayments.findOne({ associateEmail: paymentGig.client.email });
        if (!associatePayment) {
          return associatePaymentsConsts.errors.payment404;
        }

        if (associatePayment.paymentsArray[associatePayment.paymentNumber].paymentId) {
          return `El cliente ya tiene su pago correcto ${JSON.stringify(
            associatePayment.paymentsArray[associatePayment.paymentNumber]
          )}`;
        }
        console.log(associatePayment, 'soy el payment');
        if (!associatePayment.paymentsArray[0].paymentId) {
          const payment = associatePayment.paymentsArray[0];
          // const date = new Date(paymentGig.timestamp);

          payment.status = paymentGig.status;
          // payment.date = date.toString();
          payment.url = paymentGig.shortURL;
          payment.paymentId = paymentGig.fid;
          console.log(payment.status);
        } else {
          const payment = associatePayment.paymentsArray[associatePayment.paymentNumber];
          // const date = new Date(paymentGig.timestamp);

          payment.status = paymentGig.status;
          // payment.date = date.toString();
          payment.url = paymentGig.shortURL;
          payment.paymentId = paymentGig.fid;
        }

        if (associatePayment.balance === 0) {
          const totalItems = paymentGig.items.reduce((acc: number, curr: any) => acc + curr.total, 0);

          associatePayment.balance -= totalItems;
        }
        await associatePayment.save();

        return JSON.stringify(associatePayment.paymentsArray[associatePayment.paymentNumber]);
      } catch (error) {
        return JSON.stringify(error);
      }
    });

    const responses = await Promise.all(promises);
    console.log(responses);

    return res.status(200).send({ data: responses, nextUrl: pendingPayments.data.nextUrl });
  } catch (error) {
    console.error(error);
    await saveGigErrors('paymentFixAssignation', req.body, error);
    return res.status(400).send({ error });
  }
};

export const fixesDataAssociatePayments: AsyncController = async (req, res) => {
  try {
    const allAssociatePayments = await AssociatePayments.find({
      region: { $exists: false },
    });

    const promises = allAssociatePayments.map(async (paymentTable) => {
      console.log({ paymentTable: paymentTable.associateEmail, mssg: 'data' });
      if (!paymentTable.model || !paymentTable.region) {
        //asignar modelo y region
        const stockVehicle = await StockVehicle.findById(paymentTable.vehiclesId);

        if (!stockVehicle) {
          return 'Vehiculo no encontrado';
        }

        paymentTable.region = stockVehicle.vehicleState.toUpperCase();

        await paymentTable.save();

        const response = {
          email: paymentTable.associateEmail,
          region: paymentTable.region,
        };
        return `Actualizado ${JSON.stringify(response)}`;
      }
      return `Cliente no actualizado ${paymentTable.associateEmail}`;
    });

    const responses = await Promise.all(promises);

    return res.status(201).send({ responses });
  } catch (error) {
    console.error(error);
    await saveGigErrors('fixesDataAssociatePayments', req.body, error);
    return res.status(400).send({ error });
  }
};

export const fixesClabeMonexAssociatePayments: AsyncController = async (req, res) => {
  try {
    const batchSize: number = 20;
    const associatePayments = await AssociatePayments.find({});

    const allResponses = [];
    const failedResponses: any = [];

    for (let i = 0; i <= associatePayments.length; i += batchSize) {
      const paymentsBatch = associatePayments.slice(i, i + batchSize);

      const promises = paymentsBatch.map(async (paymentsTable) => {
        const stockVehicle = await StockVehicle.findById(paymentsTable.vehiclesId);
        if (!stockVehicle) {
          return stockVehiclesText.errors.stockVehiclesNotFound;
        }

        const associate = await Associate.findById(paymentsTable.associateId);

        if (!associate) {
          return associateText.errors.associateNotFound;
        }

        const userData = JSON.stringify({
          alias: stockVehicle.carNumber,
          currency_code: wire4Data.bankAccount.currency_code,
          email: [paymentsTable.associateEmail],
          name: `${associate.firstName} ${associate.lastName}`,
        });

        const responseMonex = await createWire4BankAccount(userData);
        if (responseMonex.error) {
          // Agregar a la lista de respuestas fallidas
          failedResponses.push({
            errorMessage: responseMonex.error,
            associateEmail: paymentsTable.associateEmail,
          });
          return null;
        }

        paymentsTable.monexClabe = responseMonex.clabe;
        await paymentsTable.save();

        return `CLABE asignada a ${paymentsTable.associateEmail}, clabe: ${userData}`;
      });
      const responses = await Promise.all(promises);
      const successfulResponses = responses.filter((response) => response !== null);
      allResponses.push(...successfulResponses);
    }

    return res.status(200).send({ allResponses, failedResponses });
  } catch (error) {
    console.error(error);
    await saveGigErrors('fixesClabeMonexAssociatePayments', req.body, error);
    return res.status(400).send({ error });
  }
};

export const gigClabeAssignator: AsyncController = async (req, res) => {
  const { regionCode } = req.params;
  const { url } = req.body;
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const gigConfig = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const { data } = await axios.get(url, gigConfig);

    const batchSize: number = 20;
    const clients = data.clients;

    const allResponses = [];
    const responseDB = new gigUnnasigned({ region: regionCode });

    for (let i = 0; i <= clients.length; i += batchSize) {
      const clientsBatch = clients.slice(i, i + batchSize);

      const promises = await clientsBatch.map(async (client: any) => {
        console.log(client);
        const metadataObj = JSON.parse(client.metadata);

        const isEmpty = Object.keys(metadataObj).length === 0 && metadataObj.constructor === Object;
        const faltaClabe = !metadataObj.hasOwnProperty('clabe');

        if (isEmpty || faltaClabe || metadataObj.clabe === null) {
          const paymentTable = await AssociatePayments.findOne({ associateEmail: client.email });

          if (!paymentTable) {
            responseDB.data.push({ email: client.email, companyNumber: client.company });
            return `${associatePaymentsConsts.errors.payment404} ${JSON.stringify(client)}`;
          }

          if (!paymentTable.monexClabe) {
            return `Usuario sin clabe monex en DB ${client.email}`;
          }
          console.log(client.name, client.email);
          const gigData = {
            id: client.id,
            metadata: {
              clabe: paymentTable.monexClabe,
              internalId: paymentTable.associateId,
            },
          };

          const gigClabe = axios.put(
            'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/client',
            gigData,
            gigConfig
          );

          return gigClabe.then((response) => JSON.stringify(response.data));
        }
        return null;
      });
      const responses = await Promise.all(promises);
      await responseDB.save();
      allResponses.push(...responses);
    }
    return res.status(200).send({ allResponses, startAfet: data.startAfter });
  } catch (error) {
    console.error(error);
    await saveGigErrors('gigClabeAssignator', req.body, error);
    return res.status(400).send({ error });
  }
};

export const monexEmailFix: AsyncController = async (req, res) => {
  try {
    const token = await getWire4Token();
    const { data } = await axios(`${WIRE4_API_URL}/subscriptions/${WIRE4_SUCRIPTION}/depositants`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    if (!data || !data.depositants || data.depositants.length === 0) {
      return res.status(404).send({ message: 'No se encontraron las clabes' });
    }

    const batchSize = 20;
    const allResponses = [];
    for (let i = 0; i < data.depositants.length; i += batchSize) {
      const depositantBatch = data.depositants.slice(i, i + batchSize);

      const promises = depositantBatch.map(async (depositant: any) => {
        const paymentTable = await AssociatePayments.findOne({ monexClabe: depositant.depositant_clabe });
        if (!paymentTable) {
          return associatePaymentsConsts.errors.payment404;
        }

        if (paymentTable.monexEmail !== depositant.email[0]) {
          paymentTable.monexEmail = depositant.email[0];

          await paymentTable.save();

          return `Actualizado ${paymentTable.monexClabe}, ${paymentTable.monexEmail}`;
        }
        return null;
      });

      const responses = await Promise.all(promises);
      allResponses.push(...responses);
    }

    return res.status(200).send({ allResponses });
  } catch (error) {
    console.error(error);
    await saveGigErrors('monexEmailFix', req.body, error);
    return res.status(400).send({ error });
  }
};

export const gigCompanyAssignator: AsyncController = async (req, res) => {
  const { regionCode } = req.params;
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const gigConfig = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const { data } = await axios.get(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/clients/list',
      gigConfig
    );

    const batchSize: number = 20;
    const clients = data.clients;

    const allResponses = [];
    for (let i = 0; i <= clients.length; i += batchSize) {
      const clientsBatch = clients.slice(i, i + batchSize);

      const promises = await clientsBatch.map(async (client: any) => {
        if (client.company.length > 0) {
          const mainContract = await MainContractSchema.findOne({ contractNumber: client.company });
          if (!mainContract) {
            return 'Cliente sin MainContract';
          }
          const paymentTable = await AssociatePayments.findOne({ associateId: mainContract.associatedId });

          if (!paymentTable) {
            return 'PaymentTable no encontrada';
          }

          if (paymentTable.associateEmail !== client.email) {
            return `Correos diferentes! ${paymentTable.associateEmail}, ${client.email} `;
          }

          return null;
        }
        return null;
      });
      const responses = await Promise.all(promises);
      allResponses.push(...responses);
    }
    return res.status(200).send({ allResponses });
  } catch (error) {
    console.error(error);
    await saveGigErrors('gigCompanyAssignator', req.body, error);
    return res.status(400).send({ error });
  }
};

export const addMonexAccountToGig: AsyncController = async (req, res) => {
  const associatePayments = await AssociatePayments.find({
    monexClabe: { $exists: true },
  });
  const responses = [];
  if (associatePayments.length === 0) {
    return res.status(404).send({ message: 'No clabes to add' });
  }

  for (const associatePayment of associatePayments) {
    const gigToken = tokenAssignGigstack(associatePayment.region as ValidRegion);
    const gigConfig = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const stockVehicle = await StockVehicle.findById(associatePayment.vehiclesId);
    if (!stockVehicle) {
      return res.status(404).send({ message: stockVehiclesText.errors.stockVehiclesNotFound });
    }

    const company = (await stockVehicle.extensionCarNumber)
      ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
      : stockVehicle.carNumber;

    try {
      console.log('check if the user have clabe in gigstack', company);
      const checkIfTheAccountHaveClabe = await axios.get(
        `https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/clients/list?company=${company.toString()}`,
        gigConfig
      );

      const id = checkIfTheAccountHaveClabe.data.clients[0].id;
      const gigData = {
        id,
        metadata: {
          clabe: associatePayment.monexClabe,
          internalId: associatePayment.associateId,
        },
      };

      const response = await axios.put(
        'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/client',
        gigData,
        gigConfig
      );
      console.log(response.data);
      responses.push(response.data);
      gigHistory.create({
        body: gigData,
        headers: gigConfig.headers,
        method: 'PUT',
        url: 'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/client',
        status: response.status,
        response: response.data,
      });
      associatePayment.gigId = id;
      await associatePayment.save();
    } catch (error) {
      responses.push({ error: error });
    }
  }
  return res.status(200).send({ message: 'All clabes added to gig', responses });
};
