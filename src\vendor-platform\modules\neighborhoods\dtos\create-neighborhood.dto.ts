import { z } from 'zod';

export const createNeighborhoodDto = z.object({
  crewId: z.string(),
  companyId: z.string(),
  cityId: z.string(),
  name: z.string().min(3),
  address: z
    .object({
      street: z.string().min(3),
      number: z.string().min(1),
      interior: z.string().optional(),
      colony: z.string().min(3),
      city: z.string().min(3),
      postalCode: z.string().min(5),
    })
    .optional(),
  mapsLink: z.string().optional(),
});

export type CreateNeighborhoodDto = z.infer<typeof createNeighborhoodDto>;
