import { Request, Response } from 'express';
import CustomerAcknowledgement, { ICustomerAcknowledgement } from '../models/customerAcknowledgementSchema';
import { logger } from '@/clean/lib/logger';
import { sendOnboardingSupportEndEmailToCustomer } from '@/modules/platform_connections/emailFunc';
import { ONBOARDING_SUPPORT_URL } from '@/constants';
import Associate from '@/models/associateSchema';
import { sendOnboardingSupportEnd } from '@/services/onboarding/sendHilos';

export const createCustomerAcknowledgement = async (req: Request, res: Response) => {
  const {
    associateId,
    fullName,
    readBenfitsOffered,
    readInsuranceCoverage,
    readMaintenance,
    readTermConditions,
    readTheft,
    readAccident,
    readPayments,
    pointsAcknowledement,
  } = req.body;

  // Validate status for each step
  const steps = [
    readBenfitsOffered,
    readInsuranceCoverage,
    readMaintenance,
    readTermConditions,
    readTheft,
    readAccident,
    readPayments,
    pointsAcknowledement,
  ];
  for (const step of steps) {
    if (step.status !== 'accepted' && step.status !== 'pending') {
      logger.error(
        `[CustomerAcknowledgement -> createCustomerAcknowledgement]: Invalid status value: ${step.status}. Must be either "accepted" or "pending".`
      );
      return res
        .status(400)
        .json({ message: `Invalid status value: ${step.status}. Must be either "accepted" or "pending".` });
    }
  }

  try {
    const newAcknowledgement = new CustomerAcknowledgement({
      associateId,
      fullName,
      readBenfitsOffered,
      readInsuranceCoverage,
      readMaintenance,
      readTermConditions,
      readTheft,
      readAccident,
      readPayments,
      pointsAcknowledement,
    });

    const savedDoc = await newAcknowledgement.save();
    logger.info(
      `[CustomerAcknowledgement -> createCustomerAcknowledgement]: acknowledgement created ${savedDoc}`
    );
    return res.status(201).json(savedDoc);
  } catch (err) {
    logger.error(`[CustomerAcknowledgement -> createCustomerAcknowledgement]: ${err}`);
    return res.status(500).json({ message: 'Server error', error: err });
  }
};

export const getCustomerAcknowledgementByAssociateId = async (req: Request, res: Response) => {
  const { associateId } = req.params;

  try {
    const acknowledgements = await CustomerAcknowledgement.find({ associateId }).exec();

    if (!acknowledgements || acknowledgements.length === 0) {
      return res.status(404).json({ message: 'No acknowledgements found for the given associateId' });
    }

    return res.status(200).json(acknowledgements);
  } catch (err) {
    logger.error(`[CustomerAcknowledgement -> getCustomerAcknowledgementByAssociateId]: ${err}`);
    return res.status(500).json({ message: 'Server error', error: err });
  }
};

export const updateStep = async (req: Request, res: Response, stepKey: keyof ICustomerAcknowledgement) => {
  const { status, timeStamp } = req.body;
  const { id } = req.params;

  if (!status || !timeStamp) {
    return res.status(400).json({ message: 'status and timeStamp are required' });
  }

  if (status !== 'accepted' && status !== 'pending') {
    return res.status(400).json({ message: 'status must be either "accepted" or "pending"' });
  }
  console.log('id', id);
  try {
    const updatedDoc = await CustomerAcknowledgement.findByIdAndUpdate(
      id,
      { [stepKey]: { status, timeStamp: new Date(timeStamp) } },
      { new: true }
    );

    if (!updatedDoc) {
      return res.status(404).json({ message: 'Document not found' });
    }

    logger.info(`[CustomerAcknowledgement -> updateStep step updated]: ${updatedDoc}`);
    if (stepKey === 'pointsAcknowledement' && status === 'accepted') {
      //send whatsapp and email
      const associate = await Associate.findById(updatedDoc?.associateId);
      if (associate) {
        //email
        try {
          sendOnboardingSupportEndEmailToCustomer({
            customerEmail: associate.email,
            subject: 'Felicidades!',
            url: ONBOARDING_SUPPORT_URL,
          });
        } catch (error) {
          logger.error(
            `[CustomerAcknowledgement -> updateStep->sendOnboardingSupportEndEmailToCustomer]: ${error}`
          );
        }
        //whatsapp
        try {
          sendOnboardingSupportEnd({
            phone: associate.phone.toString(),
            type: 'onboardingSupportEnd',
          });
        } catch (error) {
          logger.error(`[CustomerAcknowledgement -> updateStep->sendOnboardingSupportEnd]: ${error}`);
        }
      }
    }

    return res.status(200).json(updatedDoc);
  } catch (err) {
    logger.error(`[CustomerAcknowledgement -> updateStep]: ${err}`);
    return res.status(500).json({ message: 'Server error', error: err });
  }
};
