import { logger } from '@/clean/lib/logger';
import { ANTHROPIC_MODEL } from '@/constants';
import Anthropic from '@anthropic-ai/sdk';
import { getMimeTypeFromFilename } from './utils';

export const allowedMimeTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
] as const;
export type AllowedMimeType = (typeof allowedMimeTypes)[number];

// Source for LLM OCR
export type Source = {
  data: string;
  media_type: AllowedMimeType;
  prompt?: string;
  filename?: string;
};

// Initialize Anthropic client
const client = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY, // This is the default and can be omitted
});

const parseImageText = async (param: Source) => {
  logger.info(`[parseImageText] - Source: ${JSON.stringify(param)}`);

  // Validate MIME type
  if (!allowedMimeTypes.includes(param.media_type)) {
    throw new Error(`Unsupported MIME type: ${param.media_type}`);
  }

  try {
    // Build content block for image or PDF
    const contentItems = [] as any[];
    if (param.media_type === 'application/pdf') {
      contentItems.push({
        type: 'file',
        source: {
          type: 'base64',
          media_type: param.media_type,
          data: param.data,
          filename: param.filename || 'document.pdf',
        },
      });
    } else {
      contentItems.push({
        type: 'image',
        source: {
          type: 'base64',
          media_type: param.media_type,
          data: param.data,
        },
      });
    }
    contentItems.push({ type: 'text', text: param.prompt });

    // Send request to Anthropic API
    const response = await client.messages.create({
      model: ANTHROPIC_MODEL,
      max_tokens: 1024,
      messages: [{ role: 'user', content: contentItems }],
    });

    logger.info(`[parseImageText] - Response: ${JSON.stringify(response)}`);

    // Validate and parse response using JSON substring extraction
    if (response?.content?.length > 0) {
      const firstContentBlock = response.content[0];
      if (firstContentBlock.type === 'text') {
        const txt = firstContentBlock.text;
        if (!txt) throw new Error('Empty response from Anthropic');
        const start = txt.indexOf('{');
        const end = txt.lastIndexOf('}');
        if (start < 0 || end < 0) throw new Error('Invalid JSON response');
        return JSON.parse(txt.slice(start, end + 1));
      } else {
        throw new Error('Unexpected response content type from Anthropic API');
      }
    } else {
      throw new Error('Empty response from Anthropic API');
    }
  } catch (error) {
    logger.error(`[parseImageText] - Error: ${JSON.stringify(error)}`);
    throw new Error(`Failed to parse image text: ${JSON.stringify(error)}`);
  }
};

// Helper function for Anthropic fallback for multiple sources
const anthropicParseMultipleSources = async (sources: Source[], prompt: string) => {
  logger.info('[parseMultipleImages][Anthropic] Fallback for multiple sources');
  const anthropicClient = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
  const contentItems: any[] = [];
  for (const source of sources) {
    let mediaType = source.media_type;
    if (!mediaType && source.filename) mediaType = getMimeTypeFromFilename(source.filename);
    if (!mediaType || !allowedMimeTypes.includes(mediaType)) {
      throw new Error(`Unsupported MIME type: ${mediaType}`);
    }
    if (mediaType === 'application/pdf') {
      contentItems.push({
        type: 'file',
        source: {
          type: 'base64',
          media_type: mediaType,
          data: source.data,
          filename: source.filename || 'document.pdf',
        },
      });
    } else {
      contentItems.push({
        type: 'image',
        source: {
          type: 'base64',
          media_type: mediaType,
          data: source.data,
        },
      });
    }
  }
  contentItems.push({ type: 'text', text: prompt });

  const response = await anthropicClient.messages.create({
    model: ANTHROPIC_MODEL,
    max_tokens: 1024,
    messages: [{ role: 'user', content: contentItems }],
  });

  if (response?.content?.length > 0) {
    const firstContentBlock = response.content[0];
    if (firstContentBlock.type === 'text') {
      const txt = firstContentBlock.text;
      if (!txt) throw new Error('Empty response from Anthropic');
      const start = txt.indexOf('{');
      const end = txt.lastIndexOf('}');
      if (start < 0 || end < 0) throw new Error('Invalid JSON response');
      return JSON.parse(txt.slice(start, end + 1));
    } else {
      throw new Error('Unexpected response content type from Anthropic API');
    }
  } else {
    throw new Error('Empty response from Anthropic API');
  }
};

export { parseImageText, anthropicParseMultipleSources };
