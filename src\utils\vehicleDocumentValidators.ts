import { Request, Response, NextFunction } from 'express';
import {
  DocumentCategory,
  PresignedUrlRequestItem,
  ProcessDocumentsRequest,
} from '../types&interfaces/vehicleDocuments';

/**
 * Validation error interface for consistent error responses
 */
interface ValidationError {
  code: string;
  message: string;
  field?: string;
}

/**
 * Validator for single OCR extraction endpoint
 */
export const validateOCRExtractionRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors: ValidationError[] = [];
  const { documentType, vehicleId, plates } = req.body;
  const file = req.file;

  // Check if file is provided
  if (!file) {
    errors.push({
      code: 'FILE_MISSING',
      message: 'No file provided',
      field: 'file',
    });
  }

  // Check required parameters
  if (!documentType) {
    errors.push({
      code: 'MISSING_DOCUMENT_TYPE',
      message: 'documentType is required',
      field: 'documentType',
    });
  }

  if (!vehicleId) {
    errors.push({
      code: 'MISSING_VEHICLE_ID',
      message: 'vehicleId is required',
      field: 'vehicleId',
    });
  }

  // Validate documentType is a valid category
  if (documentType && !Object.values(DocumentCategory).includes(documentType)) {
    errors.push({
      code: 'INVALID_DOCUMENT_TYPE',
      message: `Invalid documentType. Must be one of: ${Object.values(DocumentCategory).join(', ')}`,
      field: 'documentType',
    });
  }

  // Check plates requirement for plate documents
  if (
    documentType &&
    (documentType === DocumentCategory.PLATES_BACK || documentType === DocumentCategory.PLATES_FRONT) &&
    !plates
  ) {
    errors.push({
      code: 'MISSING_PLATES',
      message: 'Plates are required for plate documents',
      field: 'plates',
    });
  }

  // Validate vehicleId format (assuming it's a MongoDB ObjectId)
  if (vehicleId && !/^[0-9a-fA-F]{24}$/.test(vehicleId)) {
    errors.push({
      code: 'INVALID_VEHICLE_ID',
      message: 'vehicleId must be a valid ObjectId',
      field: 'vehicleId',
    });
  }

  // If there are validation errors, return 400 response
  if (errors.length > 0) {
    res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_FAILED',
        message: 'Request validation failed',
        details: errors,
      },
    });
    return;
  }

  // If validation passes, continue to the next middleware/controller
  next();
};

/**
 * Validator for presigned URL generation endpoint
 */
export const validatePresignedUrlRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors: ValidationError[] = [];
  const { filesToUpload } = req.body as { filesToUpload: PresignedUrlRequestItem[] };
  const userId = (req as any).userId?.userId as string;

  // Check if user ID is provided (from verifyToken middleware)
  if (!userId) {
    errors.push({
      code: 'MISSING_USER_ID',
      message: 'User ID is required',
      field: 'userId',
    });
  }

  // Check if filesToUpload is provided and is an array
  if (!filesToUpload) {
    errors.push({
      code: 'MISSING_FILES_TO_UPLOAD',
      message: 'filesToUpload is required',
      field: 'filesToUpload',
    });
  } else if (!Array.isArray(filesToUpload)) {
    errors.push({
      code: 'INVALID_FILES_TO_UPLOAD_FORMAT',
      message: 'filesToUpload must be an array',
      field: 'filesToUpload',
    });
  } else if (filesToUpload.length === 0) {
    errors.push({
      code: 'EMPTY_FILES_TO_UPLOAD',
      message: 'At least one file must be provided for upload',
      field: 'filesToUpload',
    });
  } else if (filesToUpload.length > 50) {
    errors.push({
      code: 'TOO_MANY_FILES',
      message: 'Cannot upload more than 50 documents at a time',
      field: 'filesToUpload',
    });
  }

  // Validate each file item
  if (Array.isArray(filesToUpload)) {
    filesToUpload.forEach((fileItem, index) => {
      if (!fileItem.fileName) {
        errors.push({
          code: 'MISSING_FILE_NAME',
          message: `fileName is required for file at index ${index}`,
          field: `filesToUpload[${index}].fileName`,
        });
      }

      if (!fileItem.contentType) {
        errors.push({
          code: 'MISSING_CONTENT_TYPE',
          message: `contentType is required for file at index ${index}`,
          field: `filesToUpload[${index}].contentType`,
        });
      }

      if (!fileItem.documentCategory) {
        errors.push({
          code: 'MISSING_DOCUMENT_CATEGORY',
          message: `documentCategory is required for file at index ${index}`,
          field: `filesToUpload[${index}].documentCategory`,
        });
      } else if (!Object.values(DocumentCategory).includes(fileItem.documentCategory)) {
        errors.push({
          code: 'INVALID_DOCUMENT_CATEGORY',
          message: `Invalid documentCategory at index ${index}. Must be one of: ${Object.values(DocumentCategory).join(', ')}`,
          field: `filesToUpload[${index}].documentCategory`,
        });
      }

      // Validate content type format
      if (
        fileItem.contentType &&
        !fileItem.contentType.match(/^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^]*$/)
      ) {
        errors.push({
          code: 'INVALID_CONTENT_TYPE',
          message: `Invalid contentType format at index ${index}`,
          field: `filesToUpload[${index}].contentType`,
        });
      }
    });
  }

  // If there are validation errors, return 400 response
  if (errors.length > 0) {
    res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_FAILED',
        message: 'Request validation failed',
        details: errors,
      },
    });
    return;
  }

  // If validation passes, continue to the next middleware/controller
  next();
};

/**
 * Validator for document processing endpoint
 */
export const validateDocumentProcessingRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors: ValidationError[] = [];
  const { documents, userName, userEmail, country, region, contractNumber } =
    req.body as ProcessDocumentsRequest;
  const userId = (req as any).userId?.userId as string;

  // Check if user ID is provided (from verifyToken middleware)
  if (!userId) {
    errors.push({
      code: 'MISSING_USER_ID',
      message: 'User ID is required',
      field: 'userId',
    });
  }

  // Check if userName is provided
  if (!userName) {
    errors.push({
      code: 'MISSING_USER_NAME',
      message: 'userName is required',
      field: 'userName',
    });
  } else if (typeof userName !== 'string' || userName.trim().length === 0) {
    errors.push({
      code: 'INVALID_USER_NAME',
      message: 'userName must be a non-empty string',
      field: 'userName',
    });
  }

  // Check if userEmail is provided
  if (!userEmail) {
    errors.push({
      code: 'MISSING_USER_EMAIL',
      message: 'userEmail is required',
      field: 'userEmail',
    });
  } else if (typeof userEmail !== 'string' || !userEmail.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
    errors.push({
      code: 'INVALID_USER_EMAIL',
      message: 'userEmail must be a valid email address',
      field: 'userEmail',
    });
  }

  // Check if documents is provided and is an array
  if (!documents) {
    errors.push({
      code: 'MISSING_DOCUMENTS',
      message: 'documents is required',
      field: 'documents',
    });
  } else if (!Array.isArray(documents)) {
    errors.push({
      code: 'INVALID_DOCUMENTS_FORMAT',
      message: 'documents must be an array',
      field: 'documents',
    });
  } else if (documents.length === 0) {
    errors.push({
      code: 'EMPTY_DOCUMENTS',
      message: 'At least one document must be provided for processing',
      field: 'documents',
    });
  } else if (documents.length > 50) {
    errors.push({
      code: 'TOO_MANY_DOCUMENTS',
      message: 'Cannot process more than 50 documents at a time',
      field: 'documents',
    });
  }

  // Validate each document item
  if (Array.isArray(documents)) {
    documents.forEach((document, index) => {
      if (!document.s3Key) {
        errors.push({
          code: 'MISSING_S3_KEY',
          message: `s3Key is required for document at index ${index}`,
          field: `documents[${index}].s3Key`,
        });
      } else if (typeof document.s3Key !== 'string' || document.s3Key.trim().length === 0) {
        errors.push({
          code: 'INVALID_S3_KEY',
          message: `s3Key must be a non-empty string for document at index ${index}`,
          field: `documents[${index}].s3Key`,
        });
      }

      if (!document.originalFileName) {
        errors.push({
          code: 'MISSING_ORIGINAL_FILE_NAME',
          message: `originalFileName is required for document at index ${index}`,
          field: `documents[${index}].originalFileName`,
        });
      }

      if (!document.documentCategory) {
        errors.push({
          code: 'MISSING_DOCUMENT_CATEGORY',
          message: `documentCategory is required for document at index ${index}`,
          field: `documents[${index}].documentCategory`,
        });
      } else if (!Object.values(DocumentCategory).includes(document.documentCategory)) {
        errors.push({
          code: 'INVALID_DOCUMENT_CATEGORY',
          message: `Invalid documentCategory at index ${index}. Must be one of: ${Object.values(DocumentCategory).join(', ')}`,
          field: `documents[${index}].documentCategory`,
        });
      }

      if (!document.contentType) {
        errors.push({
          code: 'MISSING_CONTENT_TYPE',
          message: `contentType is required for document at index ${index}`,
          field: `documents[${index}].contentType`,
        });
      }
    });
  }

  // Validate optional fields if provided
  if (country && typeof country !== 'string') {
    errors.push({
      code: 'INVALID_COUNTRY',
      message: 'country must be a string',
      field: 'country',
    });
  }

  if (region && typeof region !== 'string') {
    errors.push({
      code: 'INVALID_REGION',
      message: 'region must be a string',
      field: 'region',
    });
  }

  if (contractNumber && typeof contractNumber !== 'string') {
    errors.push({
      code: 'INVALID_CONTRACT_NUMBER',
      message: 'contractNumber must be a string',
      field: 'contractNumber',
    });
  }

  // If there are validation errors, return 400 response
  if (errors.length > 0) {
    res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_FAILED',
        message: 'Request validation failed',
        details: errors,
      },
    });
    return;
  }

  // If validation passes, continue to the next middleware/controller
  next();
};
