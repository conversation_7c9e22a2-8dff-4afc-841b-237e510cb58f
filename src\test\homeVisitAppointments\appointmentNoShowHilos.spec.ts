import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { sendHomeVisitAppointmentNoShowMessage } from '../../services/onboarding/sendHilos';

jest.mock('nodemailer');

const HILOS_URL = 'https://api.hilos.io/api/channels/whatsapp/template';
const HILOS_TEMPLATES = {
  homeVisitAppointmentNoShow: { templateId: '067b5563-f473-7f47-8000-c9e2f9287be1' },
};

describe('sendHomeVisitAppointmentNoShowMessage', () => {
  let mockAxios: MockAdapter;

  beforeEach(() => {
    mockAxios = new MockAdapter(axios);
  });

  afterEach(() => {
    mockAxios.restore();
  });

  it('should send a message successfully and return the response data', async () => {
    const mockResponse = { success: true, message: 'Message sent' };
    const mockUrl = `${HILOS_URL}/${HILOS_TEMPLATES.homeVisitAppointmentNoShow.templateId}/send`;
    mockAxios.onPost(mockUrl).reply(200, mockResponse);

    const result = await sendHomeVisitAppointmentNoShowMessage({
      name: 'John Doe',
      phone: '1234567890',
      type: 'homeVisitAppointmentNoShow',
      requestId: 'request-123',
    });
    expect(result).toEqual({ success: true, message: 'Message sent' });
  });

  it('should throw an error when the API call fails', async () => {
    mockAxios
      .onPost(`${HILOS_URL}/${HILOS_TEMPLATES.homeVisitAppointmentNoShow.templateId}/send`)
      .reply(500, { error: 'Internal Server Error' });

    await expect(
      sendHomeVisitAppointmentNoShowMessage({
        name: 'John Doe',
        phone: '1234567890',
        type: 'homeVisitAppointmentNoShow',
        requestId: 'request-123',
      })
    ).rejects.toThrow();
  });
});
