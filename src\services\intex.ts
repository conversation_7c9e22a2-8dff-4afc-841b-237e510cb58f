import { Request } from 'express';
import StockVehicle from '../models/StockVehicleSchema';
import User from '../models/userSchema';
import { replaceDocWithUrl } from './getPropertyWithUrls';

/**
 * Returns an object with properties passed by second parameter
 * @param formData  object
 * @param properties array of strings
 */

export function parsedProperties(formData: Request['body'], properties: string[]): Record<string, any> {
  const datosAnalizados: Record<string, any> = {};

  properties.forEach((property) => {
    const valor = formData[property];
    datosAnalizados[property] = valor ? JSON.parse(valor) : null;
  });

  return datosAnalizados;
}

export function parseBody(formData: Request['body']): Record<string, any> {
  const datosAnalizados: Record<string, any> = {};

  for (const property in formData) {
    if (Object.prototype.hasOwnProperty.call(formData, property)) {
      const valor = formData[property];
      datosAnalizados[property] = valor ? JSON.parse(valor) : null;
    }
  }

  return datosAnalizados;
}

const stock = new StockVehicle();

type HistoryArray = (typeof stock)['updateHistory'];

/**
 * Returns an array of sorted history data with user information
 * @param {HistoryArray} historyArray should be typeof HistoryArray
 */

export async function getUpdateHistoryWithUserInfo(historyArray: HistoryArray) {
  const updateHistory = historyArray;

  const userIds = updateHistory.map((item) => item.userId);
  const users = await User.find({ _id: { $in: userIds } }, { name: 1 }).lean();

  const updateHistoryWithUserInfo = updateHistory.map((item) => {
    const user = users.find((usr) => usr._id.toString() === item.userId.toString());

    return {
      ...item,
      user: user || null,
    };
  });

  const usersWithImg = [];

  for (let h of updateHistoryWithUserInfo) {
    const user = await User.findById(h.userId).lean();
    if (user) {
      if (user.image) {
        const img = await replaceDocWithUrl(user.image.toString());
        // console.log({ ...user, image: img });
        usersWithImg.push({ ...h, user: { ...h.user, image: img } });
      } else {
        usersWithImg.push({ ...h, user: { ...h.user, image: { docId: '', url: '', originalName: '' } } });
      }
    } else {
      usersWithImg.push({
        ...h,
        user: { name: 'Usuario eliminado', image: { docId: '', url: '', originalName: '' } },
      });
    }
  }

  const sortedUpdateHistory = usersWithImg.sort((a, b) => {
    const timeA = new Date(a.time ?? '').getTime();
    const timeB = new Date(b.time ?? '').getTime();

    return timeB - timeA; // Orden descendente (el más reciente primero)
  });
  return sortedUpdateHistory;
}
