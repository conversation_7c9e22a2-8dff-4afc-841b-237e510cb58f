import { AsyncController } from '@/types&interfaces/types';
import { NeighborhoodService } from '../services/neighborhood.service';
import { createNeighborhoodDto } from '../dtos/create-neighborhood.dto';
import { updateNeighborhoodDto } from '../dtos/update-neighborhood.dto';

export const createNeighborhood: AsyncController = async (req, res) => {
  const data = createNeighborhoodDto.parse(req.body);

  const neighborhood = await NeighborhoodService.createNeighborhood(data);

  return res.status(201).send({
    message: 'Neighborhood created',
    data: neighborhood,
  });
};

export const getNeighborhoods: AsyncController = async (req, res) => {
  const { companyId, crewId } = req.query as { companyId?: string; crewId?: string };

  console.log('query params', req.query);
  const neighborhoods = await NeighborhoodService.getNeighborhoods({
    companyId,
    crewId,
  });

  return res.status(200).send({
    message: 'Neighborhoods found',
    data: neighborhoods,
  });
};

export const getNeighborhoodById: AsyncController = async (req, res) => {
  const { neighborhoodId } = req.params;

  const neighborhood = await NeighborhoodService.getNeighborhoodById(neighborhoodId);

  if (!neighborhood) {
    return res.status(404).send({
      message: 'Neighborhood not found',
    });
  }

  return res.status(200).send({
    message: 'Neighborhood found',
    data: neighborhood,
  });
};

export const getNeighborhoodsByCity: AsyncController = async (req, res) => {
  const { cityId } = req.params;

  const neighborhoods = await NeighborhoodService.getNeighborhoodsByCity(cityId);

  return res.status(200).send({
    message: 'Neighborhoods found',
    data: neighborhoods,
  });
};

export const updateNeighborhood: AsyncController = async (req, res) => {
  const { neighborhoodId } = req.params;
  const { success, data, error } = updateNeighborhoodDto.safeParse(req.body);

  if (!success) {
    error.issues.forEach((issue) => {
      console.log(issue.path, issue.message);
    });
    return res.status(400).send({
      message: 'Invalid data',
      error: error,
    });
  }

  const updatedNeighborhood = await NeighborhoodService.updateNeighborhood(neighborhoodId, data);

  if (!updatedNeighborhood) {
    return res.status(404).send({
      message: 'Neighborhood not found',
    });
  }

  return res.status(200).send({
    message: 'Neighborhood updated',
    data: updatedNeighborhood,
  });
};

export const deleteNeighborhood: AsyncController = async (req, res) => {
  const { neighborhoodId } = req.params;

  const deletedNeighborhood = await NeighborhoodService.deleteNeighborhood(neighborhoodId);

  if (!deletedNeighborhood) {
    return res.status(404).send({
      message: 'Neighborhood not found',
    });
  }

  return res.status(200).send({
    message: 'Neighborhood deleted',
    data: deletedNeighborhood,
  });
};

export const getNeighborhoodsByCityState: AsyncController = async (req, res) => {
  const { state } = req.params;

  const neighborhoods = await NeighborhoodService.getNeighborhoodsByCity(state);

  return res.status(200).send({
    message: 'Neighborhoods found',
    data: neighborhoods,
  });
};
