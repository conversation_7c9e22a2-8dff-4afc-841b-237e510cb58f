import { Router } from 'express';
import {
  blockPayment,
  cancelGigstack,
  getPendingPayments,
  paymentLink,
  reminderPayment,
  updateStatus,
  markaspaid,
  updateMarkaspaid,
} from '../controllers/gigstack';

const gighooks = Router();

gighooks.post('/paymentLink/:region', paymentLink);
gighooks.get('/pending/:region', getPendingPayments);
gighooks.post('/update/:regionCode', updateStatus);
gighooks.post('/blockPayment', blockPayment);
gighooks.post('/reminders', reminderPayment);
gighooks.post('/cancel', cancelGigstack);
gighooks.post('/markaspaid', markaspaid);
gighooks.get('/updateMarkaspaid', updateMarkaspaid);

export default gighooks;
