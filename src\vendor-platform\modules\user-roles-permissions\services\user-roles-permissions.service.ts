import { Types } from 'mongoose';
import UserRolesPermissions, { 
  IUserRolesPermissions, 
  GlobalUserRole, 
  UserType,
  IModulePermissions 
} from '../models/user-roles-permissions.model';

export class UserRolesPermissionsService {
  
  /**
   * Crear permisos para un usuario
   */
  async createUserPermissions(data: {
    userId: Types.ObjectId;
    userType: UserType;
    organizationId?: Types.ObjectId;
    roles: GlobalUserRole[];
    createdBy: Types.ObjectId;
  }): Promise<IUserRolesPermissions> {
    const permissions = this.getDefaultPermissionsByUserType(data.userType);
    
    const userPermissions = new UserRolesPermissions({
      userId: data.userId,
      userType: data.userType,
      organizationId: data.organizationId,
      roles: data.roles,
      permissions,
      createdBy: data.createdBy,
      updatedBy: data.createdBy,
    });
    
    return await userPermissions.save();
  }
  
  /**
   * Obtener permisos de un usuario por ID
   */
  async getUserPermissions(userId: Types.ObjectId): Promise<IUserRolesPermissions | null> {
    return await UserRolesPermissions.findOne({ userId, isActive: true })
      .populate('user')
      .populate('organization');
  }
  
  /**
   * Actualizar permisos de un usuario
   */
  async updateUserPermissions(
    userId: Types.ObjectId,
    updateData: Partial<IUserRolesPermissions>,
    updatedBy: Types.ObjectId
  ): Promise<IUserRolesPermissions | null> {
    return await UserRolesPermissions.findOneAndUpdate(
      { userId, isActive: true },
      { ...updateData, updatedBy, updatedAt: new Date() },
      { new: true }
    );
  }
  
  /**
   * Verificar si un usuario tiene un permiso específico
   */
  async hasPermission(
    userId: Types.ObjectId,
    module: string,
    permission: string
  ): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    
    if (!userPermissions || !userPermissions.isActive) {
      return false;
    }
    
    const modulePermissions = (userPermissions.permissions as any)[module];
    if (!modulePermissions) {
      return false;
    }
    
    return modulePermissions[permission] === true;
  }
  
  /**
   * Verificar si un usuario tiene alguno de los roles especificados
   */
  async hasAnyRole(userId: Types.ObjectId, roles: GlobalUserRole[]): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    
    if (!userPermissions || !userPermissions.isActive) {
      return false;
    }
    
    return userPermissions.roles.some(role => roles.includes(role));
  }
  
  /**
   * Obtener usuarios por tipo
   */
  async getUsersByType(userType: UserType): Promise<IUserRolesPermissions[]> {
    return await UserRolesPermissions.find({ userType, isActive: true })
      .populate('user')
      .populate('organization');
  }
  
  /**
   * Obtener usuarios por organización
   */
  async getUsersByOrganization(organizationId: Types.ObjectId): Promise<IUserRolesPermissions[]> {
    return await UserRolesPermissions.find({ organizationId, isActive: true })
      .populate('user')
      .populate('organization');
  }
  
  /**
   * Desactivar permisos de un usuario
   */
  async deactivateUserPermissions(userId: Types.ObjectId, updatedBy: Types.ObjectId): Promise<boolean> {
    const result = await UserRolesPermissions.findOneAndUpdate(
      { userId },
      { isActive: false, updatedBy, updatedAt: new Date() }
    );
    
    return !!result;
  }
  
  /**
   * Obtener permisos por defecto según el tipo de usuario
   */
  private getDefaultPermissionsByUserType(userType: UserType) {
    const basePermissions = {
      create: false,
      read: false,
      update: false,
      delete: false,
      manageAll: false,
    };
    
    switch (userType) {
      case 'ocn':
        return this.getOCNPermissions();
      case 'organization':
        return this.getOrganizationPermissions();
      case 'workshop':
        return this.getWorkshopPermissions();
      case 'company':
        return this.getCompanyPermissions();
      case 'company-gestor':
        return this.getCompanyGestorPermissions();
      default:
        return this.getDefaultPermissions();
    }
  }
  
  /**
   * Permisos para usuarios OCN (administradores de la plataforma)
   */
  private getOCNPermissions() {
    return {
      fleetOrders: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllOrders: true,
        manageDispersion: true,
        uploadEvidence: true,
        manageSLAs: true,
      },
      organizations: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllOrganizations: true,
        manageUsers: true,
      },
      users: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllUsers: true,
        manageRoles: true,
        inviteUsers: true,
      },
      workshops: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllWorkshops: true,
        manageSchedules: true,
      },
      services: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllServices: true,
        manageServiceTypes: true,
      },
      appointments: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllAppointments: true,
        manageSchedules: true,
      },
      stockVehicles: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllVehicles: true,
        manageStock: true,
      },
      companies: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllCompanies: true,
        manageCities: true,
        manageCrews: true,
      },
      gestores: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllGestores: true,
        manageProcedimientos: true,
        manageTramites: true,
      },
      correctiveMaintenance: {
        create: true,
        read: true,
        update: true,
        delete: true,
        manageAll: true,
        viewAllMaintenance: true,
        scheduleMaintenances: true,
      },
      systemAdmin: {
        viewSystemLogs: true,
        manageSystemSettings: true,
        viewAnalytics: true,
        exportData: true,
      },
    };
  }
  
  /**
   * Permisos para usuarios de organización
   */
  private getOrganizationPermissions() {
    return {
      fleetOrders: {
        create: false,
        read: true,
        update: false,
        delete: false,
        manageAll: false,
        viewAllOrders: false,
        manageDispersion: false,
        uploadEvidence: false,
        manageSLAs: false,
      },
      organizations: {
        create: false,
        read: true,
        update: false,
        delete: false,
        manageAll: false,
        viewAllOrganizations: false,
        manageUsers: false,
      },
      users: {
        create: false,
        read: true,
        update: false,
        delete: false,
        manageAll: false,
        viewAllUsers: false,
        manageRoles: false,
        inviteUsers: false,
      },
      workshops: {
        create: false,
        read: true,
        update: true,
        delete: false,
        manageAll: false,
        viewAllWorkshops: false,
        manageSchedules: true,
      },
      services: {
        create: true,
        read: true,
        update: true,
        delete: false,
        manageAll: false,
        viewAllServices: false,
        manageServiceTypes: false,
      },
      appointments: {
        create: true,
        read: true,
        update: true,
        delete: false,
        manageAll: false,
        viewAllAppointments: false,
        manageSchedules: true,
      },
      stockVehicles: {
        create: false,
        read: true,
        update: false,
        delete: false,
        manageAll: false,
        viewAllVehicles: false,
        manageStock: false,
      },
      companies: {
        create: false,
        read: true,
        update: false,
        delete: false,
        manageAll: false,
        viewAllCompanies: false,
        manageCities: false,
        manageCrews: false,
      },
      gestores: {
        create: false,
        read: true,
        update: false,
        delete: false,
        manageAll: false,
        viewAllGestores: false,
        manageProcedimientos: false,
        manageTramites: false,
      },
      correctiveMaintenance: {
        create: false,
        read: true,
        update: false,
        delete: false,
        manageAll: false,
        viewAllMaintenance: false,
        scheduleMaintenances: false,
      },
      systemAdmin: {
        viewSystemLogs: false,
        manageSystemSettings: false,
        viewAnalytics: false,
        exportData: false,
      },
    };
  }
  
  /**
   * Permisos básicos para otros tipos de usuario
   */
  private getWorkshopPermissions() {
    // Implementar permisos específicos para talleres
    return this.getDefaultPermissions();
  }
  
  private getCompanyPermissions() {
    // Implementar permisos específicos para empresas
    return this.getDefaultPermissions();
  }
  
  private getCompanyGestorPermissions() {
    // Implementar permisos específicos para gestores de empresa
    return this.getDefaultPermissions();
  }
  
  private getDefaultPermissions() {
    return {
      fleetOrders: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllOrders: false,
        manageDispersion: false,
        uploadEvidence: false,
        manageSLAs: false,
      },
      organizations: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllOrganizations: false,
        manageUsers: false,
      },
      users: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllUsers: false,
        manageRoles: false,
        inviteUsers: false,
      },
      workshops: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllWorkshops: false,
        manageSchedules: false,
      },
      services: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllServices: false,
        manageServiceTypes: false,
      },
      appointments: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllAppointments: false,
        manageSchedules: false,
      },
      stockVehicles: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllVehicles: false,
        manageStock: false,
      },
      companies: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllCompanies: false,
        manageCities: false,
        manageCrews: false,
      },
      gestores: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllGestores: false,
        manageProcedimientos: false,
        manageTramites: false,
      },
      correctiveMaintenance: {
        create: false,
        read: false,
        update: false,
        delete: false,
        manageAll: false,
        viewAllMaintenance: false,
        scheduleMaintenances: false,
      },
      systemAdmin: {
        viewSystemLogs: false,
        manageSystemSettings: false,
        viewAnalytics: false,
        exportData: false,
      },
    };
  }
}
