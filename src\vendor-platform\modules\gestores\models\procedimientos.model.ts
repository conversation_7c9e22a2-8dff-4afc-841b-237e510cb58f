import { Schema, Types, Document } from 'mongoose';
import vendorDB from '@/vendor-platform/db';
import { ITramite } from './tramites.model';
import { IGestores } from './gestores.model';

export interface IDocumentUpload {
  documentId: Types.ObjectId;
  name: string;
  uploadDate: Date;
  documentType: string;
}

export interface IProcedimiento extends Document {
  _id: Types.ObjectId;
  tramiteId: Types.ObjectId | ITramite;
  gestorId: Types.ObjectId | IGestores;
  vehicleId: Types.ObjectId;
  status:
    | 'Pendiente'
    | 'Agendado'
    | 'En Proceso'
    | 'Esperando Documentos'
    | 'Completado'
    | 'Cancelado'
    | 'Fallido';
  scheduledDate?: Date;
  startDate?: Date;
  completionDate?: Date;
  notes?: string;
  cost?: number;
  token?: string;
  appointmentId?: Types.ObjectId;
  uploadedDocuments?: IDocumentUpload[];
  createdAt: Date;
  updatedAt: Date;
}

const ProcedimientoSchema = new Schema<IProcedimiento>(
  {
    tramiteId: {
      type: Schema.Types.ObjectId,
      ref: 'Tramites',
      required: [true, 'El tipo de trámite es obligatorio.'],
      index: true,
    },
    gestorId: {
      type: Schema.Types.ObjectId,
      ref: 'Gestores',
      required: [true, 'El gestor asignado es obligatorio.'],
      index: true,
    },
    vehicleId: {
      type: Schema.Types.ObjectId,
      ref: 'StockVehicle',
      required: [true, 'El ID del vehículo es obligatorio.'],
      index: true,
    },
    status: {
      type: String,
      required: true,
      enum: [
        'Pendiente',
        'Agendado',
        'En Proceso',
        'Esperando Documentos',
        'Completado',
        'Cancelado',
        'Fallido',
      ],
      default: 'Pendiente',
      index: true,
    },
    scheduledDate: {
      type: Date,
      required: false,
    },
    startDate: {
      type: Date,
      required: false,
    },
    completionDate: {
      type: Date,
      required: false,
    },
    notes: {
      type: String,
      trim: true,
      required: false,
    },
    token: {
      type: String,
      unique: true,
      default: function () {
        const digits = Math.floor(100000 + Math.random() * 900000).toString();
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const letter1 = letters.charAt(Math.floor(Math.random() * letters.length));
        const letter2 = letters.charAt(Math.floor(Math.random() * letters.length));

        return `${digits}${letter1}${letter2}`;
      },
    },
    cost: { type: Number },
    appointmentId: { type: Schema.Types.ObjectId, ref: 'Appointment' },
    uploadedDocuments: [
      {
        _id: false,
        documentId: { type: Schema.Types.ObjectId, ref: 'Document', required: true },
        name: { type: String, required: true },
        uploadDate: { type: Date, default: Date.now },
        documentType: { type: String, required: true },
      },
    ],
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        delete ret.token;
        return ret;
      },
    },
  }
);

export const Procedimiento = vendorDB.model<IProcedimiento>('Procedimiento', ProcedimientoSchema);
