import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import {
  createOrganization,
  getOrganizations,
  getOrganizationById,
  getOrganizationWorshops,
  getOrganizationServices,
  getOrganizationSchedule,
  updateOrganizationSchedule,
} from '../controllers/organizations.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  createOrganizationOverride,
  getOrganizationOverrides,
} from '../../scheduleOverride/controllers/scheduleOverride.controller';
import { createServiceType, getAllServicesType } from '../../serviceType/controllers/serviceType.controller';
import { getOrganizationAppointments } from '../../workshop/controllers/workshop-appointments.controller';

const organizationsRouter = Router();

const organizationsUrl = '/organizations';

organizationsRouter.post(organizationsUrl, verifyTokenVendorPlatform, errorHandlerV2(createOrganization));
organizationsRouter.get(organizationsUrl, verifyTokenVendorPlatform, errorHandlerV2(getOrganizations));
organizationsRouter.get(
  organizationsUrl + '/:organizationId',
  verifyTokenVendorPlatform,
  errorHandlerV2(getOrganizationById)
);
organizationsRouter.get(
  organizationsUrl + '/:organizationId/workshops',
  verifyTokenVendorPlatform,
  errorHandlerV2(getOrganizationWorshops)
);

organizationsRouter.get(
  organizationsUrl + '/:organizationId/services',
  verifyTokenVendorPlatform,
  errorHandlerV2(getOrganizationServices)
);

organizationsRouter.post(
  organizationsUrl + '/:organizationId/overrides',
  verifyTokenVendorPlatform,
  errorHandlerV2(createOrganizationOverride)
);

organizationsRouter.get(
  organizationsUrl + '/:organizationId/overrides',
  verifyTokenVendorPlatform,
  errorHandlerV2(getOrganizationOverrides)
);

organizationsRouter.get(
  organizationsUrl + '/:organizationId/schedule',
  verifyTokenVendorPlatform,
  errorHandlerV2(getOrganizationSchedule)
);

organizationsRouter.put(
  organizationsUrl + '/:organizationId/schedule',
  verifyTokenVendorPlatform,
  errorHandlerV2(updateOrganizationSchedule)
);

organizationsRouter.post(
  `${organizationsUrl}/:organizationId/service-types`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createServiceType)
);
organizationsRouter.get(
  `${organizationsUrl}/:organizationId/service-types`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getAllServicesType)
);

// getOrganizationAppointments

organizationsRouter.get(
  organizationsUrl + '/:organizationId/appointments',
  verifyTokenVendorPlatform,
  errorHandlerV2(getOrganizationAppointments)
);

export default organizationsRouter;
