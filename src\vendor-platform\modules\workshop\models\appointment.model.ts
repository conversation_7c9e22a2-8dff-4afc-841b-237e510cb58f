import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';
import { IServiceType } from '../../serviceType/models/serviceType.model';
import { IWorkshop } from './workshops.model';
import { IOrganization } from '../../organizations/models/organization.model';
import { IAssociate } from '@/models/associateSchema';
import { IStockVehicle } from '@/models/StockVehicleSchema';

export enum AppointmentVendorStatus {
  scheduled = 'scheduled',
  completed = 'completed',
  canceled = 'canceled',
  rescheduled = 'rescheduled',
  'not-attended' = 'not-attended',
}

export interface IAppointment extends Document {
  workshop: IWorkshop;
  workshopId: mongoose.Types.ObjectId;
  organizationId: mongoose.Types.ObjectId;
  organization: IOrganization;
  associateId: string;
  associate: IAssociate;
  stockId: string;
  stock: IStockVehicle;
  startTime: Date;
  endTime: Date;
  data: {
    registeredKm: number;
    maintenanceNumber: number;
    type: 'preventive' | 'corrective';
    correctiveMaintenanceOrderId?: mongoose.Types.ObjectId; // Link to corrective maintenance order
    failureDescription?: string; // For corrective maintenance
    urgencyLevel?: 'low' | 'medium' | 'high' | 'critical'; // For corrective maintenance
  };
  // status: 'scheduled' | 'completed' | 'cancelled';
  // status: (typeof statusEnumArray)[number];
  status: keyof typeof AppointmentVendorStatus;
  statusHistory: {
    status: (typeof statusEnumArray)[number];
    date: Date;
  }[];
  serviceTypeId: mongoose.Types.ObjectId;
  duration: number;
  service: IServiceType;
}

// const statusEnumArray = ['scheduled', 'completed', 'canceled', 'rescheduled'] as const;
const statusEnumArray = Object.values(AppointmentVendorStatus);

const AppointmentSchema = new Schema(
  {
    workshopId: { type: Schema.Types.ObjectId, ref: 'Workshop', required: true },
    organizationId: { type: Schema.Types.ObjectId, ref: 'Organization', required: true },
    associateId: { type: String, required: true },
    stockId: { type: String, required: true },
    data: {
      registeredKm: { type: Number },
      maintenanceNumber: { type: Number }, // Agregar el número de mantenimiento
      type: { type: String, enum: ['preventive', 'corrective'], required: true, default: 'preventive' },
      correctiveMaintenanceOrderId: { type: Schema.Types.ObjectId, ref: 'CorrectiveMaintenanceOrder' },
      failureDescription: { type: String },
      urgencyLevel: { type: String, enum: ['low', 'medium', 'high', 'critical'], default: 'medium' },
    },
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    status: {
      type: String,
      enum: statusEnumArray,
      default: 'scheduled',
    },

    statusHistory: [
      {
        status: {
          type: String,
          enum: statusEnumArray,
          default: AppointmentVendorStatus.scheduled,
        },
        date: {
          type: Date,
          default: Date.now,
        },
      },
    ],

    serviceTypeId: { type: Schema.Types.ObjectId, ref: 'ServiceType', required: true },
    duration: { type: Number, required: true }, // Guardar la duración en el momento de la creación
  },
  {
    timestamps: true,
  }
);

// Add virtuals to populate the service field

AppointmentSchema.virtual('service', {
  ref: 'ServiceType',
  localField: 'serviceTypeId',
  foreignField: '_id',
  justOne: true,
});

AppointmentSchema.virtual('serviceType', {
  ref: 'ServiceType',
  localField: 'serviceTypeId',
  foreignField: '_id',
  justOne: true,
});

AppointmentSchema.virtual('workshop', {
  ref: 'Workshop',
  localField: 'workshopId',
  foreignField: '_id',
  justOne: true,
});

AppointmentSchema.virtual('organization', {
  ref: 'Organization',
  localField: 'organizationId',
  foreignField: '_id',
  justOne: true,
});

AppointmentSchema.set('toObject', { virtuals: true });
AppointmentSchema.set('toJSON', { virtuals: true });

AppointmentSchema.index({ serviceTypeId: 1 });
AppointmentSchema.index({ workshopId: 1, startTime: 1 });
AppointmentSchema.index({ organizationId: 1 });
AppointmentSchema.index({ associateId: 1 });
AppointmentSchema.index({ stockId: 1 });

// haz unico la siguiente convinación: associateId, stockId, data.maintenanceNumber
// esto para tener un registro único de cada mantenimiento correspondiente a cada vehículo
AppointmentSchema.index({ associateId: 1, stockId: 1, 'data.maintenanceNumber': 1 }, { unique: true });

export const AppointmentVendor = vendorDB.model<IAppointment>('Appointment', AppointmentSchema);
