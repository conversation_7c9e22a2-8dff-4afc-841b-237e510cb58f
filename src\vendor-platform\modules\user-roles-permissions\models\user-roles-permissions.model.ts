import vendorDB from '@/vendor-platform/db';
import { Types, Schema, Document } from 'mongoose';
import { userVendorTypes } from '../../users/models/user.model';

export type UserType = (typeof userVendorTypes)[number];

export enum GlobalUserRole {
  // OCN Roles
  OCN_SUPER_ADMIN = 'ocn_super_admin',
  OCN_ADMIN = 'ocn_admin',
  OCN_FLEET_MANAGER = 'ocn_fleet_manager',
  OCN_VIEWER = 'ocn_viewer',
  
  // Organization Roles
  ORG_ADMIN = 'org_admin',
  ORG_MANAGER = 'org_manager',
  ORG_SUPERVISOR = 'org_supervisor',
  ORG_OPERATOR = 'org_operator',
  ORG_VIEWER = 'org_viewer',
  
  // Workshop Roles
  WORKSHOP_ADMIN = 'workshop_admin',
  WORKSHOP_MANAGER = 'workshop_manager',
  WORKSHOP_TECHNICIAN = 'workshop_technician',
  
  // Company Roles
  COMPANY_ADMIN = 'company_admin',
  COMPANY_MANAGER = 'company_manager',
  COMPANY_GESTOR = 'company_gestor',
}

export interface IModulePermissions {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
  manageAll?: boolean; // Para usuarios OCN que pueden gestionar todo
}

export interface IUserRolesPermissions extends Document {
  userId: Types.ObjectId;
  userType: UserType;
  organizationId?: Types.ObjectId; // Solo para usuarios tipo 'organization'
  roles: GlobalUserRole[];
  permissions: {
    // Fleet Orders Module
    fleetOrders: IModulePermissions & {
      viewAllOrders: boolean;
      manageDispersion: boolean;
      uploadEvidence: boolean;
      manageSLAs: boolean;
    };
    
    // Organizations Module
    organizations: IModulePermissions & {
      viewAllOrganizations: boolean;
      manageUsers: boolean;
    };
    
    // Users Module
    users: IModulePermissions & {
      viewAllUsers: boolean;
      manageRoles: boolean;
      inviteUsers: boolean;
    };
    
    // Workshops Module
    workshops: IModulePermissions & {
      viewAllWorkshops: boolean;
      manageSchedules: boolean;
    };
    
    // Services Module
    services: IModulePermissions & {
      viewAllServices: boolean;
      manageServiceTypes: boolean;
    };
    
    // Appointments Module
    appointments: IModulePermissions & {
      viewAllAppointments: boolean;
      manageSchedules: boolean;
    };
    
    // Stock Vehicles Module
    stockVehicles: IModulePermissions & {
      viewAllVehicles: boolean;
      manageStock: boolean;
    };
    
    // Companies Module
    companies: IModulePermissions & {
      viewAllCompanies: boolean;
      manageCities: boolean;
      manageCrews: boolean;
    };
    
    // Gestores Module
    gestores: IModulePermissions & {
      viewAllGestores: boolean;
      manageProcedimientos: boolean;
      manageTramites: boolean;
    };
    
    // Corrective Maintenance Module
    correctiveMaintenance: IModulePermissions & {
      viewAllMaintenance: boolean;
      scheduleMaintenances: boolean;
    };
    
    // System Administration
    systemAdmin: {
      viewSystemLogs: boolean;
      manageSystemSettings: boolean;
      viewAnalytics: boolean;
      exportData: boolean;
    };
  };
  isActive: boolean;
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const modulePermissionsSchema = new Schema({
  create: { type: Boolean, default: false },
  read: { type: Boolean, default: false },
  update: { type: Boolean, default: false },
  delete: { type: Boolean, default: false },
  manageAll: { type: Boolean, default: false },
}, { _id: false });

const userRolesPermissionsSchema = new Schema<IUserRolesPermissions>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'Users',
      required: true,
      unique: true,
    },
    
    userType: {
      type: String,
      enum: userVendorTypes,
      required: true,
    },
    
    organizationId: {
      type: Schema.Types.ObjectId,
      ref: 'Organization',
      required: function(this: IUserRolesPermissions) {
        return this.userType === 'organization';
      },
    },
    
    roles: [{
      type: String,
      enum: Object.values(GlobalUserRole),
    }],
    
    permissions: {
      fleetOrders: {
        ...modulePermissionsSchema.obj,
        viewAllOrders: { type: Boolean, default: false },
        manageDispersion: { type: Boolean, default: false },
        uploadEvidence: { type: Boolean, default: false },
        manageSLAs: { type: Boolean, default: false },
      },
      
      organizations: {
        ...modulePermissionsSchema.obj,
        viewAllOrganizations: { type: Boolean, default: false },
        manageUsers: { type: Boolean, default: false },
      },
      
      users: {
        ...modulePermissionsSchema.obj,
        viewAllUsers: { type: Boolean, default: false },
        manageRoles: { type: Boolean, default: false },
        inviteUsers: { type: Boolean, default: false },
      },
      
      workshops: {
        ...modulePermissionsSchema.obj,
        viewAllWorkshops: { type: Boolean, default: false },
        manageSchedules: { type: Boolean, default: false },
      },
      
      services: {
        ...modulePermissionsSchema.obj,
        viewAllServices: { type: Boolean, default: false },
        manageServiceTypes: { type: Boolean, default: false },
      },
      
      appointments: {
        ...modulePermissionsSchema.obj,
        viewAllAppointments: { type: Boolean, default: false },
        manageSchedules: { type: Boolean, default: false },
      },
      
      stockVehicles: {
        ...modulePermissionsSchema.obj,
        viewAllVehicles: { type: Boolean, default: false },
        manageStock: { type: Boolean, default: false },
      },
      
      companies: {
        ...modulePermissionsSchema.obj,
        viewAllCompanies: { type: Boolean, default: false },
        manageCities: { type: Boolean, default: false },
        manageCrews: { type: Boolean, default: false },
      },
      
      gestores: {
        ...modulePermissionsSchema.obj,
        viewAllGestores: { type: Boolean, default: false },
        manageProcedimientos: { type: Boolean, default: false },
        manageTramites: { type: Boolean, default: false },
      },
      
      correctiveMaintenance: {
        ...modulePermissionsSchema.obj,
        viewAllMaintenance: { type: Boolean, default: false },
        scheduleMaintenances: { type: Boolean, default: false },
      },
      
      systemAdmin: {
        viewSystemLogs: { type: Boolean, default: false },
        manageSystemSettings: { type: Boolean, default: false },
        viewAnalytics: { type: Boolean, default: false },
        exportData: { type: Boolean, default: false },
      },
    },
    
    isActive: {
      type: Boolean,
      default: true,
    },
    
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'Users',
      required: true,
    },
    
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'Users',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Índices para búsquedas eficientes
userRolesPermissionsSchema.index({ userId: 1 }, { unique: true });
userRolesPermissionsSchema.index({ userType: 1 });
userRolesPermissionsSchema.index({ organizationId: 1 });
userRolesPermissionsSchema.index({ isActive: 1 });

// Virtual para popular el usuario
userRolesPermissionsSchema.virtual('user', {
  ref: 'Users',
  localField: 'userId',
  foreignField: '_id',
  justOne: true,
});

// Virtual para popular la organización
userRolesPermissionsSchema.virtual('organization', {
  ref: 'Organization',
  localField: 'organizationId',
  foreignField: '_id',
  justOne: true,
});

userRolesPermissionsSchema.set('toJSON', { virtuals: true });
userRolesPermissionsSchema.set('toObject', { virtuals: true });

const UserRolesPermissions = vendorDB.model<IUserRolesPermissions>(
  'UserRolesPermissions',
  userRolesPermissionsSchema
);

export default UserRolesPermissions;
