import axios from 'axios';
import { associatePaymentsConsts } from '../constants';
import AssociatePayments from '../models/associatePayments';
import moment from 'moment';
import { ValidRegion, tokenAssignGigstack } from './tokenAssignGigstack';
import RegionsPayments from '../models/regionPaymentsSchema';
import Associate from '../models/associateSchema';
import { BlockPayment } from '../types&interfaces/interfaces';
// import { gpsActionAndStock } from './gpsActions';

export const createRemindersGigstack = async (regionCode: ValidRegion) => {
  let succeded = 0;
  let failed = 0;
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const { data } = await axios.get(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/list?limit=100&status=requires_payment_method',
      { headers: { Authorization: `Bearer ${gigToken}` } }
    );

    const region = await RegionsPayments.findOne({ region: regionCode });

    if (!region) {
      return new Error('Non valid region');
    }

    await Promise.all(
      data.data.map(async (payment: any) => {
        if (payment.status === associatePaymentsConsts.gigstackValues.pending) {
          const lastWeek = moment().subtract(8, 'days');
          const today = moment();
          const paymentDate = moment(payment.timestamp);

          if (paymentDate.isBetween(lastWeek, today, null, '[]')) {
            const associatePayments = await AssociatePayments.findOne({
              associateEmail: payment.client.email,
            });
            if (!associatePayments) {
              return new Error('Tabla de pagos no encontrada');
            }

            if (!associatePayments.model) {
              return new Error(associatePaymentsConsts.errors.modelNotFound);
            }
            const associate = await Associate.findById(associatePayments.associateId);

            if (!associate) {
              return new Error('Asociado no encontrado');
            }
            const isRentPayment = payment.items.some(
              (item: any) => item.id === region.models[associatePayments.model as string].rentID
            );
            if (isRentPayment) {
              const config = {
                headers: {
                  Authorization: `Bearer ${gigToken}`,
                  'Content-Type': 'application/json',
                },
              };
              const response = await axios.post(
                'https://serverless.twilio.com/v1/Services/ZS1373873cddf33fec54bb644f970b1997/Builds/ZBba5068e93e25c8df9354b77dfdac3891/whatsapp',
                payment,
                config
              );
              return response.data;
            }
            return console.log('Pagos no pendiente');
          }
        }
        return console.log('Pagos no pendientes');
      })
    );

    return console.log(`Enviados ${succeded} recordatorios con exito, no enviados ${failed}`);
  } catch (error) {
    failed++;
    return console.error('Ha ocurrido un error:', error);
    // Puedes decidir qué hacer con el error aquí, como lanzarlo nuevamente o manejarlo de otra manera.
  }
};

export const createBlockPayment = async (regionCode: string) => {
  let succeded = 0;
  let failed = 0;
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);
    const region = await RegionsPayments.findOne({ region: regionCode });
    if (!region) {
      return new Error('Non valid region');
    }

    const { data } = await axios.get(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/list?limit=100&status=requires_payment_method',
      { headers: { Authorization: `Bearer ${gigToken}` } }
    );

    await Promise.all(
      data.data.map(async (payment: any) => {
        const lastWeek = moment().subtract(8, 'days');
        const today = moment();
        const paymentDate = moment(payment.timestamp);

        if (paymentDate.isBetween(lastWeek, today, null, '[]')) {
          const associatePayments = await AssociatePayments.findOne({ associateEmail: payment.client.email });
          if (!associatePayments) {
            return new Error('Tabla de pagos no encontrada');
          }
          const associate = await Associate.findById(associatePayments.associateId);

          if (!associate) {
            return new Error('Asociado no encontrado');
          }
          if (!associatePayments.model) {
            return new Error(associatePaymentsConsts.errors.modelNotFound);
          }

          const isRentPayment = payment.items.some(
            (item: any) => item.id === region.models[associatePayments.model as string].rentID
          );
          if (isRentPayment) {
            // console.log('Pago aqui', payment.internalItems, region, region.reactivationFee);
            const gigPayment = {
              client: {
                name: `${associate.firstName} ${associate.lastName}`,
                email: associate.email,
              },
              items: [
                {
                  quantity: 1,
                  id: region.reactivationFee,
                },
              ],
              currency: 'MXN',
              methodsTypesOptions: ['bank'],
            };
            const config = {
              headers: {
                Authorization: `Bearer ${gigToken}`,
                'Content-Type': 'application/json',
              },
            };
            const responseGigstack = await axios.post(
              'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/create',
              gigPayment,
              config
            );
            const blockObj: BlockPayment =
              associatePayments.newPaymentsArr[associatePayments.newPaymentsArr.length - 1].blockPayment;
            if (blockObj) {
              associatePayments.block = true;
              const totalItems = responseGigstack.data.data.items.reduce(
                (acc: number, curr: any) => acc + curr.total,
                0
              );
              associatePayments.balance -= totalItems;
              //En desarrollo!!!!
              // gpsActionAndStock('falta de pago');
            }
            await associatePayments.save();
            succeded++;
            return data.data;
          }
          return data.data;
        }
        return console.log('No pagos pendientes!');
      })
    );
    return console.log(`Enviados ${succeded} pagos de bloqueo con exito, no enviados ${failed}`);
  } catch (error) {
    failed++;
    return console.error(error);
  }
};
