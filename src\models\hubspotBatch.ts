import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const hubspotBatch = new Schema({
  requestId: {
    type: Schema.Types.ObjectId,
    ref: 'AdmissionRequests',
    required: true,
  },
  hubspotId: {
    type: String,
  },
  dealId: {
    type: String,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
hubspotBatch.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const HubspotBatch = model('HubspotBatch', hubspotBatch);

export default HubspotBatch;
