/* import { genericMessages, stockVehiclesText } from '../../constants';
import StockVehicle from '../../models/StockVehicleSchema';
import ServiceStock from '../../models/serviceStock';
import { AsyncController } from '../../types&interfaces/types';

export const sendInServiceVehicleId: AsyncController = async (req, res) => {
  const { id } = req.params;
  const { dateIn, dateOut } = req.body;

  if (!dateIn || !dateOut)
    return res.status(400).send({ message: stockVehiclesText.errors.dischargedMissing });

  try {
    const stockVehicle = await StockVehicle.findById(id);
    if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    const newService = {
      dateIn,
      dateOut,
      comments: req.body.comments,
      vehicleId: stockVehicle._id,
    };

    // stockVehicle.inServiceData = newService;

    // stockVehicle.inServiceDataArray.push(newService);

    const service = new ServiceStock(newService);

    await service.save();

    stockVehicle.updateHistory.push({
      step: 'VEHICULO ENVIADO A TALLER',
      userId: req.userId.userId,
      description: '',
    });

    stockVehicle.status = 'in-service';
    await stockVehicle.save();
    return res.status(200).send({ message: stockVehiclesText.success.dischargedVehicle, stockVehicle });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};
 */
