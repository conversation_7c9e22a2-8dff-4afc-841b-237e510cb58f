import {
  sendHomeVisitAppointmentNoShowMessageEmail,
  transporter,
} from '../../modules/platform_connections/emailFunc';

jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn(),
  })),
}));

describe('sendHomeVisitAppointmentNoShowMessageEmail', () => {
  let mockSendMail: jest.Mock;

  beforeEach(() => {
    mockSendMail = transporter.sendMail as jest.Mock;
  });

  it('should send an email with correct parameters', async () => {
    mockSendMail.mockResolvedValue({ messageId: 'test-id' });

    const emailDetails = {
      name: '<PERSON>',
      email: '<EMAIL>',
      customerWebAppLink: 'https://example.com/reschedule',
      requestId: '123',
    };

    const response = await sendHomeVisitAppointmentNoShowMessageEmail(emailDetails);

    expect(mockSendMail).toHaveBeenCalledTimes(1);
    expect(mockSendMail).toHaveBeenCalledWith(
      expect.objectContaining({
        from: expect.any(String),
        to: emailDetails.email,
        subject: expect.stringContaining('OCN Home Visit'),
        html: expect.stringContaining(emailDetails.customerWebAppLink),
      })
    );
    expect(response).toEqual({ messageId: 'test-id' });
  });

  it('should handle errors when sending email fails', async () => {
    mockSendMail.mockRejectedValue(new Error('Email sending failed'));

    const emailDetails = {
      name: 'John Doe',
      email: '<EMAIL>',
      customerWebAppLink: 'https://example.com/reschedule',
      requestId: '123',
    };

    await expect(sendHomeVisitAppointmentNoShowMessageEmail(emailDetails)).rejects.toThrow(
      'Email sending failed'
    );
  });
});
