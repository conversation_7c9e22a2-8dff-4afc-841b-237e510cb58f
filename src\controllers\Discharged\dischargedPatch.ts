import { uploadFile } from '../../aws/s3';
import { genericMessages, stockVehiclesText } from '../../constants';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleSubCategoryType,
} from '../../models/StockVehicleSchema';
import Document from '../../models/documentSchema';
import { removeEmptySpacesNameFile } from '../../services/removeEmptySpaces';
import { AsyncController } from '../../types&interfaces/types';
import Associate from '../../models/associateSchema';

type FieldName1 = 'platesDischargedDoc' | 'dictamenDoc' | 'reportDoc';

export const dischargedVehicleById: AsyncController = async (req, res) => {
  const { id } = req.params;
  const { comments, reason, date, subCategory } = req.body;

  if (!reason || !date) return res.status(400).send({ message: stockVehiclesText.errors.dischargedMissing });

  const files = req.files as { [fieldname: string]: Express.Multer.File[] };

  const documentFields = [
    { fieldName: 'platesDischargedDoc' },
    { fieldName: 'dictamenDoc' },
    { fieldName: 'reportDoc' },
  ];

  try {
    const stockVehicle = await StockVehicle.findById(id);
    if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    stockVehicle.dischargedData = {
      reason: reason,
      date,
    };
    if (comments) {
      stockVehicle.dischargedData.comments = comments;
    }

    if (files) {
      for (const field of documentFields) {
        if (field.fieldName in files) {
          const fieldConversion = field.fieldName as unknown as keyof typeof files;
          const file = files[fieldConversion][0];
          const removeSpacesFileName = removeEmptySpacesNameFile(file);
          const s3Path = `stock/${stockVehicle.carNumber}/dischargedDocs/${field.fieldName}/`;
          const command = await uploadFile(file, removeSpacesFileName, s3Path);
          const documentPath = command.input.Key;

          const document = new Document({
            originalName: removeSpacesFileName,
            path: documentPath,
            vehicleId: stockVehicle._id,
          });
          stockVehicle.dischargedData[field.fieldName as FieldName1] = document._id;
          await document.save();
        }
      }
    }

    stockVehicle.updateHistory.push({
      step: 'VEHICULO DADO DE BAJA',
      userId: req.userId.userId,
      description: 'Razón: ' + reason,
    });

    stockVehicle.status = 'discharged';
    stockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    stockVehicle.category = VehicleCategory.withdrawn;
    stockVehicle.subCategory = subCategory as VehicleSubCategoryType;
    await stockVehicle.save();
    const driversId = stockVehicle.drivers.length - 1;
    await Associate.updateOne({ _id: stockVehicle.drivers[driversId] }, { $set: { active: false } });
    return res.status(200).send({ message: stockVehiclesText.success.dischargedVehicle, stockVehicle });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};
