interface IVehicleMaintenanceAppointmentEmailTemplate {
  name: string;
}

interface IVehicleMaintenanceAppointmentScheduledEmailTemplate
  extends IVehicleMaintenanceAppointmentEmailTemplate {
  date: string;
  time: string;
  rescheduleAppointmentLink: string;
  workshopName: string;
  workshopLocation: string;
}

interface IVehicleMaintenanceAppointmentCancelledEmailTemplate
  extends IVehicleMaintenanceAppointmentEmailTemplate {
  link: string;
}

interface IVehicleMaintenanceAppointmentMissedEmailTemplate
  extends IVehicleMaintenanceAppointmentEmailTemplate {
  link: string;
}

interface IVehicleMaintenanceAppointmentCompletedEmailTemplate
  extends IVehicleMaintenanceAppointmentEmailTemplate {}

interface IVehicleMaintenanceAppointmentRescheduledEmailTemplate
  extends IVehicleMaintenanceAppointmentEmailTemplate {
  date: string;
  time: string;
  link: string;
}

export function vehicleMaintenanceAppointmentScheduledEmailTemplate({
  name,
  date,
  time,
  rescheduleAppointmentLink,
  workshopName,
  workshopLocation,
}: IVehicleMaintenanceAppointmentScheduledEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vehicle Maintenance</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola ${name}
            <br />
            <br />
            ¡Tu cita de servicio de mantenimiento ha sido confirmada! 🔧
            <br />
            <br />
            Nos complace informarte que tu cita de mantenimiento ha sido agendada con éxito.
            <br />
            <br />
            📅 Fecha: ${date}
            <br />
            ⏰ Hora: ${time}
            <br />
            ⚙️ Taller: ${workshopName}
            <br />
            <br />
            ⚠️ Importante: Si necesitas reprogramar o cancelar tu cita debes realizalo mediante el siguiente enlace
            <br />
            <br />
            ¡Gracias por confiar en nosotros! Nos aseguraremos de brindarte el mejor servicio.
            <br />
            <br />
            📍 Dirección taller: ${workshopLocation}
            <br />
            🗓️ Reprogramar cita: ${rescheduleAppointmentLink}
          </p>
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export function vehicleMaintenanceAppointmentCancelledEmailTemplate({
  name,
  link,
}: IVehicleMaintenanceAppointmentCancelledEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vehicle Maintenance</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola ${name}
            <br />
            <br />
            Notamos que cancelaste el mantenimiento que habías programado. Esperamos que te encuentres bien.
            <br />
            <br />
            🔧 Recuerda que darle mantenimiento a tu auto es muy importante para mantenerlo en excelentes condiciones.
            <br />
            📅 Puedes programar fácilmente una nueva cita haciendo clic en el siguiente enlace:
            <br />
            👉 ${link}
            <br />
            <br />
            ¡Estamos aquí para ayudarte!
          </p>
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export function vehicleMaintenanceAppointmentMissedEmailTemplate({
  name,
  link,
}: IVehicleMaintenanceAppointmentMissedEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vehicle Maintenance</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola ${name}
            <br />
            <br />
            🚫 Cita cancelada
            <br />
            Lamentablemente tu cita fue cancelada porque no te presentaste en la fecha y hora programadas.
            <br />
            <br />
            📅 Puedes reagendar fàcilmente a través del siguiente enlace:
            <br />
            👉 ${link}
            <br />
            <br />
            ✨ Además, puedes usar este mismo enlace en cualquier momento para programar tu cita y evitar futuras inasistencias.
            <br />
            <br />
            ⚠️ Recuerda que si no asistes nuevamente, se aplicará una penalización según nuestras políticas.
            <br />
            <br />
            ¡Gracias por tu comprensión! 😄
          </p>
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>`;
}

export function vehicleMaintenanceAppointmentCompletedEmailTemplate({
  name,
}: IVehicleMaintenanceAppointmentCompletedEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vehicle Maintenance</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola ${name}
            <br />
            <br />
            El mantenimiento de tu auto se ha realizado con éxito. ✅
            <br />
            ¡Gracias por tu tiempo!
            <br />
            Te deseamos un excelente día ☀️
          </p>
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>`;
}

export function vehicleMaintenanceAppointmentRescheduledEmailTemplate({
  name,
  date,
  time,
  link,
}: IVehicleMaintenanceAppointmentRescheduledEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vehicle Maintenance</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola ${name}
            <br />
            <br />
            Notamos que no pudiste asistir al mantenimiento programado para hoy. Esperamos que te encuentres bien.
            <br />
            <br />
            🔧 Recuerda que el mantenimiento es clave para mantener tu auto en excelentes condiciones.
            <br />
            📅 Para reprogramar tu cita, simplemente haz clic en el siguiente enlace:
            <br />
            👉 ${link} ${date} ${time}
            <br />
            <br />
            ¡Muchas gracias!
          </p>
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>`;
}
