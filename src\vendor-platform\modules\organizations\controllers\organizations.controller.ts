import { AsyncController } from '@/types&interfaces/types';
import { createOrgDto } from '../dtos/create-org.dto';
import { organizationsService } from '../services/organizations.service';
import { serviceClassInstance } from '../../services/services/service.service';
import OrganizationModel from '../models/organization.model';

export const createOrganization: AsyncController = async (req, res) => {
  const validatedData = createOrgDto.parse(req.body);

  if (!validatedData) {
    return res.status(400).send({ message: 'Invalid data' });
  }
  const role = req.userReq.role;
  const newOrg = await organizationsService.createOrganization(validatedData, role);

  return res.status(201).send({
    message: 'Organization created',
    data: newOrg,
  });
};

export const getOrganizations: AsyncController = async (req, res) => {
  const organizations = await organizationsService.getOrganizations();
  if (!organizations) {
    return res.status(404).send({
      message: 'Organizations not found',
    });
  }

  return res.status(200).send({
    message: 'Organizations found',
    data: organizations,
  });
};

export const getOrganizationById: AsyncController = async (req, res) => {
  const { organizationId } = req.params;

  const organization = await organizationsService.getOrganizationById(organizationId);

  if (!organization) {
    return res.status(404).send({
      message: 'Organization not found',
    });
  }

  return res.status(200).send({
    message: 'Organization found',
    data: organization,
  });
};

export const getOrganizationWorshops: AsyncController = async (req, res) => {
  const { organizationId } = req.params;

  const workshops = await organizationsService.getOrganizationWorkshops(organizationId);

  if (!workshops || workshops.length === 0) {
    return res.status(404).send({
      message: 'Workshops not found',
    });
  }
  return res.status(200).send({
    message: 'Workshops found',
    data: workshops,
  });
};

export const getOrganizationServices: AsyncController = async (req, res) => {
  const organizationId = req.userVendor.organizationId || req.params.organizationId;

  const page = req.query.page ? parseInt(req.query.page as string) : 0;
  const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

  const { services, ...rest } = await serviceClassInstance.getOrganizationServices(organizationId, {
    page,
    limit,
  });

  return res.status(200).send({ message: 'Services found', data: services, ...rest });
};

export const getOrganizationSchedule: AsyncController = async (req, res) => {
  const organization = await OrganizationModel.findById(req.params.organizationId);
  if (!organization) {
    return res.status(404).send({ error: 'Organización no encontrada' });
  }
  return res.status(200).send({ message: 'Schedule found', data: organization.globalScheduleConfig });
};

export const updateOrganizationSchedule: AsyncController = async (req, res) => {
  const organization = await OrganizationModel.findByIdAndUpdate(
    req.params.organizationId,
    { globalScheduleConfig: req.body },
    { new: true }
  );
  if (!organization) {
    return res.status(404).send({ error: 'Organización no encontrada' });
  }
  return res.status(200).send({ message: 'Schedule updated', data: organization.globalScheduleConfig });
};
