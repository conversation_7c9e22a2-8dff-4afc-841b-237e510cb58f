import { Types } from 'mongoose';
import { uploadFile } from '../../../aws/s3';
import { associateText, CountriesEnum, genericMessages } from '../../../constants';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import AssociatePayments from '../../../models/associatePayments';
import Associate from '../../../models/associateSchema';
import AssociateUS from '../../../models/associateSchemaUS';
import Document from '../../../models/documentSchema';
import MainContractSchema from '../../../models/mainContractSchema';
import StockVehicle from '../../../models/StockVehicleSchema';
import {
  mapReplaceArrayOfDocsId,
  replaceArrayOfDocsObjectsWithDocUrl,
  replaceDocWithUrl,
  replaceSingleObjectWithDocUrlSureste,
} from '../../../services/getPropertyWithUrls';
import { removeEmptySpacesNameFile } from '../../../services/removeEmptySpaces';
import axios from 'axios';
import { associateServiceUS } from './associate-us.service';
import { logger } from '../../../clean/lib/logger';

function capitalizeFirstLetterOfEachWord(name: string): string {
  return name
    .toLowerCase() // Convertimos todo a minúsculas primero
    .split(' ') // Dividimos por espacios para separar las palabras
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalizamos la primera letra de cada palabra
    .join(' '); // Unimos las palabras de nuevo
}

class AssociateService {
  async getAssociateByVehicle(stockVehicle: any) {
    const currentAssociate = await Associate.findById(
      stockVehicle.drivers[stockVehicle.drivers.length - 1]?._id
    )
      .select('+avalData +digitalSignature')
      .lean();

    if (!currentAssociate) {
      return [];
    }

    if (currentAssociate?.country === CountriesEnum['United States']) {
      const associatesUS = await this.getAssociateUS({
        currentAssociate,
        stockVehicle,
      });
      return associatesUS;
    }

    const associates = [];
    const { deliveredImages, adendumDocs, picture, unSignedContractDoc, mainContract, docs } =
      await this.documentsUrl(currentAssociate, stockVehicle);

    const associateObj = await this.associateMapper({
      currentAssociate,
      deliveredImages,
      adendumDocs,
      picture,
      unSignedContractDoc,
      mainContract,
      docs,
    });

    if (!currentAssociate.signDocs) {
      associates.push(associateObj);
      return associates;
    }

    const signDocs = await this.getAssociateSignDocsUrls(currentAssociate);
    const bankStatements = await this.getBankStatementsUrls(currentAssociate);
    associates.push({ ...associateObj, signDocs, bankStatements });
    return associates;
  }

  async updateAssociate(associateMetaData: {
    id: Types.ObjectId;
    body: any;
    files: Express.Multer.File[];
    userId: Types.ObjectId;
  }) {
    try {
      const { id, body, files, userId } = associateMetaData;
      if (!id) {
        return {
          status: 400,
          message: genericMessages.errors.missingBody,
          data: null,
        };
      }

      const associate = await Associate.findById(id).select('+avalData');
      if (!associate) {
        return {
          status: 404,
          message: associateText.errors.associateNotFound,
          data: null,
        };
      }
      if (body.email && body.email !== associate.email) {
        const payment = await AssociatePayments.findOne({ email: body.email });
        if (payment) {
          payment.associateEmail = body.email.toLowerCase().trim();
          await payment.save();
        }
      }
      const updatedAssociateData = this.prepareAssociateData(associate.country, { ...body });
      await this.updateAvalIne({ associate, updatedAssociateData, files });
      await Associate.updateOne({ _id: id }, updatedAssociateData);
      await this.updatePaymentService({ obj: updatedAssociateData, associate });
      await this.updateStockVehicleHistory({ associate, userId });
      await associateServiceUS.updateAssociateUS({ associateId: id, associateData: { ...body } });

      return {
        status: 200,
        message: associateText.success.associateUpdated,
        data: null,
      };
    } catch (error: any) {
      logger.error('Error occured while updating associate', error);
      return {
        status: 500,
        message: error?.response?.data?.message || genericMessages.errors.somethingWentWrong,
        data: null,
      };
    }
  }

  private prepareAssociateData(associateCountry: string, associateData: any) {
    if (associateCountry && associateCountry === CountriesEnum['United States']) {
      delete associateData.contacts;
      delete associateData.city;
      delete associateData.state;
      delete associateData.ssn;
    }
    if (associateData?.firstName) {
      associateData.firstName = capitalizeFirstLetterOfEachWord(
        associateData?.firstName?.toLowerCase().trim()
      );
    }
    if (associateData?.lastName) {
      associateData.lastName = capitalizeFirstLetterOfEachWord(associateData?.lastName?.toLowerCase().trim());
    }
    return associateData;
  }

  private async updateStockVehicleHistory({
    associate,
    userId,
  }: {
    associate: Record<string, any>;
    userId: Types.ObjectId;
  }) {
    const currentVehicle = await StockVehicle.findById(
      associate.vehiclesId?.[associate.vehiclesId.length - 1]
    );

    if (currentVehicle) {
      currentVehicle.updateHistory.push({
        description: '',
        step: 'Updated driver profile',
        userId: userId,
      });
      await currentVehicle.save();
    }
  }

  private async updateAvalIne({
    associate,
    updatedAssociateData,
    files,
  }: {
    associate: Record<string, any>;
    updatedAssociateData: Record<string, any>;
    files: Express.Multer.File[];
  }) {
    if (files.length === 0) {
      updatedAssociateData.avalData = {
        ...updatedAssociateData.avalData,
        ine: associate.avalData.ine,
      };
      return;
    }

    const avalINE = files.find((file: any) => file.fieldname === 'avalINE');
    if (!avalINE) {
      return;
    }
    const removeSpacesFileName = removeEmptySpacesNameFile(avalINE);
    const documentPath = `associate/${associate.email}/avalINE/${removeSpacesFileName}`;
    const s3Path = `associate/${associate.email}/avalINE/`;
    if (associate.avalData.ine) {
      const oldAvalINE = await Document.findById(associate.avalData.ine);
      if (oldAvalINE) {
        await oldAvalINE.remove();
      }
    }
    const document = new Document({
      originalName: removeSpacesFileName,
      associateId: associate._id,
      path: documentPath,
    });
    await document.save();
    updatedAssociateData.avalData = {
      ...updatedAssociateData.avalData,
      ine: document._id,
    };
    await uploadFile(avalINE, removeSpacesFileName, s3Path);
  }

  private async updatePaymentService({
    obj,
    associate,
  }: {
    obj: Record<string, any>;
    associate: Record<string, any>;
  }) {
    try {
      const clientId = associate.clientId;
      if (!clientId) {
        return;
      }
      const updateObj = {
        phone: obj.phone?.toString() || associate.phone.toString(),
        email: obj.email || associate.email,
        name: obj.firstName || associate.firstName,
        lastName: obj.lastName || associate.lastName,
        legal_name: `${obj.firstName || associate.firstName} ${obj.lastName || associate.lastName}`
          .trim()
          .replace(/\s\s+/g, ' ') // Remove extra spaces (double spaces, etc.)
          .toUpperCase(),
        rfc: obj.rfc || associate.rfc,
        zip: obj.postalCode?.toString() || associate.postalCode.toString(),
        country: associate.country,
      };
      await axios.patch(`${PAYMENTS_API_URL}/clients/${clientId}`, updateObj, {
        headers: {
          Authorization: `Bearer ${PAYMENTS_API_KEY}`,
        },
      });
    } catch (error) {
      console.log('Error updating client in Payments api', error);
    }
  }

  private async getAssociateUS(associateMetaData: any) {
    const { currentAssociate, stockVehicle } = associateMetaData;

    const { deliveredImages, adendumDocs, picture, unSignedContractDoc, mainContract } =
      await this.documentsUrl(currentAssociate, stockVehicle);
    const associatesUS = [];

    const associateUS = await AssociateUS.findOne({ associate: currentAssociate._id })
      .select('+documents')
      .lean();
    if (!associateUS) {
      const associateObj = {
        ...currentAssociate,
        _id: currentAssociate?._id,
        deliveredImages,
        adendumDocs,
        address: {
          addressStreet: currentAssociate?.addressStreet,
          exterior: currentAssociate?.exterior,
          interior: currentAssociate?.interior,
          colony: currentAssociate?.colony,
          delegation: currentAssociate?.delegation,
          city: currentAssociate?.city,
          state: currentAssociate?.state,
          postalCode: currentAssociate?.postalCode,
        },
        picture,
        unSignedContractDoc,
        contractData: mainContract || null,
        documents: {
          ine: {
            ineFront: '',
            ineBack: '',
          },
          driverLicense: {
            driverLicenseFront: '',
            driverLicenseBack: '',
          },
          curp: '',
          addressVerification: '',
          taxStatus: '',
          garage: '',
          proofOfCompletionOfAnyRequiredSafetyCourses: '',
          drivingRecord: '',
          rideShareDates: '',
          rideShareRideHistory: '',
          avgWeeklyIncomeOfLastTwelveWeeks: [],
          signature: '',
        },
        avalData: {
          ...currentAssociate.avalData,
          ine: (await replaceDocWithUrl(currentAssociate.avalData?.ine?.toString())) || null,
        },
        digitalSignature: currentAssociate.digitalSignature,
      };

      if (!currentAssociate.signDocs) {
        associatesUS.push(associateObj);
        return associatesUS;
      }

      const signDocs = await this.getAssociateSignDocsUrls(currentAssociate);
      associatesUS.push({ ...associateObj, signDocs });
      return associatesUS;
    }

    const docs = await this.getAssociateUSDocumentsUrls(associateUS);
    const bankStatements = await this.getBankStatementsUrls(associateUS);
    const associateUSObj = await this.associateUSMapper({
      currentAssociate,
      associateUS,
      deliveredImages,
      adendumDocs,
      picture,
      unSignedContractDoc,
      mainContract,
      docs,
    });

    if (!currentAssociate.signDocs) {
      associatesUS.push(associateUSObj);
      return associatesUS;
    }
    const signDocs = await this.getAssociateSignDocsUrls(currentAssociate);
    associatesUS.push({ ...associateUSObj, signDocs, bankStatements });
    return associatesUS;
  }

  private async documentsUrl(associate: any, stockVehicle: any) {
    const docs = await this.getAssociateDocumentsUrls(associate);
    const unSignedContractDoc = await replaceDocWithUrl(associate?.unSignedContractDoc?.toString());
    const picture = await replaceDocWithUrl(associate?.picture?.toString());

    const filter = { associatedId: associate._id, stockId: stockVehicle._id };
    const mainContract = await MainContractSchema.findOne(filter);
    const deliveredImages = (await mapReplaceArrayOfDocsId(associate.deliveredImages)) || [];
    const adendumDocs = associate.adendumDocs && (await mapReplaceArrayOfDocsId(associate.adendumDocs));

    return {
      docs,
      unSignedContractDoc,
      picture,
      filter,
      mainContract,
      deliveredImages,
      adendumDocs,
    };
  }

  private async getBankStatementsUrls(associate: any) {
    const bankStatements = await replaceSingleObjectWithDocUrlSureste(associate.bankStatement, [
      'bankStatementOne',
      'bankStatementTwo',
      'bankStatementThree',
      'bankStatementFour',
      'bankStatementFive',
      'bankStatementSix',
    ]);
    return bankStatements;
  }

  private async getAssociateDocumentsUrls(associate: any) {
    const selectedDocs = {
      ineFront: associate?.documents?.ineFront,
      ineBack: associate?.documents?.ineBack,
      driverLicenseFront: associate?.documents?.driverLicenseFront,
      driverLicenseBack: associate?.documents?.driverLicenseBack,
      curp: associate?.documents?.curp,
      addressVerification: associate?.documents?.addressVerification,
      taxStatus: associate?.documents?.taxStatus,
      garage: associate?.documents?.garage,
    };

    const docs = await replaceSingleObjectWithDocUrlSureste(selectedDocs, [
      'ineFront',
      'ineBack',
      'curp',
      'addressVerification',
      'taxStatus',
      'driverLicenseFront',
      'driverLicenseBack',
      'garage',
    ]);

    return docs;
  }

  private async getAssociateSignDocsUrls(associate: any) {
    const existingDocs = {
      contract: associate.signDocs.contract,
      promissoryNote: associate.signDocs.promissoryNote,
      deliveryReceipt: associate.signDocs.deliveryReceipt,
      warranty: associate.signDocs.warranty,
      invoice: associate.signDocs.invoice,
      privacy: associate.signDocs.privacy,
      contactInfo: associate.signDocs.contactInfo,
    };

    if (associate.signDocs.contactInfo) {
      existingDocs.contactInfo = associate.signDocs.contactInfo;
    }
    const signDocs = await replaceSingleObjectWithDocUrlSureste(associate.signDocs, [
      'contract',
      'promissoryNote',
      'deliveryReceipt',
      'warranty',
      'invoice',
      'privacy',
      'contactInfo',
    ]);

    return signDocs;
  }

  private async getAssociateUSDocumentsUrls(associateUS: any) {
    const selectedDocs = {
      driverLicenseFront: associateUS?.documents?.driverLicenseFront,
      driverLicenseBack: associateUS?.documents?.driverLicenseBack,
      addressVerification: associateUS?.documents?.addressVerification,
      garage: associateUS?.documents?.garage,
      proofOfCompletionOfAnyRequiredSafetyCourses:
        associateUS?.documents?.proofOfCompletionOfAnyRequiredSafetyCourses,
      drivingRecord: associateUS?.documents?.drivingRecord,
      rideShareDates: associateUS?.documents?.rideShareDates,
      rideShareRideHistory: associateUS?.documents?.rideShareRideHistory,
      signature: associateUS?.documents?.signature,
    };
    const docs = await replaceSingleObjectWithDocUrlSureste(selectedDocs, [
      'driverLicenseFront',
      'driverLicenseBack',
      'addressVerification',
      'garage',
      'proofOfCompletionOfAnyRequiredSafetyCourses',
      'drivingRecord',
      'rideShareDates',
      'rideShareRideHistory',
      'signature',
    ]);
    const avgWeeklyIncomeOfLastTwelveWeeks = await replaceArrayOfDocsObjectsWithDocUrl(
      associateUS?.documents?.avgWeeklyIncomeOfLastTwelveWeeks
    );
    const docsUrls = {
      ...docs,
      avgWeeklyIncomeOfLastTwelveWeeks: avgWeeklyIncomeOfLastTwelveWeeks,
    };
    return docsUrls;
  }

  private async associateMapper(associateMetaData: any) {
    const {
      currentAssociate,
      deliveredImages,
      adendumDocs,
      picture,
      unSignedContractDoc,
      mainContract,
      docs,
    } = associateMetaData;

    const associateObj = {
      ...currentAssociate,
      _id: currentAssociate?._id,
      deliveredImages,
      adendumDocs,
      address: {
        addressStreet: currentAssociate?.addressStreet,
        exterior: currentAssociate?.exterior,
        interior: currentAssociate?.interior,
        colony: currentAssociate?.colony,
        delegation: currentAssociate?.delegation,
        city: currentAssociate?.city,
        state: currentAssociate?.state,
        postalCode: currentAssociate?.postalCode,
      },
      picture,
      unSignedContractDoc,
      contractData: mainContract || null,
      documents: {
        ine: {
          ineFront: docs?.ineFront,
          ineBack: docs?.ineBack,
        },
        driverLicense: {
          driverLicenseFront: docs?.driverLicenseFront,
          driverLicenseBack: docs?.driverLicenseBack,
        },
        addressVerification: docs?.addressVerification,
        curp: docs?.curp,
        taxStatus: docs?.taxStatus,
        garage: docs?.garage,
      },
      avalData: {
        ...currentAssociate.avalData,
        ine: (await replaceDocWithUrl(currentAssociate.avalData?.ine?.toString())) || null,
      },
      digitalSignature: currentAssociate.digitalSignature,
    };
    return associateObj;
  }

  private async associateUSMapper(associateMetaData: any) {
    const {
      currentAssociate,
      associateUS,
      deliveredImages,
      adendumDocs,
      picture,
      unSignedContractDoc,
      mainContract,
      docs,
    } = associateMetaData;

    const associateUSObj = {
      ...currentAssociate,
      ...associateUS,
      _id: currentAssociate?._id,
      associate: currentAssociate?._id,
      deliveredImages,
      adendumDocs,
      address: {
        addressStreet: currentAssociate?.addressStreet,
        exterior: currentAssociate?.exterior,
        interior: currentAssociate?.interior,
        colony: currentAssociate?.colony,
        delegation: currentAssociate?.delegation,
        city: currentAssociate?.city,
        state: currentAssociate?.state,
        postalCode: currentAssociate?.postalCode,
      },
      picture,
      unSignedContractDoc,
      contractData: mainContract || null,
      documents: {
        ine: {
          ineFront: '',
          ineBack: '',
        },
        driverLicense: {
          driverLicenseFront: docs?.driverLicenseFront,
          driverLicenseBack: docs?.driverLicenseBack,
        },
        addressVerification: docs?.addressVerification,
        garage: docs?.garage,
        proofOfCompletionOfAnyRequiredSafetyCourses: docs?.proofOfCompletionOfAnyRequiredSafetyCourses,
        drivingRecord: docs?.drivingRecord,
        rideShareDates: docs?.rideShareDates,
        rideShareRideHistory: docs?.rideShareRideHistory,
        avgWeeklyIncomeOfLastTwelveWeeks: docs?.avgWeeklyIncomeOfLastTwelveWeeks,
        signature: docs?.signature,
        curp: '',
        taxStatus: '',
      },
      avalData: {
        ...currentAssociate.avalData,
        ine: (await replaceDocWithUrl(currentAssociate.avalData?.ine?.toString())) || null,
      },
      digitalSignature: currentAssociate.digitalSignature,
    };

    return associateUSObj;
  }
}

const associateService = new AssociateService();

export { associateService };
