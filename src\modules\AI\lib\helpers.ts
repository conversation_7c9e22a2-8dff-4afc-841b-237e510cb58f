import { repoGetMediaSignedUrl } from '@/clean/data/s3Repositories';
import { retrieveMostRecent12WeeksEarnings } from '@/clean/domain/usecases';
import { AdmissionRequestMongo } from '@/models/admissionRequestSchema';
import { MetricMongo } from '@/models/metricsSchema';
import DocumentMongo from '@/models/documentSchema';
import { AdmissionRequest } from '@/clean/domain/entities';
import { Weights } from './types';
import { logger } from '@/clean/lib/logger';

export async function getHomeVisitModelBody(requestId: string) {
  const request = await AdmissionRequestMongo.findOne({ _id: requestId });
  return request;
}

export async function getPreApprovalBody(requestId: string) {
  const request = await AdmissionRequestMongo.findOne({ _id: requestId });
  if (!request || !request.earningsAnalysis) {
    throw new Error('Request not found');
  }

  const earnings = request.earningsAnalysis.earnings.map((earning) => earning?.totalAmount);
  const mostRecent12Weeks = await retrieveMostRecent12WeeksEarnings(requestId);
  const metrics = await MetricMongo.findOne({ requestId });

  return {
    ...request.toObject(),
    metrics: metrics?.toObject(),
    earnings,
    platform_deposits: mostRecent12Weeks.length,
  };
}

/**
 * Calculate weekly earnings from an admission request
 *
 * @param admissionRequest The admission request containing earnings data
 * @returns Average weekly earnings
 */
export function getWeeklyEarnings(admissionRequest: AdmissionRequest): number {
  const earnings = admissionRequest.earningsAnalysis?.earnings;
  if (!earnings || earnings.length === 0) {
    return 0;
  }

  const validEarnings = earnings.filter((earning) => earning?.totalAmount !== undefined);
  return validEarnings.length > 0
    ? validEarnings.reduce((sum, earning) => sum + earning.totalAmount, 0) / validEarnings.length
    : 0;
}

export async function getFinancialAssessmentBody(admissionRequest: AdmissionRequest) {
  const weeklyEarnings = getWeeklyEarnings(admissionRequest);

  const documentsArray = await Promise.all(
    admissionRequest.documentsAnalysis.documents
      .filter((doc) => doc.type.includes('bank_statement'))
      .map(async (doc) => {
        const media = await DocumentMongo.findById(doc.mediaId);

        if (!media) {
          return null;
        }

        const signedUrl = await repoGetMediaSignedUrl(media.path);
        return {
          signed_url: signedUrl,
          docType: doc.type,
          analysisResult: doc.analysisResult,
        };
      })
  );

  return {
    customer_id: admissionRequest.id,
    weekly_earnings: weeklyEarnings,
    documents_array: documentsArray,
  };
}

/**
 * Calculate a weighted score based on provided weights and corresponding values.
 *
 * @param weights - Dictionary containing weight values for different criteria
 * @param values - Dictionary containing normalized values (between 0 and 1) for each criteria
 * @returns Score between 0 and 100
 */
export function calculateWeightedScore(
  weights: Weights,
  values: Record<string, number | null | undefined>
): number {
  let totalScore = 0;
  let totalWeight = 0;

  for (const key in weights) {
    if (Object.prototype.hasOwnProperty.call(weights, key)) {
      const value = values[key] ?? 0;
      totalScore += value * weights[key];
      totalWeight += weights[key];
    }
  }

  return totalWeight === 0 ? 0 : totalScore / totalWeight;
}

/**
 * Extract JSON from string response
 */
export function extractJsonFromString(text: string): any {
  try {
    // Find json pattern starting with { and ending with }
    const jsonMatch = text.match(/\{[\s\S]*\}/);

    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }

    throw new Error('No valid JSON found in response');
  } catch (error) {
    logger.error(`Failed to extract JSON from response: ${error}`);
    throw new Error(`Failed to parse response: ${error}`);
  }
}

function formatAddressUS({ postalCode = '', city = '', state = '', street = '', department = '' } = {}) {
  let fullAddress = ``;
  if (street) {
    fullAddress = `${street}`;
  }
  if (department) {
    fullAddress = `${fullAddress} ${department}`;
  }
  if (city) {
    fullAddress = `${fullAddress}, ${city}`;
  }
  if (state) {
    fullAddress = `${fullAddress}, ${state}`;
  }
  if (postalCode) {
    fullAddress = `${fullAddress}, ${postalCode}`;
  }
  return fullAddress;
}

export enum Countries {
  'United States' = 'United States',
  Mexico = 'Mexico',
}

export const CountriesShortNames = Object.freeze({
  [Countries['United States']]: 'us',
  [Countries.Mexico]: 'mx',
});

export function formatAddress({
  postalCode = '',
  city = '',
  state = '',
  neighborhood = '',
  street = '',
  streetNumber = '',
  department = '',
  country = '',
} = {}) {
  if (country === CountriesShortNames['United States']) {
    return formatAddressUS({
      postalCode,
      city,
      state,
      street,
      department,
    });
  }
  const isNonEmpty = (value: null | undefined | string) =>
    value !== null && value !== undefined && value.trim() !== '';
  const addressParts = [
    isNonEmpty(street) && isNonEmpty(streetNumber) ? `${street} ${streetNumber}` : null,
    department,
    isNonEmpty(neighborhood) && isNonEmpty(postalCode) ? `${neighborhood} ${postalCode}` : null,
    isNonEmpty(city) && isNonEmpty(state) ? `${city}, ${state}` : null,
  ];

  return addressParts.filter((part) => part !== null).join(', ');
}
