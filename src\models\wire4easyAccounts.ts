import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const wire4easyAccountsSchema = new Schema({
  associateEmail: {
    type: String,
    required: [true, 'Object is required'],
  },
  clabe: {
    type: String,
    required: [true, 'Api version is required'],
  },

  contract: {
    type: String,
    required: [true, 'Api version is required'],
  },

  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});

const Wire4EasyAccounts = model('Wire4EasyAccounts', wire4easyAccountsSchema);

export default Wire4EasyAccounts;
