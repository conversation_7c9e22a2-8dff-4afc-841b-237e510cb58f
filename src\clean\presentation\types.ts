import { Country, GigPlatform, PalencaWebhookAction } from '../domain/enums';
// This is the payload that is sent to the webhook from Palenca
// https://www.developers.palenca.com/docs/v2/Guides/Webhooks/

export interface PalencaWebhookData {
  webhook_action: PalencaWebhookAction;
  user_id: string;
  account_id: string;
  external_id: string;
  country: Country;
  platform: GigPlatform;
  status_details: string;
  phone: string;
}

export interface PalencaWebhookPayload {
  webhook_url: string;
  data: PalencaWebhookData;
}
