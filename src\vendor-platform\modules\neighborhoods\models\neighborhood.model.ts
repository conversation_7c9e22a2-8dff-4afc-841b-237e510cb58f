import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';
import { WeeklySchedule } from '@/models/Schedules';

interface IScheduleConfig {
  weeklySchedule: WeeklySchedule;
  installationDuration: number;
  timezone: string;
  breakTime: {
    start: string;
    end: string;
  };
  maxSimultaneousInstallations: number;
}

export interface INeighborhood extends Document {
  crewId: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  cityId: mongoose.Types.ObjectId;
  name: string;
  active: boolean;
  scheduleConfig: IScheduleConfig;
  address: {
    street: string;
    number: string;
    interior: string;
    colony: string;
    city: string;
    postalCode: string;
  };
  mapsLink: string;
}

const ScheduleConfigSchema = new Schema({
  weeklySchedule: {
    monday: { start: String, end: String },
    tuesday: { start: String, end: String },
    wednesday: { start: String, end: String },
    thursday: { start: String, end: String },
    friday: { start: String, end: String },
    saturday: { start: String, end: String },
    sunday: { start: String, end: String },
  },
  installationDuration: { type: Number, default: 180 },
  timezone: { type: String },
  breakTime: {
    start: { type: String },
    end: { type: String },
  },
  maxSimultaneousInstallations: { type: Number, required: true },
});

const NeighborhoodSchema = new Schema(
  {
    crewId: { type: Schema.Types.ObjectId, ref: 'Crew', required: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
    cityId: { type: Schema.Types.ObjectId, ref: 'City', required: true },
    name: { type: String, required: true },
    active: { type: Boolean, default: true },
    scheduleConfig: { type: ScheduleConfigSchema /* , required: true */ },
    // address: {
    //   street: { type: String, default: '' },
    //   number: { type: String, default: '' },
    //   interior: { type: String },
    //   colony: { type: String, default: '' },
    //   city: { type: String, default: '' },
    //   postalCode: { type: String, default: '' },
    // },
    mapsLink: { type: String },
  },
  { timestamps: true }
);

export const NeighborhoodVendorModel = vendorDB.model<INeighborhood>('Neighborhood', NeighborhoodSchema);
