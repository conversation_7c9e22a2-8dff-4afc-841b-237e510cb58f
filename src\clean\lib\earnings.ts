import { MIN_EARNINGS_APPROVED, MIN_EARNINGS_APPROVED_WITH_CONDITIONS } from '../../constants';
import { EarningsAnalysisStatus } from '../domain/enums';

export const determineEarningsAnalysisStatus = (totalEarnings: number): EarningsAnalysisStatus => {
  if (totalEarnings >= MIN_EARNINGS_APPROVED) {
    return EarningsAnalysisStatus.approved;
  } else if (totalEarnings >= MIN_EARNINGS_APPROVED_WITH_CONDITIONS) {
    return EarningsAnalysisStatus.approved_with_conditions;
    return EarningsAnalysisStatus.approved; //91
  } else if (totalEarnings >= MIN_EARNINGS_APPROVED_WITH_CONDITIONS) {
    return EarningsAnalysisStatus.approved_with_conditions; // 74 - 91
  } else {
    return EarningsAnalysisStatus.rejected;
  }
};
