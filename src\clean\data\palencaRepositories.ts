import { HttpClient } from '../lib/httpClient';
import { PALENCA_API_BASE_URL, PALENCA_API_KEY } from '../../constants';
import {
  PalencaEarning,
  EarningsRequestPayload,
  EarningsResponsePayload,
  PalencaResponseEnvelope,
  ProfileResponsePayload,
} from './palencaModels';
import { earningPalencaAdapter, metricPalencaAdapter } from '../domain/adapters';
import { Earning, Metric } from '../domain/entities';
import { GigPlatform } from '../domain/enums';
import {
  CouldNotRetrievePalencaEarningsException,
  CouldNotRetrievePalencaMetrics,
} from '../errors/exceptions';
import { logger } from '../lib/logger';
import Palenca from '../../models/palenca';

export async function fetchHistoricalEarnings(api: HttpClient, accountId: string) {
  const startDate = new Date();
  const endDate = new Date();
  // We can only retrieve earnings based on a date range
  // We make a request with a large with one year range to get all earnings
  endDate.setFullYear(startDate.getFullYear() - 1);
  const startDateString = startDate.toISOString().split('T')[0];
  const endDateString = endDate.toISOString().split('T')[0];

  const initialPayload: EarningsRequestPayload = {
    start_date: startDateString,
    end_date: endDateString,
    options: {
      items_per_page: 100,
      page: 1,
    },
  };
  let allEarnings: PalencaEarning[] = [];
  let currentPage = 1;
  let totalPages = 1;
  logger.info(`[fetchHistoricalEarnings] Fetching earnings for account ${accountId}`);
  while (currentPage <= totalPages) {
    const payload = { ...initialPayload, options: { ...initialPayload.options, page: currentPage } };
    try {
      logger.info(
        `[fetchHistoricalEarnings] Fetching earnings for account ${accountId} - page ${currentPage}`
      );
      const response = await api.post<PalencaResponseEnvelope<EarningsResponsePayload>>('', payload);
      if (response.success && response.data) {
        allEarnings = allEarnings.concat(response.data.earnings);
        currentPage = response.pagination!.page;
        totalPages = response.pagination!.total_pages;
        currentPage++;
      } else {
        logger.error(`[fetchHistoricalEarnings] Error fetching earnings for account ${accountId}`);
        throw new CouldNotRetrievePalencaEarningsException();
      }
    } catch (error) {
      logger.error(`[fetchHistoricalEarnings] Error fetching earnings for account ${accountId}`);
      throw new CouldNotRetrievePalencaEarningsException();
    }
  }

  logger.info(`[fetchHistoricalEarnings] Fetched ${allEarnings.length} earnings for account ${accountId}`);

  return allEarnings;
}

export const repoRetrieveEarnings = async (
  accountId: string,
  platform: GigPlatform,
  requestId: string
): Promise<Earning[]> => {
  const url = `${PALENCA_API_BASE_URL}/accounts/${accountId}/earnings/search`;
  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'x-api-key': PALENCA_API_KEY,
  };
  const api = new HttpClient(url, headers);
  await Palenca.create({ type: 'earnings', body: api });
  try {
    logger.info(`[repoRetrieveEarnings] Fetching earnings for account ${accountId}`);
    const earnings = await fetchHistoricalEarnings(api, accountId);
    if (!earnings) return [];
    const earningsEntities = earnings.map((entity) => earningPalencaAdapter(entity, platform, requestId));
    return earningsEntities;
  } catch (error) {
    logger.error(`[repoRetrieveEarnings] Error fetching earnings for account ${accountId}`);
    throw new CouldNotRetrievePalencaEarningsException();
  }
};

export async function fetchProfile(api: HttpClient, accountId: string): Promise<ProfileResponsePayload> {
  try {
    const response = await api.get<PalencaResponseEnvelope<ProfileResponsePayload>>('');
    if (response.success && response.data) {
      return response.data;
    } else {
      logger.error(`[fetchProfile] Error fetching profile for account ${accountId}`);
      throw new CouldNotRetrievePalencaEarningsException();
    }
  } catch (error) {
    logger.error(`[fetchProfile] Error fetching profile for account ${accountId}`);
    throw new CouldNotRetrievePalencaEarningsException();
  }
}

export const repoRetrieveMetric = async (
  accountId: string,
  platform: GigPlatform,
  requestId: string
): Promise<Metric | null> => {
  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'x-api-key': PALENCA_API_KEY,
  };
  const url = `${PALENCA_API_BASE_URL}/accounts/${accountId}/profile/`;
  const api = new HttpClient(url, headers);
  try {
    await Palenca.create({ type: 'profile', body: api });
    logger.info(`[repoRetrieveMetric] Fetching metrics for account ${accountId}`);
    const response = await fetchProfile(api, accountId);
    if (!response) return null;
    const entity = metricPalencaAdapter(response.metrics_info, platform, requestId);
    return entity;
  } catch (error) {
    logger.error(`[repoRetrieveMetric] Error fetching metrics for account ${accountId}`);
    throw new CouldNotRetrievePalencaMetrics();
  }
};
