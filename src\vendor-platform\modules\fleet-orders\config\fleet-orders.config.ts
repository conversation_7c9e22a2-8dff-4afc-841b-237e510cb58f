/**
 * Configuración para el módulo Fleet Orders
 */

export const FLEET_ORDERS_CONFIG = {
  // Configuración de SLA (en días)
  SLA: {
    SENT_DEADLINE_DAY: 6, // Día 6 del mes para envío
    DISPERSION_DEADLINE_DAY: 22, // Día 22 del mes para dispersión
    INVOICE_LETTER_REQUEST_DAY: 1, // Día 1 del siguiente mes para solicitud de cartas
    INVOICE_LETTER_ARRIVAL_DAY: 4, // Día 4 del siguiente mes para llegada de cartas
    WARNING_DAYS_BEFORE: 2, // Días antes del vencimiento para enviar advertencia
  },

  // Configuración de Slack
  SLACK: {
    WEBHOOK_URL: process.env.SLACK_WEBHOOK_URL || '',
    CHANNEL: process.env.FLEET_ORDERS_SLACK_CHANNEL || '#fleet-orders',
    ENABLED: process.env.FLEET_ORDERS_SLACK_ENABLED === 'true',
  },

  // Configuración de cron jobs
  CRON: {
    SLA_CHECK_SCHEDULE: '0 8-18 * * 1-5', // Cada hora de 8 AM a 6 PM, Lun-Vie
    DAILY_SUMMARY_SCHEDULE: '0 9 * * *', // 9:00 AM todos los días
    CLEANUP_SCHEDULE: '0 2 * * 0', // 2:00 AM todos los domingos
    CRITICAL_ALERTS_SCHEDULE: '*/30 * * * *', // Cada 30 minutos
    TIMEZONE: 'America/Mexico_City',
  },

  // Configuración de paginación
  PAGINATION: {
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100,
  },

  // Configuración de archivos
  FILES: {
    MAX_EVIDENCE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_EVIDENCE_TYPES: ['image/jpeg', 'image/png', 'application/pdf'],
    EVIDENCE_UPLOAD_PATH: 'fleet-orders/evidence/',
  },

  // Configuración de validaciones
  VALIDATION: {
    MIN_YEAR: 2024,
    MAX_VEHICLES_PER_ORDER: 50,
    MAX_NOTIFICATION_EMAILS: 10,
    MIN_UNIT_PRICE: 0,
    MAX_UNIT_PRICE: 10000000, // 10 millones
  },

  // Estados que requieren evidencia obligatoria
  EVIDENCE_REQUIRED_STATES: [
    'sent',
    'dispersion',
    'invoice_letter_request',
    'invoice_letter_arrival',
    'supplier_notification',
  ],

  // Transiciones de estado válidas
  VALID_STATUS_TRANSITIONS: {
    created: ['sent'],
    sent: ['dispersion'],
    dispersion: ['invoice_letter_request'],
    invoice_letter_request: ['invoice_letter_arrival'],
    invoice_letter_arrival: ['supplier_notification'],
    supplier_notification: ['waiting_for_cars'],
    waiting_for_cars: ['delivered'],
    delivered: [], // Estado final
  },

  // Configuración de limpieza
  CLEANUP: {
    OLD_ALERTS_DAYS: 30, // Días después de los cuales se eliminan alertas resueltas
  },
};

/**
 * Validar configuración al inicializar
 */
export function validateFleetOrdersConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validar configuración de SLA
  if (FLEET_ORDERS_CONFIG.SLA.SENT_DEADLINE_DAY < 1 || FLEET_ORDERS_CONFIG.SLA.SENT_DEADLINE_DAY > 31) {
    errors.push('SENT_DEADLINE_DAY debe estar entre 1 y 31');
  }

  if (FLEET_ORDERS_CONFIG.SLA.DISPERSION_DEADLINE_DAY < 1 || FLEET_ORDERS_CONFIG.SLA.DISPERSION_DEADLINE_DAY > 31) {
    errors.push('DISPERSION_DEADLINE_DAY debe estar entre 1 y 31');
  }

  if (FLEET_ORDERS_CONFIG.SLA.WARNING_DAYS_BEFORE < 1) {
    errors.push('WARNING_DAYS_BEFORE debe ser mayor a 0');
  }

  // Validar configuración de archivos
  if (FLEET_ORDERS_CONFIG.FILES.MAX_EVIDENCE_SIZE < 1024) {
    errors.push('MAX_EVIDENCE_SIZE debe ser al menos 1KB');
  }

  // Validar configuración de validaciones
  if (FLEET_ORDERS_CONFIG.VALIDATION.MIN_YEAR < 2020) {
    errors.push('MIN_YEAR debe ser al menos 2020');
  }

  if (FLEET_ORDERS_CONFIG.VALIDATION.MAX_VEHICLES_PER_ORDER < 1) {
    errors.push('MAX_VEHICLES_PER_ORDER debe ser mayor a 0');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Obtener configuración de Slack
 */
export function getSlackConfig() {
  return {
    isEnabled: FLEET_ORDERS_CONFIG.SLACK.ENABLED && !!FLEET_ORDERS_CONFIG.SLACK.WEBHOOK_URL,
    webhookUrl: FLEET_ORDERS_CONFIG.SLACK.WEBHOOK_URL,
    channel: FLEET_ORDERS_CONFIG.SLACK.CHANNEL,
  };
}

/**
 * Obtener configuración de SLA para un mes específico
 */
export function getSLAConfigForMonth(year: number, month: number) {
  const nextMonth = month === 12 ? 1 : month + 1;
  const nextYear = month === 12 ? year + 1 : year;

  return {
    sentDeadline: new Date(year, month - 1, FLEET_ORDERS_CONFIG.SLA.SENT_DEADLINE_DAY, 23, 59, 59, 999),
    dispersionDeadline: new Date(year, month - 1, FLEET_ORDERS_CONFIG.SLA.DISPERSION_DEADLINE_DAY, 23, 59, 59, 999),
    invoiceLetterRequestDeadline: new Date(nextYear, nextMonth - 1, FLEET_ORDERS_CONFIG.SLA.INVOICE_LETTER_REQUEST_DAY, 23, 59, 59, 999),
    invoiceLetterArrivalDeadline: new Date(nextYear, nextMonth - 1, FLEET_ORDERS_CONFIG.SLA.INVOICE_LETTER_ARRIVAL_DAY, 23, 59, 59, 999),
  };
}
