import { Router } from 'express';
import cronJobTempPayments from './tempSuscription.routes';
import { verifyCronJobToken } from '../../../middlewares/verifyToken';

const routes = Router();

cronJobTempPayments.use((req, res, next) => {
  // Ejecuta el middleware verifyCronJobToken si la ruta es /job-handler
  if (req.path.includes('/job-handler') && req.path !== '/job-handler/login') {
    verifyCronJobToken(req, res, next);
  } else {
    next(); // Si no es la ruta específica, pasamos al siguiente middleware sin verificar
  }
});

routes.use('/job-handler', cronJobTempPayments);

export default routes;
