import { Async<PERSON>ontroller } from '@/types&interfaces/types';
import { ServiceTypeVendorModel } from '../models/serviceType.model';

export const createServiceType: AsyncController = async (req, res) => {
  try {
    req.body.organization = req.params.organizationId;
    const serviceType = await ServiceTypeVendorModel.create(req.body);
    return res.status(201).send({ message: 'Service type created', data: serviceType });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const getAllServicesType: AsyncController = async (req, res) => {
  try {
    const { organizationId } = req.params;
    const { maintenanceType } = req.query;

    const query: any = {
      organization: organizationId,
      isActive: true,
    };

    // Filter by maintenance type if specified
    if (maintenanceType && (maintenanceType === 'preventive' || maintenanceType === 'corrective')) {
      query.maintenanceType = maintenanceType;
    }

    const serviceTypes = await ServiceTypeVendorModel.find(query);
    return res.status(200).send({ message: 'Service types found', data: serviceTypes });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const getServiceTypeById: AsyncController = async (req, res) => {
  try {
    const serviceType = await ServiceTypeVendorModel.findById(req.params.id);
    if (!serviceType) {
      return res.status(404).send({ error: 'Service type not found' });
    }
    return res.status(200).send({ message: 'Service type found', data: serviceType });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const updateServiceType: AsyncController = async (req, res) => {
  try {
    const serviceType = await ServiceTypeVendorModel.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    });
    if (!serviceType) {
      return res.status(404).send({ error: 'Service type not found' });
    }
    return res.status(200).send({ message: 'Service type updated', data: serviceType });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const deleteServiceType: AsyncController = async (req, res) => {
  try {
    // Soft delete - solo marcar como inactivo
    const serviceType = await ServiceTypeVendorModel.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    if (!serviceType) {
      return res.status(404).send({ error: 'Service type not found' });
    }
    return res.status(200).send({ message: 'Service type deleted', data: serviceType });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};
