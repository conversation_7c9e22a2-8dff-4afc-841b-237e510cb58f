import { NextFunction, Request, Response } from 'express';
import { logger } from '../clean/lib/logger';
import { FileUploadRetriesRep } from '../models/fileUploadRetriesSchema';
import { FileUploadRetries } from '../clean/domain/entities';
import { MAX_UPLOAD_REQUESTS } from '../constants';

export const ocrLimitTriesMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  logger.info(`[ocrLimitTriesMiddleware] middleware intercepted call`);
  const { requestId, platform, uploadType } = req.body;
  const file: Express.Multer.File | undefined = req.file;
  if (!file || !requestId || !platform || !uploadType) {
    logger.error({
      status: 400,
      message: 'Missing data',
      requestId: requestId,
      platform: platform,
      method: 'ocrLimitTriesMiddleware',
    });
    return res.status(400).send({ message: 'Missing data' });
  }
  const fileMetaData = {
    originalName: file?.originalname || '',
    mimeType: file?.mimetype || '',
    contentLength: file?.size || 0,
  };
  const existingFileRetryData = await FileUploadRetriesRep.findOne({
    requestId,
    platform,
    uploadType,
    fileMetaData,
  });

  if (!existingFileRetryData) {
    const fileUploadRetries = new FileUploadRetries({
      requestId,
      platform,
      uploadType,
      fileMetaData,
      count: 1,
    });
    await FileUploadRetriesRep.create(fileUploadRetries);
    logger.info(`[ocrLimitTriesMiddleware] first attempt made at uploading image ${fileUploadRetries}`);
  } else {
    if (existingFileRetryData.count >= MAX_UPLOAD_REQUESTS) {
      logger.error(
        `[ocrLimitTriesMiddleware] multiple attempt made at uploading image ${existingFileRetryData}`
      );
      return res
        .status(429)
        .json({ message: 'This document has been uploaded multiple times. Invalid Data.' });
    } else {
      existingFileRetryData.count += 1;
      await existingFileRetryData.save();
      logger.info(
        `[ocrLimitTriesMiddleware] multiple attempt made at uploading image count increased ${existingFileRetryData}`
      );
    }
  }
  return next();
};
