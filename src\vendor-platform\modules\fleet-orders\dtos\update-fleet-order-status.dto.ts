import { FleetOrderStatus, EvidenceType } from '../models/fleet-order.model';

export interface UpdateFleetOrderStatusDTO {
  status: FleetOrderStatus;
  evidence?: {
    type: EvidenceType;
    url?: string;
    description: string;
  };
  notes?: string;
}

/**
 * Valida los datos para actualizar el estado de una orden de flota
 */
export function validateUpdateFleetOrderStatusDTO(data: any): {
  isValid: boolean;
  errors: string[];
  validatedData?: UpdateFleetOrderStatusDTO;
} {
  const errors: string[] = [];

  // Validar status
  if (!data.status || typeof data.status !== 'string') {
    errors.push('El estado es requerido y debe ser una cadena de texto');
  } else if (!Object.values(FleetOrderStatus).includes(data.status as FleetOrderStatus)) {
    errors.push(`Estado inválido. Estados válidos: ${Object.values(FleetOrderStatus).join(', ')}`);
  }

  // Validar evidence si está presente
  if (data.evidence) {
    const evidenceErrors = validateEvidenceDTO(data.evidence);
    errors.push(...evidenceErrors);
  }

  // Validar notes si está presente
  if (data.notes && typeof data.notes !== 'string') {
    errors.push('Las notas deben ser una cadena de texto');
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    validatedData: {
      status: data.status,
      evidence: data.evidence,
      notes: data.notes,
    },
  };
}

/**
 * Valida los datos de evidencia
 */
function validateEvidenceDTO(evidence: any): string[] {
  const errors: string[] = [];

  if (!evidence.type || typeof evidence.type !== 'string') {
    errors.push('El tipo de evidencia es requerido');
  } else if (!Object.values(EvidenceType).includes(evidence.type as EvidenceType)) {
    errors.push(`Tipo de evidencia inválido. Tipos válidos: ${Object.values(EvidenceType).join(', ')}`);
  }

  if (!evidence.description || typeof evidence.description !== 'string') {
    errors.push('La descripción de la evidencia es requerida');
  }

  if (evidence.url && typeof evidence.url !== 'string') {
    errors.push('La URL de la evidencia debe ser una cadena de texto');
  }

  return errors;
}

/**
 * Verifica si un estado requiere evidencia obligatoria
 */
export function isEvidenceRequired(status: FleetOrderStatus): boolean {
  const statusesRequiringEvidence = [
    FleetOrderStatus.SENT,
    FleetOrderStatus.DISPERSION,
    FleetOrderStatus.INVOICE_LETTER_REQUEST,
    FleetOrderStatus.INVOICE_LETTER_ARRIVAL,
    FleetOrderStatus.SUPPLIER_NOTIFICATION,
  ];

  return statusesRequiringEvidence.includes(status);
}

/**
 * Obtiene los tipos de evidencia válidos para un estado
 */
export function getValidEvidenceTypesForStatus(status: FleetOrderStatus): EvidenceType[] {
  switch (status) {
    case FleetOrderStatus.SENT:
    case FleetOrderStatus.INVOICE_LETTER_REQUEST:
    case FleetOrderStatus.SUPPLIER_NOTIFICATION:
      return [EvidenceType.LOG];
    
    case FleetOrderStatus.DISPERSION:
      return [EvidenceType.LOG, EvidenceType.DOCUMENT];
    
    case FleetOrderStatus.INVOICE_LETTER_ARRIVAL:
      return [EvidenceType.PHOTO, EvidenceType.PDF];
    
    default:
      return Object.values(EvidenceType);
  }
}
