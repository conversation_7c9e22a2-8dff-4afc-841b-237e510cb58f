/* eslint-disable max-params */
import { instanceToPlain } from 'class-transformer';
import {
  AdmissionRequest,
  Document,
  Pagination,
  RequestDocument,
  RequestPersonalData,
  RequestDocumentsAnalysis,
  HomeVisit,
  WeeklyEarning,
  Metric,
  Event,
  PlatformMetric,
} from '../domain/entities';
import { AppException } from '../errors/exceptions';

class Envelope<T> {
  success: boolean;

  data: T;

  error: AppException | null;

  pagination: Pagination | null;

  constructor(
    success: boolean = true,
    data: T | null = null,
    error: AppException | null = null,
    pagination: Pagination | null = null
  ) {
    this.success = success;
    this.data = data as T;
    this.error = error;
    this.pagination = pagination;
  }
}

export const exceptionSerializer = (exception: AppException) => {
  const envelope = new Envelope<null>(false, null, exception);
  return instanceToPlain(envelope);
};

export const AdmissionRequestSerializer = (admissionRequest: AdmissionRequest) => {
  const envelope = new Envelope<AdmissionRequest>(true, admissionRequest);
  return instanceToPlain(envelope);
};

export const AdmissionRequestFailureSerializer = (responsePayload: Record<string, any>) => {
  const envelope = new Envelope<typeof responsePayload>(true, responsePayload);
  return instanceToPlain(envelope);
};

export const RequestPersonalDataSerializer = (requestPersonalData: RequestPersonalData) => {
  const envelope = new Envelope<RequestPersonalData>(true, requestPersonalData);
  return instanceToPlain(envelope);
};

export const documentSerializer = (document: Document | Array<Document>) => {
  const envelope = new Envelope<Document | Array<Document>>(true, document);
  return instanceToPlain(envelope);
};

export const requestDocumentsSerializer = (requestDocuments: RequestDocument) => {
  const envelope = new Envelope<RequestDocument>(true, requestDocuments);
  return instanceToPlain(envelope);
};

export const paginatedSerializer = <T>(data: T[], pagination: Pagination) => {
  const envelope = new Envelope<T[]>(true, data, null, pagination);
  return instanceToPlain(envelope);
};

export const requestDocumentsAnalysisSerializer = (requestDocumentsAnalysis: RequestDocumentsAnalysis) => {
  const envelope = new Envelope<RequestDocumentsAnalysis>(true, requestDocumentsAnalysis);
  return instanceToPlain(envelope);
};

export const requestHomeVisitSerializer = (requestHomeVisit: HomeVisit) => {
  const envelope = new Envelope<HomeVisit>(true, requestHomeVisit);
  return instanceToPlain(envelope);
};

export const requestWeeklyEarningsSerializer = (requestWeeklyEarnings: WeeklyEarning[]) => {
  const envelope = new Envelope<WeeklyEarning[]>(true, requestWeeklyEarnings);
  return instanceToPlain(envelope);
};

export const requestMetricSerializer = (requestMetric: Metric) => {
  const envelope = new Envelope<Metric>(true, requestMetric);
  return instanceToPlain(envelope);
};

export const eventsSerializer = (events: Event[]) => {
  const envelope = new Envelope<Event[]>(true, events);
  return instanceToPlain(envelope);
};

export const publicAdmissionRequestSerializer = (admissionRequest: AdmissionRequest) => {
  const envelope = new Envelope<AdmissionRequest>(true, admissionRequest);
  return instanceToPlain(envelope);
};

export const PlatformMetricSerializer = (platformMetric: PlatformMetric) => {
  const envelope = new Envelope<PlatformMetric>(true, platformMetric);
  return instanceToPlain(envelope);
};

export const ScheduleSerializer = (schedule: Record<string, any>) => {
  const envelope = new Envelope(true, schedule);
  return envelope;
};

export const SlotsSerializer = (slots: Record<string, any> | Array<Record<string, any>>) => {
  const envelope = new Envelope(true, slots);
  return envelope;
};

export const AppointmentSerializer = (appointment: Record<string, any> | Array<Record<string, any>>) => {
  const envelope = new Envelope(true, appointment);
  return envelope;
};

export const UsersWithAvailableSlotSerializer = (users: Record<string, any> | Array<Record<string, any>>) => {
  const envelope = new Envelope(true, users);
  return envelope;
};
