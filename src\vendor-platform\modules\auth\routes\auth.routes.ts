import { Router } from 'express';
import { completeRegister, signInVendor, verifyInvitationToken } from '../controllers/auth.controller';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';

const authRouter = Router();

const authUrl = '/auth';

authRouter.post(authUrl + '/login', errorHandlerV2(signInVendor));
authRouter.post(authUrl + '/verify-invitation-token', errorHandlerV2(verifyInvitationToken));
authRouter.post(authUrl + '/complete-register', errorHandlerV2(completeRegister));

export default authRouter;
