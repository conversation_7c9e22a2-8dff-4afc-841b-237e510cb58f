import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { FleetOrdersService } from '../services/fleet-orders.service';
import { SLAMonitoringService } from '../services/sla-monitoring.service';
import { SlackNotificationsService } from '../services/slack-notifications.service';
import { validateCreateFleetOrderDTO } from '../dtos/create-fleet-order.dto';
import { validateUpdateFleetOrderStatusDTO, isEvidenceRequired } from '../dtos/update-fleet-order-status.dto';
import { validateUpdateDispersionDTO, validateDispersionConsistency } from '../dtos/dispersion.dto';
import { FleetOrderStatus } from '../models/fleet-order.model';

const fleetOrdersService = new FleetOrdersService();
const slaMonitoringService = new SLAMonitoringService();
const slackNotificationsService = new SlackNotificationsService();

/**
 * Crear una nueva orden de flota
 */
export const createFleetOrder = async (req: Request, res: Response) => {
  try {
    const createdBy = new Types.ObjectId(req.userVendor!.userId);
    
    // Validar datos de entrada
    const validation = validateCreateFleetOrderDTO(req.body);
    if (!validation.isValid) {
      return res.status(400).json({
        message: 'Datos de entrada inválidos',
        errors: validation.errors,
        code: 'VALIDATION_ERROR'
      });
    }

    // Crear la orden
    const order = await fleetOrdersService.createFleetOrder(validation.validatedData!, createdBy);

    // Enviar notificación a Slack
    try {
      await slackNotificationsService.sendOrderCreatedNotification(order);
    } catch (slackError) {
      console.error('Error sending Slack notification:', slackError);
      // No fallar la creación por error de Slack
    }

    res.status(201).json({
      message: 'Orden de flota creada exitosamente',
      data: order
    });
  } catch (error: any) {
    console.error('Error creating fleet order:', error);
    
    if (error.message.includes('Ya existe una orden')) {
      return res.status(409).json({
        message: error.message,
        code: 'ORDER_ALREADY_EXISTS'
      });
    }

    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Obtener una orden por ID
 */
export const getFleetOrderById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    const order = await fleetOrdersService.getFleetOrderById(new Types.ObjectId(id));
    
    if (!order) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    res.status(200).json({
      message: 'Orden obtenida exitosamente',
      data: order
    });
  } catch (error) {
    console.error('Error getting fleet order:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Obtener orden por número de orden
 */
export const getFleetOrderByNumber = async (req: Request, res: Response) => {
  try {
    const { orderNumber } = req.params;
    
    const order = await fleetOrdersService.getFleetOrderByNumber(orderNumber);
    
    if (!order) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    res.status(200).json({
      message: 'Orden obtenida exitosamente',
      data: order
    });
  } catch (error) {
    console.error('Error getting fleet order by number:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Listar órdenes con filtros y paginación
 */
export const listFleetOrders = async (req: Request, res: Response) => {
  try {
    const { status, month, year, page = 1, limit = 10 } = req.query;
    
    const filters: any = {
      page: Number(page),
      limit: Number(limit),
    };
    
    if (status) filters.status = status as FleetOrderStatus;
    if (month) filters.month = Number(month);
    if (year) filters.year = Number(year);

    const result = await fleetOrdersService.listFleetOrders(filters);

    res.status(200).json({
      message: 'Órdenes obtenidas exitosamente',
      data: result.orders,
      pagination: {
        page: result.page,
        limit: Number(limit),
        total: result.total,
        totalPages: result.totalPages,
      }
    });
  } catch (error) {
    console.error('Error listing fleet orders:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Actualizar estado de una orden
 */
export const updateFleetOrderStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updatedBy = new Types.ObjectId(req.userVendor!.userId);
    
    if (!Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    // Validar datos de entrada
    const validation = validateUpdateFleetOrderStatusDTO(req.body);
    if (!validation.isValid) {
      return res.status(400).json({
        message: 'Datos de entrada inválidos',
        errors: validation.errors,
        code: 'VALIDATION_ERROR'
      });
    }

    // Verificar si se requiere evidencia para este estado
    if (isEvidenceRequired(validation.validatedData!.status) && !validation.validatedData!.evidence) {
      return res.status(400).json({
        message: `Evidencia requerida para el estado "${validation.validatedData!.status}"`,
        code: 'EVIDENCE_REQUIRED'
      });
    }

    // Obtener la orden actual para comparar estados
    const currentOrder = await fleetOrdersService.getFleetOrderById(new Types.ObjectId(id));
    if (!currentOrder) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    const oldStatus = currentOrder.status;

    // Actualizar la orden
    const updatedOrder = await fleetOrdersService.updateFleetOrderStatus(
      new Types.ObjectId(id),
      validation.validatedData!,
      updatedBy
    );

    // Enviar notificación a Slack
    try {
      await slackNotificationsService.sendStatusChangeNotification(
        updatedOrder!,
        oldStatus,
        validation.validatedData!.status
      );
    } catch (slackError) {
      console.error('Error sending Slack notification:', slackError);
      // No fallar la actualización por error de Slack
    }

    // Resolver alertas de SLA para esta orden si cambió de estado
    if (oldStatus !== validation.validatedData!.status) {
      await slaMonitoringService.resolveAlertsForOrder(new Types.ObjectId(id));
    }

    res.status(200).json({
      message: 'Estado de orden actualizado exitosamente',
      data: updatedOrder
    });
  } catch (error: any) {
    console.error('Error updating fleet order status:', error);
    
    if (error.message.includes('Transición de estado inválida')) {
      return res.status(400).json({
        message: error.message,
        code: 'INVALID_STATUS_TRANSITION'
      });
    }

    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Actualizar dispersión de una orden
 */
export const updateDispersion = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updatedBy = new Types.ObjectId(req.userVendor!.userId);
    
    if (!Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    // Validar datos de entrada
    const validation = validateUpdateDispersionDTO(req.body);
    if (!validation.isValid) {
      return res.status(400).json({
        message: 'Datos de dispersión inválidos',
        errors: validation.errors,
        code: 'VALIDATION_ERROR'
      });
    }

    // Obtener la orden para validar consistencia
    const order = await fleetOrdersService.getFleetOrderById(new Types.ObjectId(id));
    if (!order) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Validar consistencia de la dispersión
    const consistencyValidation = validateDispersionConsistency(
      validation.validatedData!.dispersion,
      order.totalUnits,
      order.totalAmount
    );

    if (!consistencyValidation.isValid) {
      return res.status(400).json({
        message: 'Dispersión inconsistente con la orden',
        errors: consistencyValidation.errors,
        code: 'DISPERSION_INCONSISTENCY'
      });
    }

    // Actualizar la dispersión
    const updatedOrder = await fleetOrdersService.updateDispersion(
      new Types.ObjectId(id),
      validation.validatedData!.dispersion,
      updatedBy
    );

    res.status(200).json({
      message: 'Dispersión actualizada exitosamente',
      data: updatedOrder
    });
  } catch (error: any) {
    console.error('Error updating dispersion:', error);
    
    if (error.message.includes('Solo se puede actualizar la dispersión')) {
      return res.status(400).json({
        message: error.message,
        code: 'INVALID_STATUS_FOR_DISPERSION'
      });
    }

    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Eliminar una orden (solo si está en estado CREATED)
 */
export const deleteFleetOrder = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    await fleetOrdersService.deleteFleetOrder(new Types.ObjectId(id));

    res.status(200).json({
      message: 'Orden eliminada exitosamente'
    });
  } catch (error: any) {
    console.error('Error deleting fleet order:', error);
    
    if (error.message.includes('Solo se pueden eliminar órdenes')) {
      return res.status(400).json({
        message: error.message,
        code: 'CANNOT_DELETE_ORDER'
      });
    }

    if (error.message.includes('Orden no encontrada')) {
      return res.status(404).json({
        message: error.message,
        code: 'ORDER_NOT_FOUND'
      });
    }

    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};
