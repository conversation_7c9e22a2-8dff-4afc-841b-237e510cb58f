import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';

export interface ICrew extends Document {
  cityId: mongoose.Types.ObjectId;
  companyId: mongoose.Types.ObjectId;
  name: string;
  active: boolean;
}

const CrewSchema = new Schema(
  {
    cityId: { type: Schema.Types.ObjectId, ref: 'City', required: true },
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
    name: { type: String, required: true },
    active: { type: Boolean, default: true },
  },
  { timestamps: true }
);

export const CrewVendorModel = vendorDB.model<ICrew>('Crew', CrewSchema);
