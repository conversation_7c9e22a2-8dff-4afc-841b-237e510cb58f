import { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';

export interface ICompany extends Document {
  name: string;
  // address: string;
  phone: string;
  email: string;
  // defaultInstallationDuration: number; // en minutos
  // defaultBreakTime: {
  //   start: string; // Format "HH:mm"
  //   end: string; // Format "HH:mm"
  // };
}

const CompanySchema = new Schema(
  {
    name: { type: String, required: true },
    // address: { type: String },
    phone: { type: String },
    email: { type: String },
    // defaultInstallationDuration: { type: Number, default: 180 }, // 3 horas por defecto
    // defaultBreakTime: {
    //   start: { type: String /* default: '13:00' */ },
    //   end: { type: String /* default: '14:00' */ },
    // },
    // defaultMaxSimultaneousInstallations: { type: Number, default: 1 },
  },
  { timestamps: true }
);

export const CompanyVendorModel = vendorDB.model<ICompany>('Company', CompanySchema);
