import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  createUserPermissions,
  getUserPermissions,
  getMyPermissions,
  updateUserPermissions,
  getUsersByType,
  getUsersByOrganization,
  deactivateUserPermissions,
  checkUserPermission,
  checkUserRole
} from '../controllers/user-roles-permissions.controller';
import { 
  checkOCNUser, 
  checkPermission, 
  checkOrganizationAccess 
} from '../middlewares/check-permissions.middleware';

const userRolesPermissionsRouter = Router();

const baseUrl = '/user-roles-permissions';

// ==================== RUTAS PÚBLICAS (con autenticación básica) ====================

/**
 * Obtener mis permisos
 * GET /vendor-platform/user-roles-permissions/me
 */
userRolesPermissionsRouter.get(
  baseUrl + '/me',
  verifyTokenVendorPlatform,
  errorHandlerV2(getMyPermissions)
);

// ==================== RUTAS PARA USUARIOS OCN ====================

/**
 * Crear permisos para un usuario (solo OCN)
 * POST /vendor-platform/user-roles-permissions
 */
userRolesPermissionsRouter.post(
  baseUrl,
  verifyTokenVendorPlatform,
  checkOCNUser,
  errorHandlerV2(createUserPermissions)
);

/**
 * Obtener permisos de un usuario específico (solo OCN)
 * GET /vendor-platform/user-roles-permissions/:userId
 */
userRolesPermissionsRouter.get(
  baseUrl + '/:userId',
  verifyTokenVendorPlatform,
  checkOCNUser,
  errorHandlerV2(getUserPermissions)
);

/**
 * Actualizar permisos de un usuario (solo OCN)
 * PATCH /vendor-platform/user-roles-permissions/:userId
 */
userRolesPermissionsRouter.patch(
  baseUrl + '/:userId',
  verifyTokenVendorPlatform,
  checkOCNUser,
  errorHandlerV2(updateUserPermissions)
);

/**
 * Listar usuarios por tipo (solo OCN)
 * GET /vendor-platform/user-roles-permissions/type/:userType
 */
userRolesPermissionsRouter.get(
  baseUrl + '/type/:userType',
  verifyTokenVendorPlatform,
  checkOCNUser,
  errorHandlerV2(getUsersByType)
);

/**
 * Desactivar permisos de un usuario (solo OCN)
 * DELETE /vendor-platform/user-roles-permissions/:userId
 */
userRolesPermissionsRouter.delete(
  baseUrl + '/:userId',
  verifyTokenVendorPlatform,
  checkOCNUser,
  errorHandlerV2(deactivateUserPermissions)
);

/**
 * Verificar permiso específico de un usuario (solo OCN)
 * GET /vendor-platform/user-roles-permissions/:userId/check-permission
 */
userRolesPermissionsRouter.get(
  baseUrl + '/:userId/check-permission',
  verifyTokenVendorPlatform,
  checkOCNUser,
  errorHandlerV2(checkUserPermission)
);

/**
 * Verificar rol específico de un usuario (solo OCN)
 * POST /vendor-platform/user-roles-permissions/:userId/check-role
 */
userRolesPermissionsRouter.post(
  baseUrl + '/:userId/check-role',
  verifyTokenVendorPlatform,
  checkOCNUser,
  errorHandlerV2(checkUserRole)
);

// ==================== RUTAS PARA ORGANIZACIONES ====================

/**
 * Listar usuarios de una organización específica
 * GET /vendor-platform/user-roles-permissions/organization/:organizationId/users
 * 
 * Acceso:
 * - Usuarios OCN: pueden ver cualquier organización
 * - Usuarios de organización: solo pueden ver su propia organización
 */
userRolesPermissionsRouter.get(
  baseUrl + '/organization/:organizationId/users',
  verifyTokenVendorPlatform,
  checkOrganizationAccess('organizationId'),
  errorHandlerV2(getUsersByOrganization)
);

export default userRolesPermissionsRouter;
