import { AllowedMimeType, parseTextFromSource, Source } from './llmClients/geminiClient';
import { logger } from '@/clean/lib/logger';

const prompt = `Extract text from the provided Uber profile screenshot and output a structured JSON object using the following JSON schema:

{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "fullName": {
      "type": "string",
      "examples": ["John Smith"]
    },
    "rating": {
      "type": "number",
      "examples": [4.95]
    },
    "completedTrips": {
      "type": "integer",
      "examples": [1250]
    },
    "acceptanceRate": {
      "type": "integer",
      "examples": [98, 34, 45]
    },
    "cancellationRate": {
      "type": "integer",
      "examples": [20, 34, 88]
    },
    "timeUsingApp": {
      "type": "float",
      "unit": "years",
      "examples": ["2.5"]
    }
  },
  "required": ["fullName", "rating", "completedTrips", "acceptanceRate", "cancellationRate", "timeUsingApp"]
}

Additional requirements:
- Handle any language input and translate to english
- Return null for any fields not present in the image
- Preserve any special characters in names
- Format dates in ISO 8601 format (YYYY-MM-DD) when possible
- If time using app is a range example "3 - 7 months" output the higher range value along with month or year example "7 months"
- If time using app is a in terms of years and months example "3 years and 7 months" output the value as "3.7 years"
- Do not include any thing else in the response other than that above JSON structure
- The response should be a valid JSON Object
- Do not use Markdown code block syntax in response`;

const getUberProfileData = async (image: Express.Multer.File) => {
  try {
    // Validate the image object
    if (!image || !image.buffer || !image.mimetype) {
      throw new Error('Invalid image file provided');
    }

    // Validate MIME type
    const allowedMimeTypes: AllowedMimeType[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedMimeTypes.includes(image.mimetype as AllowedMimeType)) {
      throw new Error(`Unsupported MIME type: ${image.mimetype}`);
    }

    // Prepare the source object
    const source: Source = {
      data: image.buffer.toString('base64'),
      media_type: image.mimetype as AllowedMimeType,
      prompt: prompt,
    };

    logger.info(`[getUberProfileData] - Processing image with MIME type: ${source.media_type}`);

    // Call the Anthropic API to parse the image
    const result = await parseTextFromSource(source);
    return result;
  } catch (error) {
    logger.error(`[getUberProfileData] - Error: ${JSON.stringify(error)}`);
    throw new Error(`Failed to extract profile data: ${JSON.stringify(error)}`);
  }
};

export { getUberProfileData };
