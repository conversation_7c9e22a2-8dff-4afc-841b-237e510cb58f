import { GetObjectCommand, PutObjectCommand, S3Client, DeleteObjectCommand } from '@aws-sdk/client-s3';
import {
  AWS_BUCKET_NAME,
  AWS_BUCKET_PUBLIC_KEY,
  AWS_BUCKET_REGION,
  AWS_BUCKET_SECRET_KEY,
} from '../../constants';
import { CouldNotSignUrlException, CouldNotUploadMediaException } from '../errors/exceptions';
import { MediaType } from '../domain/enums';
import { Types } from 'mongoose';
import { logger } from '../lib/logger';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Readable } from 'node:stream';
import fs from 'node:fs';

const client = new S3Client({
  region: AWS_BUCKET_REGION,
  credentials: {
    accessKeyId: AWS_BUCKET_PUBLIC_KEY,
    secretAccessKey: AWS_BUCKET_SECRET_KEY,
  },
});

export const repoUploadMedia = async ({
  file,
  mediaType,
}: {
  file: Express.Multer.File;
  mediaType: MediaType;
}): Promise<[string, string]> => {
  const randomString = new Types.ObjectId().toString();
  const path = `media/${mediaType}/${randomString}.${file.originalname.split('.').pop()}`;

  const stream = file.buffer ? Readable.from(file.buffer) : fs.createReadStream(file.path);

  const params = {
    Bucket: AWS_BUCKET_NAME,
    Key: path,
    Body: stream,
    ContentType: file.mimetype,
  };

  try {
    const command = new PutObjectCommand(params);
    await client.send(command);
    return [path, file.mimetype];
  } catch (error) {
    logger.error(`[repoUploadMedia] - ${error}`);
    throw new CouldNotUploadMediaException();
  }
};

export const repoGetMediaSignedUrl = async (path: string): Promise<string> => {
  const params = {
    Bucket: AWS_BUCKET_NAME,
    Key: path,
  };

  try {
    const command = new GetObjectCommand(params);
    const url = await getSignedUrl(client, command, { expiresIn: 3600 });
    return url;
  } catch (error) {
    logger.error(`[repoGetMediaSignedUrl] - ${error}`);
    throw new CouldNotSignUrlException();
  }
};

export const repoGeneratePresignedPutUrl = async (s3Key: string, contentType: string): Promise<string> => {
  const params = {
    Bucket: AWS_BUCKET_NAME,
    Key: s3Key,
    ContentType: contentType,
  };

  try {
    const command = new PutObjectCommand(params);
    const url = await getSignedUrl(client, command, { expiresIn: 3600 }); // 1 hour expiry
    logger.info(`[repoGeneratePresignedPutUrl] - Successfully generated presigned PUT URL for key: ${s3Key}`);
    return url;
  } catch (error) {
    logger.error(
      `[repoGeneratePresignedPutUrl] - Error generating presigned PUT URL for key ${s3Key}: ${error}`
    );
    throw new CouldNotSignUrlException();
  }
};

// Helper function to convert stream to buffer
const streamToBuffer = async (stream: Readable): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    stream.on('data', (chunk: Buffer) => chunks.push(chunk));
    stream.on('error', reject);
    stream.on('end', () => resolve(Buffer.concat(chunks)));
  });
};

/**
 * Retrieves the Base64-encoded document data from the storage service
 *
 * @param path Path to the document (S3 key)
 * @returns Promise resolving to Base64-encoded string of the document data
 */
export const repoGetDocumentData = async (path: string): Promise<string> => {
  try {
    logger.info(`[repoGetDocumentData] Retrieving document data for path: ${path}`);

    const params = {
      Bucket: AWS_BUCKET_NAME,
      Key: path,
    };

    // Create command to get the object using the existing client
    const command = new GetObjectCommand(params);
    const response = await client.send(command);

    if (!response.Body) {
      throw new Error(`Document not found at path: ${path}`);
    }

    // Convert stream to buffer
    const bodyContents = await streamToBuffer(response.Body as Readable);

    // Convert buffer to Base64
    const base64Data = bodyContents.toString('base64');

    logger.info(`[repoGetDocumentData] Successfully retrieved document data for path: ${path}`);
    return base64Data;
  } catch (error: any) {
    logger.error(`[repoGetDocumentData] Error retrieving document data for path: ${path}`, error);
    throw new Error(`Failed to retrieve document data: ${error.message}`);
  }
};

export const repoDeleteDocument = async (path: string): Promise<void> => {
  try {
    const command = new DeleteObjectCommand({ Bucket: AWS_BUCKET_NAME, Key: path });
    await client.send(command);
    logger.info(`[repoDeleteDocument] Successfully deleted document at path: ${path}`);
  } catch (error) {
    logger.error(`[repoDeleteDocument] Failed to delete document at path: ${path}`, error);
  }
};
