import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const wire4PaymentsSchema = new Schema({
  id: {
    type: String,
    required: [true, 'Id is required'],
  },
  object: {
    type: String,
    required: [true, 'Object is required'],
  },
  api_version: {
    type: String,
    required: [true, 'Api version is required'],
  },
  created: {
    type: Date,
    required: [true, 'Created is required'],
  },
  data: {
    beneficiary_account: {
      type: String,
      required: [true, 'Beneficiary account is required'],
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
    },
    currency_code: {
      type: String,
      required: [true, 'Currency code is required'],
    },
    deposit_date: {
      type: Date,
      required: [true, 'Deposit date is required'],
    },
    confirm_date: {
      type: Date,
      required: [true, 'Confirm date is required'],
    },
    depositant: {
      type: String,
      required: [true, 'Depositant is required'],
    },
    depositant_clabe: {
      type: String,
      required: [true, 'Depositant clabe is required'],
    },
    depositant_email: {
      type: String,
      required: [true, 'Depositant email is required'],
    },
    depositant_rfc: {
      type: String,
      required: [true, 'Depositant rfc is required'],
    },
    monex_description: {
      type: String,
      required: [true, 'Monex description is required'],
    },
    monex_transaction_id: {
      type: String,
      required: [true, 'Monex transaction id is required'],
    },
    sender_account: {
      type: String,
      required: [true, 'Sender account is required'],
    },
    sender_name: {
      type: String,
      required: [true, 'Sender name is required'],
    },
    sender_rfc: {
      type: String,
      required: [true, 'Sender rfc is required'],
    },
    description: {
      type: String,
      required: [true, 'Description is required'],
    },
    reference: {
      type: String,
      required: [true, 'Reference is required'],
    },
    clave_rastreo: {
      type: String,
      required: [true, 'Clave rastreo is required'],
    },
  },
  livemode: {
    type: Boolean,
    required: [true, 'Livemode is required'],
  },
  pending_webhooks: {
    type: Number,
    required: [true, 'Pending webhooks is required'],
  },
  type: {
    type: String,
    required: [true, 'Type is required'],
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
wire4PaymentsSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const Wire4PaymentsSchema = model('Wire4PaymentsSchema', wire4PaymentsSchema);

export default Wire4PaymentsSchema;
