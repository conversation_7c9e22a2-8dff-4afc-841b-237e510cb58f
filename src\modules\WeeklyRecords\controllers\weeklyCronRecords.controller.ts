import { getISOWeek, previousMonday } from 'date-fns';
import StockVehicle from '../../../models/StockVehicleSchema';
import { getCurrentDateObject } from '../../../services/timestamps';
import { AsyncController } from '../../../types&interfaces/types';
import WeeklyRecordsModel from '../model/weeklyRecords.model';
import WeeklyDatesModel from '../model/weeklyDates';

export const generateWeeklyRecords: AsyncController = async (req, res) => {
  try {
    const currentStock = await StockVehicle.find();

    const stockRecords = currentStock.map((stock) => {
      return {
        _id: stock._id,
        carNumber: stock.carNumber,
        status: stock.status,
        deliveredDate: stock.deliveredDate[0],
      };
    });

    const today = getCurrentDateObject({
      // useIsoString: '2024-02-25T06:00:00Z',
    });

    // const today2 = new Date('2024-02-25T06:00:00Z'); // for dev purposes
    // console.log('today ', today);
    const startDate = previousMonday(today);

    const year = startDate.getFullYear();

    const weekNumber = getISOWeek(startDate);

    const statusCounts: { [status: string]: number } = currentStock.reduce((acc, stock) => {
      const { status } = stock;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as any);

    const newWeeklyRecord = new WeeklyRecordsModel({
      stockVehicles: stockRecords,
      statusStatistics: statusCounts,
      weekNumber,
      startDate,
      endDate: today,
      year,
    });

    const newWeeklyDate = new WeeklyDatesModel({
      weekNumber,
      startDate,
      year,
    });

    await newWeeklyRecord.save();
    await newWeeklyDate.save();

    return res.status(200).json({ message: 'generateWeeklyRecords' });
  } catch (error) {
    // console.log('[WEEKLY RECORD CRON JOB]', error);
    return res.status(500).json({ message: 'Error', error });
  }
};
