import { Types } from 'mongoose';

import { Document, Schema, model } from 'mongoose';

export interface RiskAnalysisDataMongoI extends Document {
  _id: Types.ObjectId;
  requestId: Types.ObjectId;
  variables: Map<string, number | string>;
  createdAt: Date;
  updatedAt: Date;
}

export const metricsSchema = new Schema(
  {
    requestId: {
      type: Types.ObjectId,
      required: true,
      ref: 'AdmissionRequest',
    },
    variables: {
      type: Map,
      of: Schema.Types.Mixed,
      required: true,
    },
  },
  { timestamps: true }
);

export const RiskAnalysisDataMongo = model<RiskAnalysisDataMongoI>(
  'RiskAnalysisData',
  metricsSchema,
  'riskAnalysisData'
);
