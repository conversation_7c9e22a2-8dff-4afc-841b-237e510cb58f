import { Request, Response } from 'express';
import { Car, ICar } from '../models/carSchema';

// Create a new car entry
export const createCar = async (req: Request, res: Response) => {
  try {
    const carData: ICar = req.body;
    const newCar = new Car(carData);
    await newCar.save();
    return res.status(201).json(newCar);
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({ message: 'Error creating car', error: error.message });
    } else {
      return res.status(500).json({ message: 'Error creating car', error: 'Unknown error' });
    }
  }
};

// Get all cars
export const getAllCars = async (req: Request, res: Response) => {
  try {
    const cars = await Car.find();
    return res.status(200).json(cars);
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({ message: 'Error fetching cars', error: error.message });
    } else {
      return res.status(500).json({ message: 'Error fetching cars', error: 'Unknown error' });
    }
  }
};

// Get a car by make
export const getCarByMake = async (req: Request, res: Response) => {
  try {
    const make = req.params.make;
    const car = await Car.findOne({ make });
    if (!car) {
      return res.status(404).json({ message: 'Car not found' });
    }
    return res.status(200).json(car);
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({ message: 'Error fetching car', error: error.message });
    } else {
      return res.status(500).json({ message: 'Error fetching car', error: 'Unknown error' });
    }
  }
};

// Update a car by make
export const updateCar = async (req: Request, res: Response) => {
  try {
    const make = req.params.make;
    const updateData = req.body;
    const updatedCar = await Car.findOneAndUpdate({ make }, updateData, { new: true });
    if (!updatedCar) {
      return res.status(404).json({ message: 'Car not found' });
    }
    return res.status(200).json(updatedCar);
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({ message: 'Error updating car', error: error.message });
    } else {
      return res.status(500).json({ message: 'Error updating car', error: 'Unknown error' });
    }
  }
};

// Delete a car by make
export const deleteCar = async (req: Request, res: Response) => {
  try {
    const make = req.params.make;
    const deletedCar = await Car.findOneAndDelete({ make });
    if (!deletedCar) {
      return res.status(404).json({ message: 'Car not found' });
    }
    return res.status(200).json({ message: 'Car deleted successfully' });
  } catch (error) {
    if (error instanceof Error) {
      return res.status(500).json({ message: 'Error deleting car', error: error.message });
    } else {
      return res.status(500).json({ message: 'Error deleting car', error: 'Unknown error' });
    }
  }
};
