import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import { AppointmentVendor, AppointmentVendorStatus } from '../../workshop/models/appointment.model';
import { DateTime } from 'luxon';
import { ServiceTypeVendorModel } from '../../serviceType/models/serviceType.model';
import { Types } from 'mongoose';
import { ScheduleService } from '../../workshop/services/schedule.service';
import Associate from '@/models/associateSchema';
import StockVehicle from '@/models/StockVehicleSchema';
import { scheduleAppointmentReminder } from '../../workshop/utils/createReminder';
import { getTimezoneOnCity } from '../../workshop/utils/timezones_on_city';
import { sendNotAttendedNotification } from '../../workshop/utils/sendHilosNotifications';
import {
  sendVehicleMaintenanceAppointmentCancelledFCMNotification,
  sendVehicleMaintenanceAppointmentMissedFCMNotification,
  sendVehicleMaintenanceAppointmentScheduledFCMNotification,
} from '../../workshop/utils/vehicleMaintenanceNotifications';
import {
  sendEmailAboutVehicleMaintenanceAppointmentCancelled,
  sendEmailAboutVehicleMaintenanceAppointmentMissed,
  sendEmailAboutVehicleMaintenanceAppointmentRescheduled,
} from '../platformConnections/emailFunctions';
import { VENDOR_PLATFORM_TALLERS_AGENDAR, VENDOR_PLATFORM_TALLERS_REAGENDAR } from '@/constants';
import { formatDateLongSpanish, formatTimeInHourAndMinutes } from '../../workshop/utils/utils';

export class AppointmentService {
  static async getAppointmentById(
    appointmentId: string,
    populate: { pAssociate: boolean; pStockId: boolean; pServiceType: boolean }
  ) {
    const appointment = await AppointmentVendor.findById(appointmentId)
      .select({
        status: 1,
        associateId: 1,
        stockId: 1,
        startTime: 1,
        endTime: 1,
        workshopId: 1,
        serviceTypeId: 1,
      })
      .populate(populate.pServiceType ? 'service' : '')
      .lean();

    if (!appointment) {
      throw HttpException.NotFound('AppointmentVendor not found');
    }

    if (populate.pAssociate) {
      const associate = await Associate.findById(appointment.associateId).select(
        'firstName lastName email phone'
      );
      if (associate) {
        appointment.associate = associate;
      }
    }

    if (populate.pStockId) {
      const stock = await StockVehicle.findById(appointment.stockId).select('model brand carPlates');
      if (stock) {
        appointment.stock = stock;
      }
    }

    return appointment;
  }

  static async rescheduleAppointment(appointmentId: string, startTime: string, serviceTypeId?: string) {
    const appointment = await AppointmentVendor.findById(appointmentId);

    if (!appointment) {
      throw HttpException.NotFound('AppointmentVendor not found');
    }

    const workshop = await ScheduleService.getWorkshopData(
      appointment.workshopId.toString(),
      'reescheduleAppointment'
    );

    const config = await ScheduleService.getEffectiveScheduleConfigForDate(
      workshop,
      DateTime.fromISO(startTime)
    );

    const serviceType = await ServiceTypeVendorModel.findById(serviceTypeId || appointment.serviceTypeId);

    if (!serviceType) {
      throw HttpException.NotFound('Service type not found');
    }

    const startDateTime = DateTime.fromISO(startTime, {
      zone: config.timezone,
    });

    const isAvailable = await ScheduleService.isSlotAvailable(
      workshop,
      startDateTime,
      serviceType.duration,
      config.capacity
    );

    if (!isAvailable) {
      // throw new Error('Selected time slot is no longer available');
      throw HttpException.BadRequest('Selected time slot is no longer available');
    }

    appointment.startTime = startDateTime.toJSDate();

    const endTime = startDateTime.plus({ minutes: serviceType.duration }).toJSDate();

    appointment.endTime = endTime;

    appointment.serviceTypeId = new Types.ObjectId(serviceTypeId);

    appointment.duration = serviceType.duration;

    appointment.status = AppointmentVendorStatus.rescheduled;

    appointment.statusHistory.push({
      status: AppointmentVendorStatus.rescheduled,
      date: new Date(),
    });

    await appointment.save();

    // TODO:
    // send notification to associate that the appointment was rescheduled
    // create the reminder 1 hour before the appointment
    await ScheduleService.sendAppointmentNotification(appointment);

    const associate = await Associate.findById(appointment.associateId).select('city firstName phone email');
    if (associate) {
      const timezone = getTimezoneOnCity(associate.city);

      await scheduleAppointmentReminder(
        {
          name: associate.firstName,
          phone: associate.phone.toString(),
          mapsLink: workshop.location.mapsLink,
          eventDate: startDateTime.toISO()!,
          workshopName: workshop.name,
        },
        timezone
      );
    }

    const formattedDate = formatDateLongSpanish({
      date: appointment.startTime,
      zone: config.timezone,
      local: 'es',
    });

    const formattedTime = formatTimeInHourAndMinutes({
      date: appointment.startTime,
      zone: config.timezone,
    });

    await sendEmailAboutVehicleMaintenanceAppointmentRescheduled({
      name: associate!.firstName,
      date: formattedDate,
      time: formattedTime,
      link: VENDOR_PLATFORM_TALLERS_REAGENDAR,
      email: associate!.email,
      associateId: appointment.associateId,
    });

    // Send FCM notification to the associate about the rescheduled appointment
    // Same function used for both scheduled and rescheduled appointments
    await sendVehicleMaintenanceAppointmentScheduledFCMNotification({
      userId: appointment.associateId,
      date: formattedDate,
      time: formattedTime,
      rescheduleAppointmentLink: VENDOR_PLATFORM_TALLERS_REAGENDAR,
      workshopName: workshop.name,
      workshopLocation: workshop.location.mapsLink,
      action: AppointmentVendorStatus.rescheduled,
    });

    return appointment;
  }

  static async cancelAppointment(appointmentId: string) {
    const appointment = await AppointmentVendor.findById(appointmentId);

    if (!appointment) {
      throw HttpException.NotFound('AppointmentVendor not found');
    }

    appointment.status = AppointmentVendorStatus.canceled;

    appointment.statusHistory.push({
      status: AppointmentVendorStatus.canceled,
      date: new Date(),
    });

    await appointment.save();

    const associate = await Associate.findById(appointment.associateId).select('firstName email');

    await sendEmailAboutVehicleMaintenanceAppointmentCancelled({
      name: associate!.firstName,
      link: VENDOR_PLATFORM_TALLERS_AGENDAR,
      email: associate!.email,
      associateId: appointment.associateId,
    });

    await sendVehicleMaintenanceAppointmentCancelledFCMNotification({
      userId: appointment.associateId,
    });

    return appointment;
  }

  static async confirmAppointment(appointmentId: string) {
    const appointment = await AppointmentVendor.findById(appointmentId);

    if (!appointment) {
      throw HttpException.NotFound('AppointmentVendor not found');
    }

    appointment.status = AppointmentVendorStatus.completed;

    appointment.statusHistory.push({
      status: AppointmentVendorStatus.completed,
      date: new Date(),
    });

    await appointment.save();

    return appointment;
  }

  static async notAttendedAppointment(appointmentId: string) {
    const appointment = await AppointmentVendor.findById(appointmentId);

    if (!appointment) {
      throw HttpException.NotFound('Appointment not found');
    }

    appointment.status = AppointmentVendorStatus['not-attended'];

    appointment.statusHistory.push({
      status: AppointmentVendorStatus['not-attended'],
      date: new Date(),
    });

    await appointment.save();

    const associate = await Associate.findById(appointment.associateId).select('firstName email city phone');
    const timezone = getTimezoneOnCity(associate!.city);
    const hour = DateTime.fromJSDate(appointment.startTime).setZone(timezone).toFormat('HH:mm');

    const day = DateTime.fromJSDate(appointment.startTime)
      .setLocale('es')
      .setZone(timezone)
      .toFormat("dd 'de' LLLL");

    const text = `${day} a las ${hour}`;

    await sendNotAttendedNotification({
      text,
      appointmentId,
      phone: associate!.phone.toString(),
    });

    await sendEmailAboutVehicleMaintenanceAppointmentMissed({
      name: associate!.firstName,
      link: VENDOR_PLATFORM_TALLERS_AGENDAR,
      email: associate!.email,
      associateId: appointment.associateId,
    });

    await sendVehicleMaintenanceAppointmentMissedFCMNotification({
      userId: appointment.associateId,
      scheduleAppointmentLink: VENDOR_PLATFORM_TALLERS_AGENDAR,
    });

    return appointment;
  }

  static async getLastScheduledAppointmentByAssociateId(associateId: string) {
    const appointment = await AppointmentVendor.findOne({
      associateId,
      // should get scheduled or rescheduled
      status: { $in: [AppointmentVendorStatus.scheduled, AppointmentVendorStatus.rescheduled] },
    })
      .populate('workshop')
      .populate('service', 'name duration')
      .sort({ date: -1 })
      .lean();

    let enrichedAppointment;

    if (appointment) {
      const associate = await Associate.findById(appointment.associateId)
        .select('firstName lastName email phone')
        .lean();

      const stock = await StockVehicle.findById(appointment.stockId)
        .select('carPlates brand model carNumber extensionCarNumber vin')
        .lean();

      enrichedAppointment = {
        ...appointment,
        associate,
        stock,
      };
    }
    return enrichedAppointment;
  }
}
