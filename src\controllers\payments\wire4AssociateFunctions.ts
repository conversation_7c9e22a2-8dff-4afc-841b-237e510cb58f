import { associatePaymentsConsts } from '../../constants';
import AssociatePayments from '../../models/associatePayments';
import { AsyncController } from '../../types&interfaces/types';

export const addTransactionHistoryWire4: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.depositant_email });
    if (!associatePayments) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }
    //Pasos solo para poder completar
    associatePayments.paymentsHistory.push({
      email: data.depositant_email,
      transactionId: data.monex_transaction_id,
      transactionAmount: data.amount,
      source: '2',
      date: data.deposit_date.$date,
    });
    await associatePayments.save();

    return res.status(200).send({ message: associatePaymentsConsts.success.historyPaymentAdded });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};
