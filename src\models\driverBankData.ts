import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const driverBankData = new Schema({
  email: {
    type: String,
    required: true,
  },
  region: {
    type: String,
    required: true,
  },
  monexAccount: {
    type: String,
    required: true,
  },
  gigUser: {
    type: String,
    required: true,
  },
  firstPayment: {
    type: String,
    required: true,
  },
  recurrent: {
    type: String,
    required: true,
  },
  date: {
    type: Date,
    default: getCurrentDateTime(),
  },
});

const DriverBankData = model('DriverBankData', driverBankData);

export default DriverBankData;
