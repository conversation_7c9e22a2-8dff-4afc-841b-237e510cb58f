export type OtpTemplateProps = {
  otp: string;
};

export default function otpTemplate({ otp }: OtpTemplateProps) {
  const year = new Date().getFullYear();
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verify your login</title>
  <!--[if mso]>
    <style type="text/css">
      body, div, p, a { font-family: Arial, Helvetica, sans-serif !important; }
    </style>
  <![endif]-->
  <style>
    body {
      font-family: Helvetica, Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #ffffff;
    }

    .email-container {
      max-width: 600px;
      margin: 0 auto;
      padding: 1rem 2rem;
      background-color: #ffffff;
    }

    .logo {
      text-align: left;
      margin-bottom: 24px;
    }

    .logo img {
      max-width: 150px;
      height: 32px;
    }

    .card {
      background-color: #ffffff;
    }

    .title {
      margin: 1rem 0;
      font-size: 36px;
      color: #344054;
    }

    .text {
      padding-bottom: 16px;
      color: #344054;
      font-size: 16px;
      line-height: 1.5;
    }

    .otp-code {
      font-size: 130%;
      font-weight: bold;
    }

    p {
      font-size: 16px;
      color: #344054;
    }

    .footer {
      background-color: #6600FA;
      color: #ffffff;
      text-align: center;
      height: 24px;
      line-height: 24px;
      font-size: 12px;
      margin-top: 20px;
    }
  </style>
</head>

<body>
  <div class="email-container">
    <div class="logo">
      <img src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp" alt="OneCarNow Logo">
    </div>
    <div class="card">
      <div style="color: #000000; text-align: left;">
        <h1 class="title"><span style='font-size:36px;'>&#128242;</span>Código de verificación</h1>
        <p class="text">Aquí tienes el código de verificación para acceder a tu cuenta OCN:</p>
        <p class="text"><strong class="otp-code">${otp}</strong></p>
        <p class="text">Si no lo solicitaste, simplemente ignora este mensaje.</p>
        <p>Gracias</p>
        <p>Team OCN</p>
      </div>
    </div>
    <!-- Footer Section -->
    <div class="footer">
      © Copyright ${year} OCN
    </div>
  </div>
</body>

</html>`;
}
