import { genericMessages, stockVehiclesText } from '../../constants';
import StockVehicle from '../../models/StockVehicleSchema';
// import ServiceStock from '../../models/serviceStock';
import { AsyncController } from '../../types&interfaces/types';

export const getStatusAndProcessByVehicleId: AsyncController = async (req, res) => {
  const { id } = req.params;

  try {
    const vehicle = await StockVehicle.findById(id);

    if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    // const { status } = vehicle;

    // const inServices = await ServiceStock.find({ vehicleId: id });

    return res.status(200).send({ message: 'getStatusAndProcessByVehicleId' });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};

export const canFinishProcessByVehicleId: AsyncController = async (req, res) => {
  const { id } = req.params;

  try {
    const vehicle = await StockVehicle.findById(id);

    if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    const { canFinishProcess } = vehicle;

    return res.status(200).send({ canFinishProcess });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};
