import { AsyncController } from '../../../types&interfaces/types';
// import { returnToDriverAssigned } from '../functions/returndriverAssigned';
import { stepRelationQuery } from '../functions/setpRelation';
import User from '../../../models/userSchema';
import { emailUsersAllowed, isProd } from '../../../constants';
import { returnToDriverAssigned, returnToVehicleReady } from '../functions';
// import { Request } from 'express';
import { Request } from 'express';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleSubCategory,
} from '../../../models/StockVehicleSchema';
import StartPayFlow from '../../../models/start-pay-flow';
import Associate from '../../../models/associateSchema';
import { getCurrentDateTime } from '../../../services/timestamps';

type StepsFunctions = {
  [key: string]: (stockId: string, req: Request) => Promise<any>;
};

const stepsFunctions: StepsFunctions = {
  vehicleReady: (stockId: string, req: Request) => returnToVehicleReady(stockId, req),
  driverAssigned: (stockId: string) => returnToDriverAssigned(stockId),
};

const stepsText: Record<string, string> = {
  vehicleReady: 'Vehiculo Regresado a listo',
  driverAssigned: 'Vehiculo Regresado a asignado',
};

export const returnFlowController: AsyncController = async (req, res) => {
  const { stepName } = req.body;
  const { stockId } = req.params;

  if (!stepName) return res.status(400).send({ message: 'Step name is required' });
  if (!stockId) return res.status(400).send({ message: 'Stock id is required' });

  const userIdRequest = req.userId.userId;

  const user = await User.findById(userIdRequest);
  if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });
  // console.log('is prod', isProd);
  if (isProd && emailUsersAllowed.includes(user.email))
    return res.status(401).send({ message: 'No tienes permisos para realizar esta accion' });

  const stepMatch = stepRelationQuery[stepName];

  const stockVehicle = await StockVehicle.findById(stockId).select({
    step: 1,
    drivers: 1,
    updateHistory: 1,
  });

  if (!stockVehicle) return res.status(404).send({ message: 'Stock no encontrado' });

  const associate = await Associate.findById(
    stockVehicle.drivers[stockVehicle?.drivers?.length - 1]?._id
  ).select({
    _id: 1,
    unSignedContractDoc: 1,
    clientId: 1,
  });

  if (!associate) return res.status(404).send({ message: 'Asociado no encontrado' });

  try {
    if (stepsFunctions[stepMatch]) {
      const response = await stepsFunctions[stepMatch](stockId, req);

      await StartPayFlow.findOneAndDelete({
        associateId: associate._id,
        stockId: stockVehicle._id,
      });

      stockVehicle.updateHistory.push({
        step: stepsText[stepMatch],
        time: getCurrentDateTime(),
        userId: userIdRequest,
      });

      stockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
      stockVehicle.category =
        stepName === 'Vehiculo listo' ? VehicleCategory.stock : VehicleCategory.assigned;
      stockVehicle.subCategory = VehicleSubCategory.default;
      await stockVehicle.save();

      //delete customer acknowledgement record
      // await CustomerAcknowledgement.findOneAndDelete({
      //   associateId: associate._id,
      //   stockVehicleId: stockVehicle._id,
      // });

      return res.status(200).send(response);
    } else {
      return res.status(404).send({ message: 'Step not found' });
    }
  } catch (error: any) {
    const message = error.response?.data?.message || error.message || 'Internal Server Error';
    console.log('client id', associate);
    return res.status(500).send({ message, clientId: associate.clientId });
  }
};
