import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  createFleetOrder,
  getFleetOrderById,
  getFleetOrderByNumber,
  listFleetOrders,
  updateFleetOrderStatus,
  updateDispersion,
  deleteFleetOrder
} from '../controllers/fleet-orders.controller';
import {
  getPendingSLAAlerts,
  getAlertsForOrder,
  resolveAlert,
  resolveAlertsForOrder,
  runSLACheck,
  getSLAStatistics,
  getDailySLASummary,
  sendDailySummaryToSlack,
  cleanupOldAlerts,
  sendPendingAlertsToSlack
} from '../controllers/fleet-orders-sla.controller';
import {
  checkPermission,
  checkOCNUser,
  checkMultiplePermissions
} from '../../user-roles-permissions/middlewares/check-permissions.middleware';
import {
  checkFleetOrderAccess,
  checkFleetOrderModifiable,
  checkFleetOrderDeletable,
  checkDispersionUpdateAllowed,
  checkOrderExistsForPeriod,
  validateOrderCreationTiming
} from '../middlewares/fleet-orders-permissions.middleware';

const fleetOrdersRouter = Router();

const baseUrl = '/fleet-orders';

// ==================== RUTAS PRINCIPALES DE FLEET ORDERS ====================

/**
 * Crear nueva orden de flota (solo usuarios OCN)
 * POST /vendor-platform/fleet-orders
 */
fleetOrdersRouter.post(
  baseUrl,
  verifyTokenVendorPlatform,
  checkOCNUser,
  checkPermission('fleetOrders', 'create'),
  validateOrderCreationTiming,
  checkOrderExistsForPeriod,
  errorHandlerV2(createFleetOrder)
);

/**
 * Listar órdenes de flota con filtros
 * GET /vendor-platform/fleet-orders
 */
fleetOrdersRouter.get(
  baseUrl,
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'read'),
  errorHandlerV2(listFleetOrders)
);

/**
 * Obtener orden por ID
 * GET /vendor-platform/fleet-orders/:id
 */
fleetOrdersRouter.get(
  baseUrl + '/:id',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'read'),
  checkFleetOrderAccess,
  errorHandlerV2(getFleetOrderById)
);

/**
 * Obtener orden por número de orden
 * GET /vendor-platform/fleet-orders/number/:orderNumber
 */
fleetOrdersRouter.get(
  baseUrl + '/number/:orderNumber',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'read'),
  errorHandlerV2(getFleetOrderByNumber)
);

/**
 * Actualizar estado de orden
 * PATCH /vendor-platform/fleet-orders/:id/status
 */
fleetOrdersRouter.patch(
  baseUrl + '/:id/status',
  verifyTokenVendorPlatform,
  checkMultiplePermissions([
    { module: 'fleetOrders', permission: 'update' },
    { module: 'fleetOrders', permission: 'uploadEvidence' }
  ]),
  checkFleetOrderAccess,
  checkFleetOrderModifiable,
  errorHandlerV2(updateFleetOrderStatus)
);

/**
 * Actualizar dispersión de orden
 * PATCH /vendor-platform/fleet-orders/:id/dispersion
 */
fleetOrdersRouter.patch(
  baseUrl + '/:id/dispersion',
  verifyTokenVendorPlatform,
  checkMultiplePermissions([
    { module: 'fleetOrders', permission: 'update' },
    { module: 'fleetOrders', permission: 'manageDispersion' }
  ]),
  checkFleetOrderAccess,
  checkDispersionUpdateAllowed,
  errorHandlerV2(updateDispersion)
);

/**
 * Eliminar orden (solo si está en estado CREATED y solo usuarios OCN)
 * DELETE /vendor-platform/fleet-orders/:id
 */
fleetOrdersRouter.delete(
  baseUrl + '/:id',
  verifyTokenVendorPlatform,
  checkOCNUser,
  checkPermission('fleetOrders', 'delete'),
  checkFleetOrderAccess,
  checkFleetOrderDeletable,
  errorHandlerV2(deleteFleetOrder)
);

// ==================== RUTAS DE SLA Y ALERTAS ====================

/**
 * Obtener alertas de SLA pendientes
 * GET /vendor-platform/fleet-orders/sla/alerts
 */
fleetOrdersRouter.get(
  baseUrl + '/sla/alerts',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(getPendingSLAAlerts)
);

/**
 * Obtener alertas para una orden específica
 * GET /vendor-platform/fleet-orders/:orderId/alerts
 */
fleetOrdersRouter.get(
  baseUrl + '/:orderId/alerts',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'read'),
  errorHandlerV2(getAlertsForOrder)
);

/**
 * Marcar alerta como resuelta
 * PATCH /vendor-platform/fleet-orders/sla/alerts/:alertId/resolve
 */
fleetOrdersRouter.patch(
  baseUrl + '/sla/alerts/:alertId/resolve',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(resolveAlert)
);

/**
 * Marcar todas las alertas de una orden como resueltas
 * PATCH /vendor-platform/fleet-orders/:orderId/alerts/resolve-all
 */
fleetOrdersRouter.patch(
  baseUrl + '/:orderId/alerts/resolve-all',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(resolveAlertsForOrder)
);

/**
 * Ejecutar verificación manual de SLA (solo usuarios OCN)
 * POST /vendor-platform/fleet-orders/sla/check
 */
fleetOrdersRouter.post(
  baseUrl + '/sla/check',
  verifyTokenVendorPlatform,
  checkOCNUser,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(runSLACheck)
);

/**
 * Obtener estadísticas de SLA
 * GET /vendor-platform/fleet-orders/sla/statistics
 */
fleetOrdersRouter.get(
  baseUrl + '/sla/statistics',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(getSLAStatistics)
);

/**
 * Obtener resumen diario de SLA
 * GET /vendor-platform/fleet-orders/sla/daily-summary
 */
fleetOrdersRouter.get(
  baseUrl + '/sla/daily-summary',
  verifyTokenVendorPlatform,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(getDailySLASummary)
);

// ==================== RUTAS DE INTEGRACIÓN CON SLACK ====================

/**
 * Enviar resumen diario a Slack (solo usuarios OCN)
 * POST /vendor-platform/fleet-orders/slack/daily-summary
 */
fleetOrdersRouter.post(
  baseUrl + '/slack/daily-summary',
  verifyTokenVendorPlatform,
  checkOCNUser,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(sendDailySummaryToSlack)
);

/**
 * Enviar alertas pendientes a Slack (solo usuarios OCN)
 * POST /vendor-platform/fleet-orders/slack/send-pending-alerts
 */
fleetOrdersRouter.post(
  baseUrl + '/slack/send-pending-alerts',
  verifyTokenVendorPlatform,
  checkOCNUser,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(sendPendingAlertsToSlack)
);

// ==================== RUTAS DE MANTENIMIENTO ====================

/**
 * Limpiar alertas resueltas antiguas (solo usuarios OCN)
 * DELETE /vendor-platform/fleet-orders/sla/cleanup
 */
fleetOrdersRouter.delete(
  baseUrl + '/sla/cleanup',
  verifyTokenVendorPlatform,
  checkOCNUser,
  checkPermission('fleetOrders', 'manageSLAs'),
  errorHandlerV2(cleanupOldAlerts)
);

export default fleetOrdersRouter;
