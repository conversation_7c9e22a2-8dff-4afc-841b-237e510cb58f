import { Router } from 'express';

import {
  getIneData,
  saveVideo,
  getEarningsAnalysis,
  saveDriverProfileData,
  // saveBankStatement,
  // ocrWebhook,
  getVideo,
  extractFinanceRecords,
  getProfileData,
  getDriverProfileData,
} from '../controllers/ocrData';

import { upload, uploadInMemory } from '../multer/multer';
import { ocrLimitTriesMiddleware } from '../middlewares/ocrLimitTries';

const router = Router();

// router.post('/webhook', ocrWebhook);
router.post('/ine-front', getIneData);
router.post('/save-video/:requestId', upload.single('video'), saveVideo);
router.get('/get-earnings-analysis/:requestId', getEarningsAnalysis);
router.post('/save-driver-profile-data/:requestId', saveDriverProfileData);
// router.post('/save-bank-statement:requestId', upload.single('document'), saveBankStatement);
router.get('/get-video/:requestId/:platform', getVideo);
router.post(
  '/upload-screenshots',
  uploadInMemory.single('image'),
  ocrLimitTriesMiddleware,
  extractFinanceRecords
);
router.get('/profile-data/:requestId/:platform/:locale', getProfileData);
router.get('/driver-profile-data/:requestId/:platform', getDriverProfileData);

export default router;
