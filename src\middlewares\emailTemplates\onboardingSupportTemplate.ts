export type OnboardingSupportTemplateProps = {
  url: string;
};

export default function onboardingSupportTemplate({ url }: OnboardingSupportTemplateProps) {
  const year = new Date().getFullYear();
  return `<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Actualización de tu Aplicación</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #344054;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #ffffff;
        }
        .logo {
            text-align: left;
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 150px;
            height: 32px;
        }
        h1 {
            color: #344054;
            font-size: 36px;
            margin-bottom: 20px;
        }
        p {
            font-size: 16px;
            color: #344054;
            margin-bottom: 15px;
        }
        ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }
        ul li {
            font-size: 16px;
            color: #344054;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 20px;
            font-size: 14px;
            color: #344054;
        }
        .email-footer {
            background-color: #6600FA;
            color: #ffffff;
            text-align: center;
            height: 24px;
            line-height: 24px;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Company Logo -->
        <div class="logo">
            <img src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp" alt="OneCarNow Logo">
        </div>
        <h1>¡Tu solicitud está casi lista! 📌</h1>
        <p>Hola, Para finalizar el proceso, sigue estos sencillos pasos:</p>
        <ul>
            <li>Accede a la siguiente URL: <a href="${url}" target="_blank">Onboarding Support</a></li>
            <li>Ingresa tu correo electrónico y número de teléfono. Enviaremos un código de verificación a ambos.</li>
            <li>Introduce el código que recibas.</li>
            <li>Lee atentamente la información y marca la casilla de confirmación que se encuentra en la última página.</li>
        </ul>
        <p>Si necesitas soporte, no dudes en contactarnos. ¡Estamos aquí para ayudarte! &#128522;</p>
        <div class="footer">
            <p>Saludos cordiales,<br>Equipo OCN</p>
        </div>
        <div class="email-footer">
         © Copyright ${year} OCN
        </div>
    </div>
</body>
</html>`;
}
