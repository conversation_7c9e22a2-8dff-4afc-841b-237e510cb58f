import axios from 'axios';
import {
  CDMX_GIGSTACK_TOKEN,
  GDL_GIGSTACK_TOKEN,
  GIGSTACK_TOKEN,
  MTY_GIGSTACK_TOKEN,
  QRO_GIGSTACK_TOKEN,
  TIJ_GIGSTACK_TOKEN,
  PBC_GIGSTACK_TOKEN,
  PUE_GIGSTACK_TOKEN,
  PBE_GIGSTACK_TOKEN,
  TOL_GIGSTACK_TOKEN,
  PTV_GIGSTACK_TOKEN,
  TEP_GIGSTACK_TOKEN,
  COL_GIGSTACK_TOKEN,
  SAL_GIGSTACK_TOKEN,
  TORR_GIGSTACK_TOKEN,
  DUR_GIGSTACK_TOKEN,
  MXLI_GIGSTACK_TOKEN,
  HER_GIGSTACK_TOKEN,
  CHI_GIGSTACK_TOKEN,
  LEO_GIGSTACK_TOKEN,
  AGS_GIGSTACK_TOKEN,
  SLP_GIGSTACK_TOKEN,
  MER_GIGSTACK_TOKEN,
  GIGST<PERSON>K_URL,
} from '../constants';

export type ValidRegion =
  | 'CDMX'
  | 'QRO'
  | 'GDL'
  | 'TIJ'
  | 'MTY'
  | 'MOKA'
  | 'TEST'
  | 'PBC'
  | 'PUE'
  | 'PBE'
  | 'TOL'
  | 'PTV'
  | 'TEP'
  | 'COL'
  | 'SAL'
  | 'TORR'
  | 'DUR'
  | 'MXLI'
  | 'HER'
  | 'CHI'
  | 'LEO'
  | 'AGS'
  | 'SLP'
  | 'MER';

export const tokenAssignGigstack = (region: ValidRegion) => {
  const REGION_TOKENS = {
    CDMX: CDMX_GIGSTACK_TOKEN,
    QRO: QRO_GIGSTACK_TOKEN,
    GDL: GDL_GIGSTACK_TOKEN,
    TIJ: TIJ_GIGSTACK_TOKEN,
    MTY: MTY_GIGSTACK_TOKEN,
    MOKA: GIGSTACK_TOKEN,
    TEST: GIGSTACK_TOKEN,
    PBC: PBC_GIGSTACK_TOKEN,
    PBE: PBE_GIGSTACK_TOKEN,
    TOL: TOL_GIGSTACK_TOKEN,
    PTV: PTV_GIGSTACK_TOKEN,
    TEP: TEP_GIGSTACK_TOKEN,
    COL: COL_GIGSTACK_TOKEN,
    SAL: SAL_GIGSTACK_TOKEN,
    TORR: TORR_GIGSTACK_TOKEN,
    DUR: DUR_GIGSTACK_TOKEN,
    MXLI: MXLI_GIGSTACK_TOKEN,
    HER: HER_GIGSTACK_TOKEN,
    CHI: CHI_GIGSTACK_TOKEN,
    LEO: LEO_GIGSTACK_TOKEN,
    AGS: AGS_GIGSTACK_TOKEN,
    SLP: SLP_GIGSTACK_TOKEN,
    MER: MER_GIGSTACK_TOKEN,
    PUE: PUE_GIGSTACK_TOKEN,
  };

  return REGION_TOKENS[region] || null;
};

export const getGigstackConfig = (region: ValidRegion) => {
  const token = tokenAssignGigstack(region);
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  };
};

type GetGistack = {
  method: 'get';
  values?: undefined;
};
type PostPutGigstack = {
  method: 'post' | 'put';
  values: any;
};
type GigstackAxios2 = {
  endpoint: string;
  region: ValidRegion;
} & (GetGistack | PostPutGigstack);

/**
 * Performs requests to the Gigstack API.
 * @param {Object} reqParams - An object containing the parameters for the Gigstack API request.
 * @param {string} reqParams.endpoint - string | The endpoint of the Gigstack API.
 * @param {ValidRegion} reqParams.region - ValidRegion | The region for the Gigstack API request.
 * @param {'get' | 'post' | 'put'} reqParams.method - 'get' | 'post' | 'put' | The HTTP method for the Gigstack API request.
 * @param {any} [reqParams.values] - The data payload for 'post' and 'put' requests.
 * @returns {Promise<any>} - A promise that resolves with the response data from the Gigstack API.
 * @remarks
 * The Gigstack API requests can be of type 'get', 'post', or 'put'. For 'get' requests, the 'values' property is optional and can be undefined.
 * For 'post' and 'put' requests, the 'values' property must be provided.
 */

export const gigstackRequests = async ({ endpoint, region, method, values }: GigstackAxios2) => {
  const gigstackConfig = getGigstackConfig(region);
  if (method === 'get') {
    try {
      const { data } = await axios[method](`${GIGSTACK_URL}${endpoint}`, gigstackConfig);
      return data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  if (method === 'post' || method === 'put') {
    try {
      const { data } = await axios[method](`${GIGSTACK_URL}${endpoint}`, values, gigstackConfig);
      return data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
  return null;
};
