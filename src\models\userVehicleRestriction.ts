import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const userVehicleRestrictionsSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: 'User',
  },

  stockRestrictions: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'StockVehicle',
        },
        carNumber: {
          type: String,
          required: true,
        },
      },
    ],
    default: [],
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

const UserVehicleRestrictions = model('UserVehicleRestrictions', userVehicleRestrictionsSchema);

export default UserVehicleRestrictions;
