import request from 'supertest';
import jwt from 'jsonwebtoken';
import { accessTokenSecret } from '../constants';
import app from '../app';
import User from '../models/userSchema';
import bcrypt from 'bcrypt';
import mongoose, { Types } from 'mongoose';

let userId: Types.ObjectId;
let userAdminId: Types.ObjectId;

beforeAll(async () => {
  const password = 'mypassword';
  const hashedPassword = await bcrypt.hash(password, 12);
  const user = await User.create({
    email: '<EMAIL>',
    name: '<PERSON><PERSON><PERSON>',
    password: hashedPassword,
    role: 'agent',
    city: 'cdmx',
    settings: {
      allowedRegions: ['cdmx', 'gdl', 'mty', 'tij', 'pbc', 'pbe', 'qro'],
    },
  });

  const adminUser = await User.create({
    email: '<EMAIL>',
    name: '<PERSON><PERSON>',
    password: hashedPassword,
    role: 'superadmin',
    city: 'cdmx',
    settings: {
      allowedRegions: ['cdmx', 'gdl', 'mty', 'tij', 'pbc', 'pbe', 'qro'],
    },
  });
  userAdminId = adminUser._id;
  userId = user._id;
});

describe('Getting user data GET /user/{userId}', () => {
  let response: request.Response;
  const token = jwt.sign({ userId }, accessTokenSecret, {
    expiresIn: '2m',
  });
  it('should return the data if token is provided', async () => {
    response = await request(app).get(`/user/${userId}`).set('Authorization', `Bearer ${token}`);
    expect(response.body).toEqual({
      message: 'Datos del usuario',
      user: response.body.user,
    });
  });

  it('should return 404 status code and user not found message if user does not exist', async () => {
    const objectId = new mongoose.Types.ObjectId();
    response = await request(app).get(`/user/${objectId}`).set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(404);
    expect(response.body).toEqual({
      message: 'Usuario no encontrado',
    });
  });

  it('should return 200 status code token is provided', async () => {
    response = await request(app).get(`/user/${userId}`).set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
  });

  it('should return 401 status code if token is not provided', async () => {
    response = await request(app).get(`/user/${userId}`);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      success: false,
      data: null,
      error: { code: 'unauthorized', errors: {} },
      pagination: null,
    });

    response = await request(app).get(`/user/${userId}`).set('Authorization', `Bearer `);
    expect(response.status).toBe(401);
    expect(response.body).toEqual({
      success: false,
      data: null,
      error: { code: 'unauthorized', errors: {} },
      pagination: null,
    });
  });
});

describe('Getting All user, GET /user/getAllUsers', () => {
  let response: request.Response;
  const token = jwt.sign({ userId }, accessTokenSecret);

  it('should return the data if is admin user and token is provided', async () => {
    const url = `/user/getAllUsers?adminId=${userAdminId}`;
    response = await request(app).get(url).set('Authorization', `Bearer ${token}`);
    // console.log('SOY LA RESPONSE', response);
    expect(response.body).toEqual({
      message: `Total de ${response.body.users.length} Usuarios`,
      users: response.body.users,
    });
  });

  it('should return 200 status code if is admin user and token is provided', async () => {
    const url = `/user/getAllUsers?adminId=${userAdminId}`;
    response = await request(app).get(url).set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(200);
  });
});
