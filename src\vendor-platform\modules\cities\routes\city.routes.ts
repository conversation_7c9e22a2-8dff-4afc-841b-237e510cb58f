import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import { createCity, getCities, getCityById, updateCity, deleteCity } from '../controllers/city.controller';
import { getCrewsByCity } from '../../crews/controllers/crew.controller';
import { getNeighborhoodsByCityState } from '../../neighborhoods/controllers/neighborhood.controller';

const cityRouter = Router();

// get neighborhoods of all cities depending on the state of the city
cityRouter.get(
  '/public/cities/state/:state/neighborhoods',
  // verifyTokenVendorPlatform
  errorHandlerV2(getNeighborhoodsByCityState)
);
cityRouter.get('/cities', verifyTokenVendorPlatform, errorHandlerV2(getCities));
cityRouter.post('/cities', verifyTokenVendorPlatform, errorHandlerV2(createCity));
cityRouter.get('/cities/:cityId', verifyTokenVendorPlatform, errorHandlerV2(getCityById));
cityRouter.get('/cities/:cityId/crews', verifyTokenVendorPlatform, errorHandlerV2(getCrewsByCity));
cityRouter.put('/cities/:cityId', verifyTokenVendorPlatform, errorHandlerV2(updateCity));
cityRouter.delete('/cities/:cityId', verifyTokenVendorPlatform, errorHandlerV2(deleteCity));
// get neighborhoods by state of the city

export default cityRouter;
