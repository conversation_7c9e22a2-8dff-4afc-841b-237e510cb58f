import Contract from '../models/contractSchema';

export const getLastContractNumber = async (region: number) => {
  try {
    const lastContract = await Contract.findOne({ region }).sort({ contractNumber: -1 }).limit(1);
    const nextNumber = lastContract?.contractNumber ? lastContract.contractNumber + 1 : 1;
    return nextNumber;
  } catch (error) {
    console.error(error);
    return error;
  }
};
