import { CreateNeighborhoodDto } from '../dtos/create-neighborhood.dto';
import { UpdateNeighborhoodDto } from '../dtos/update-neighborhood.dto';
import { CrewVendorModel } from '../../crews/models/crew.model';
import { NeighborhoodVendorModel } from '../models/neighborhood.model';
import { City } from '../../cities/models/city.model';

export class NeighborhoodService {
  static async createNeighborhood(data: CreateNeighborhoodDto) {
    const neighborhood = new NeighborhoodVendorModel(data);
    await neighborhood.save();

    // Add neighborhood to crew's neighborhoods array
    await CrewVendorModel.findByIdAndUpdate(data.crewId, {
      $push: { neighborhoods: neighborhood._id },
    });

    return neighborhood;
  }

  static async getNeighborhoods({ crewId, companyId }: { crewId?: string; companyId?: string }) {
    const neighborhoods = await NeighborhoodVendorModel.find({
      ...(companyId ? { companyId } : {}),
      ...(crewId ? { crewId } : {}), // If crewId is provided, filter by crewId
    });
    return neighborhoods;
  }

  static async getNeighborhoodById(id: string) {
    return NeighborhoodVendorModel.findById(id);
  }

  static async getNeighborhoodsByCrew(crewId: string) {
    return NeighborhoodVendorModel.find({ crewId });
  }

  static async updateNeighborhood(id: string, data: UpdateNeighborhoodDto) {
    return NeighborhoodVendorModel.findByIdAndUpdate(id, data, { new: true });
  }

  static async deleteNeighborhood(id: string) {
    const neighborhood = await NeighborhoodVendorModel.findById(id);
    if (!neighborhood) return null;

    // Remove neighborhood from crew's neighborhoods array
    await CrewVendorModel.findByIdAndUpdate(neighborhood.crewId, {
      $pull: { neighborhoods: neighborhood._id },
    });

    return NeighborhoodVendorModel.findByIdAndDelete(id);
  }

  static async getNeighborhoodsByCompany(companyId: string) {
    return NeighborhoodVendorModel.find({ companyId });
  }

  static async getNeighborhoodsByCity(cityId: string) {
    return NeighborhoodVendorModel.find({ cityId });
  }

  static async getNeighborhoodsByCityState(state: string) {
    const cities = await City.find({ state });
    const cityIds = cities.map((city) => city._id.toString());
    return NeighborhoodVendorModel.find({ cityId: { $in: cityIds } });
  }
}
