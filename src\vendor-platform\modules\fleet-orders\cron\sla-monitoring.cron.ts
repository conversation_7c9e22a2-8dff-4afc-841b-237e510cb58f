import cron from 'node-cron';
import { SLAMonitoringService } from '../services/sla-monitoring.service';
import { SlackNotificationsService } from '../services/slack-notifications.service';

const slaMonitoringService = new SLAMonitoringService();
const slackNotificationsService = new SlackNotificationsService();

/**
 * Cron job para verificar SLAs cada hora durante horario laboral
 * Se ejecuta de lunes a viernes de 8:00 AM a 6:00 PM cada hora
 */
export const startSLAMonitoringCron = () => {
  // Verificación cada hora durante horario laboral (8 AM - 6 PM, Lun-Vie)
  cron.schedule(
    '0 8-18 * * 1-5',
    async () => {
      console.log('🔍 Ejecutando verificación de SLA automática...');

      try {
        // Verificar y crear nuevas alertas
        const newAlerts = await slaMonitoringService.checkAndCreateSLAAlerts();

        if (newAlerts.length > 0) {
          console.log(`📢 Se crearon ${newAlerts.length} nuevas alertas de SLA`);

          // Enviar alertas a Slack
          for (const alert of newAlerts) {
            try {
              // Obtener la alerta con la orden poblada
              const alertsForOrder = await slaMonitoringService.getAlertsForOrder(alert.orderId);
              const alertWithOrder = alertsForOrder.find((a) => a._id.toString() === alert._id.toString());

              if (alertWithOrder && slackNotificationsService.isConfigured()) {
                const slackMessageId = await slackNotificationsService.sendSLAAlert(alertWithOrder as any);
                if (slackMessageId) {
                  await slaMonitoringService.markAlertAsSentToSlack(alert._id, slackMessageId);
                  console.log(`✅ Alerta ${alert._id} enviada a Slack`);
                }
              }
            } catch (slackError) {
              console.error(`❌ Error enviando alerta ${alert._id} a Slack:`, slackError);
            }
          }
        } else {
          console.log('✅ No se encontraron nuevas alertas de SLA');
        }
      } catch (error) {
        console.error('❌ Error en verificación automática de SLA:', error);
      }
    },
    {
      timezone: 'America/Mexico_City',
    }
  );

  console.log('🚀 Cron job de monitoreo de SLA iniciado (cada hora, 8 AM - 6 PM, Lun-Vie)');
};

/**
 * Cron job para enviar resumen diario a Slack
 * Se ejecuta todos los días a las 9:00 AM
 */
export const startDailySummaryCron = () => {
  cron.schedule(
    '0 9 * * *',
    async () => {
      console.log('📊 Enviando resumen diario de SLA...');

      try {
        if (!slackNotificationsService.isConfigured()) {
          console.log('⚠️ Slack no configurado, saltando resumen diario');
          return;
        }

        const summary = await slaMonitoringService.getDailySLASummary();
        const slackMessageId = await slackNotificationsService.sendDailySummary(summary);

        if (slackMessageId) {
          console.log('✅ Resumen diario enviado a Slack exitosamente');
        }
      } catch (error) {
        console.error('❌ Error enviando resumen diario:', error);
      }
    },
    {
      timezone: 'America/Mexico_City',
    }
  );

  console.log('🚀 Cron job de resumen diario iniciado (9:00 AM todos los días)');
};

/**
 * Cron job para limpiar alertas resueltas antiguas
 * Se ejecuta todos los domingos a las 2:00 AM
 */
export const startCleanupCron = () => {
  cron.schedule(
    '0 2 * * 0',
    async () => {
      console.log('🧹 Ejecutando limpieza de alertas antiguas...');

      try {
        const deletedCount = await slaMonitoringService.cleanupOldResolvedAlerts();
        console.log(`✅ Se eliminaron ${deletedCount} alertas resueltas antiguas`);
      } catch (error) {
        console.error('❌ Error en limpieza de alertas:', error);
      }
    },
    {
      timezone: 'America/Mexico_City',
    }
  );

  console.log('🚀 Cron job de limpieza iniciado (2:00 AM todos los domingos)');
};

/**
 * Cron job para verificar alertas críticas cada 30 minutos
 * Se ejecuta 24/7 para alertas críticas
 */
export const startCriticalAlertsCron = () => {
  cron.schedule('*/30 * * * *', async () => {
    try {
      // Obtener alertas no enviadas a Slack
      const pendingAlerts = await slaMonitoringService.getAlertsNotSentToSlack();

      // Filtrar solo alertas críticas (SLA_EXCEEDED)
      const criticalAlerts = pendingAlerts.filter((alert) => alert.alertType === 'sla_exceeded');

      if (criticalAlerts.length > 0) {
        console.log(`🚨 Enviando ${criticalAlerts.length} alertas críticas pendientes...`);

        for (const alert of criticalAlerts) {
          try {
            if (slackNotificationsService.isConfigured()) {
              const slackMessageId = await slackNotificationsService.sendSLAAlert(alert as any);
              if (slackMessageId) {
                await slaMonitoringService.markAlertAsSentToSlack(alert._id, slackMessageId);
                console.log(`✅ Alerta crítica ${alert._id} enviada a Slack`);
              }
            }
          } catch (slackError) {
            console.error(`❌ Error enviando alerta crítica ${alert._id}:`, slackError);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error verificando alertas críticas:', error);
    }
  });

  console.log('🚀 Cron job de alertas críticas iniciado (cada 30 minutos)');
};

/**
 * Inicializar todos los cron jobs de Fleet Orders
 */
export const initializeFleetOrdersCronJobs = () => {
  console.log('🚀 Inicializando cron jobs de Fleet Orders...');

  startSLAMonitoringCron();
  startDailySummaryCron();
  startCleanupCron();
  startCriticalAlertsCron();

  console.log('✅ Todos los cron jobs de Fleet Orders iniciados');
};

/**
 * Función para ejecutar verificación manual (útil para testing)
 */
export const runManualSLACheck = async () => {
  console.log('🔍 Ejecutando verificación manual de SLA...');

  try {
    const newAlerts = await slaMonitoringService.checkAndCreateSLAAlerts();
    console.log(`📢 Verificación manual completada: ${newAlerts.length} nuevas alertas`);
    return newAlerts;
  } catch (error) {
    console.error('❌ Error en verificación manual:', error);
    throw error;
  }
};
