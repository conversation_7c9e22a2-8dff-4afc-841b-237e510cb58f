import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { SLAMonitoringService } from '../services/sla-monitoring.service';
import { SlackNotificationsService } from '../services/slack-notifications.service';
import { SLAAlertType } from '../models/fleet-order-sla-alerts.model';

const slaMonitoringService = new SLAMonitoringService();
const slackNotificationsService = new SlackNotificationsService();

/**
 * Obtener alertas de SLA pendientes
 */
export const getPendingSLAAlerts = async (req: Request, res: Response) => {
  try {
    const { type, page = 1, limit = 10 } = req.query;
    
    let alerts;
    
    if (type && Object.values(SLAAlertType).includes(type as SLAAlertType)) {
      alerts = await slaMonitoringService.getAlertsByType(type as SLAAlertType);
    } else {
      alerts = await slaMonitoringService.getPendingAlerts();
    }

    // Paginación simple
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedAlerts = alerts.slice(startIndex, endIndex);

    res.status(200).json({
      message: 'Alertas de SLA obtenidas exitosamente',
      data: paginatedAlerts,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: alerts.length,
        totalPages: Math.ceil(alerts.length / Number(limit))
      }
    });
  } catch (error) {
    console.error('Error getting SLA alerts:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Obtener alertas para una orden específica
 */
export const getAlertsForOrder = async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    
    if (!Types.ObjectId.isValid(orderId)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    const alerts = await slaMonitoringService.getAlertsForOrder(new Types.ObjectId(orderId));

    res.status(200).json({
      message: 'Alertas de la orden obtenidas exitosamente',
      data: alerts
    });
  } catch (error) {
    console.error('Error getting alerts for order:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Marcar alerta como resuelta
 */
export const resolveAlert = async (req: Request, res: Response) => {
  try {
    const { alertId } = req.params;
    
    if (!Types.ObjectId.isValid(alertId)) {
      return res.status(400).json({
        message: 'ID de alerta inválido',
        code: 'INVALID_ALERT_ID'
      });
    }

    const resolvedAlert = await slaMonitoringService.resolveAlert(new Types.ObjectId(alertId));
    
    if (!resolvedAlert) {
      return res.status(404).json({
        message: 'Alerta no encontrada',
        code: 'ALERT_NOT_FOUND'
      });
    }

    res.status(200).json({
      message: 'Alerta marcada como resuelta',
      data: resolvedAlert
    });
  } catch (error) {
    console.error('Error resolving alert:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Marcar todas las alertas de una orden como resueltas
 */
export const resolveAlertsForOrder = async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    
    if (!Types.ObjectId.isValid(orderId)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    const resolvedCount = await slaMonitoringService.resolveAlertsForOrder(new Types.ObjectId(orderId));

    res.status(200).json({
      message: `${resolvedCount} alertas marcadas como resueltas`,
      data: { resolvedCount }
    });
  } catch (error) {
    console.error('Error resolving alerts for order:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Ejecutar verificación manual de SLA
 */
export const runSLACheck = async (req: Request, res: Response) => {
  try {
    const newAlerts = await slaMonitoringService.checkAndCreateSLAAlerts();

    // Enviar alertas nuevas a Slack
    for (const alert of newAlerts) {
      try {
        const alertWithOrder = await slaMonitoringService.getAlertsForOrder(alert.orderId);
        if (alertWithOrder.length > 0) {
          const alertData = alertWithOrder.find(a => a._id.toString() === alert._id.toString());
          if (alertData) {
            const slackMessageId = await slackNotificationsService.sendSLAAlert(alertData as any);
            if (slackMessageId) {
              await slaMonitoringService.markAlertAsSentToSlack(alert._id, slackMessageId);
            }
          }
        }
      } catch (slackError) {
        console.error('Error sending Slack notification for alert:', slackError);
        // Continuar con las demás alertas
      }
    }

    res.status(200).json({
      message: 'Verificación de SLA ejecutada exitosamente',
      data: {
        newAlertsCount: newAlerts.length,
        newAlerts: newAlerts
      }
    });
  } catch (error) {
    console.error('Error running SLA check:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Obtener estadísticas de SLA
 */
export const getSLAStatistics = async (req: Request, res: Response) => {
  try {
    const statistics = await slaMonitoringService.getSLAStatistics();

    res.status(200).json({
      message: 'Estadísticas de SLA obtenidas exitosamente',
      data: statistics
    });
  } catch (error) {
    console.error('Error getting SLA statistics:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Obtener resumen diario de SLA
 */
export const getDailySLASummary = async (req: Request, res: Response) => {
  try {
    const summary = await slaMonitoringService.getDailySLASummary();

    res.status(200).json({
      message: 'Resumen diario de SLA obtenido exitosamente',
      data: summary
    });
  } catch (error) {
    console.error('Error getting daily SLA summary:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Enviar resumen diario a Slack
 */
export const sendDailySummaryToSlack = async (req: Request, res: Response) => {
  try {
    const summary = await slaMonitoringService.getDailySLASummary();
    
    if (!slackNotificationsService.isConfigured()) {
      return res.status(400).json({
        message: 'Slack no está configurado',
        code: 'SLACK_NOT_CONFIGURED'
      });
    }

    const slackMessageId = await slackNotificationsService.sendDailySummary(summary);

    res.status(200).json({
      message: 'Resumen diario enviado a Slack exitosamente',
      data: {
        summary,
        slackMessageId
      }
    });
  } catch (error) {
    console.error('Error sending daily summary to Slack:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Limpiar alertas resueltas antiguas
 */
export const cleanupOldAlerts = async (req: Request, res: Response) => {
  try {
    const deletedCount = await slaMonitoringService.cleanupOldResolvedAlerts();

    res.status(200).json({
      message: 'Limpieza de alertas antiguas completada',
      data: { deletedCount }
    });
  } catch (error) {
    console.error('Error cleaning up old alerts:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Enviar alertas pendientes a Slack
 */
export const sendPendingAlertsToSlack = async (req: Request, res: Response) => {
  try {
    if (!slackNotificationsService.isConfigured()) {
      return res.status(400).json({
        message: 'Slack no está configurado',
        code: 'SLACK_NOT_CONFIGURED'
      });
    }

    const pendingAlerts = await slaMonitoringService.getAlertsNotSentToSlack();
    let sentCount = 0;

    for (const alert of pendingAlerts) {
      try {
        const slackMessageId = await slackNotificationsService.sendSLAAlert(alert as any);
        if (slackMessageId) {
          await slaMonitoringService.markAlertAsSentToSlack(alert._id, slackMessageId);
          sentCount++;
        }
      } catch (slackError) {
        console.error(`Error sending alert ${alert._id} to Slack:`, slackError);
        // Continuar con las demás alertas
      }
    }

    res.status(200).json({
      message: 'Alertas pendientes enviadas a Slack',
      data: {
        totalPending: pendingAlerts.length,
        sentCount,
        failedCount: pendingAlerts.length - sentCount
      }
    });
  } catch (error) {
    console.error('Error sending pending alerts to Slack:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};
