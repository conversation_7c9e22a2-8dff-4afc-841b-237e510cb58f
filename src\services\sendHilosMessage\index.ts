import { HILOS_API_KEY } from '../../constants';
import { HILOS_URL } from '../../constants/onboarding';
import axios from 'axios';
import { logger } from '../../clean/lib/logger';

export const sendMessage = async ({ props, template, phone }: any) => {
  try {
    const headers = {
      Authorization: `Token ${HILOS_API_KEY}`,
      'Content-Type': 'application/json',
    };

    const requestBody = {
      variables: [props],
      phone,
    };

    logger.info(
      `[sendMessage] Sending message to ${phone} with template ${template} and variables: ${JSON.stringify(requestBody)}`
    );

    const response = await axios.post(`${HILOS_URL}/${template}/send`, requestBody, { headers });

    return response.data;
  } catch (error: any) {
    logger.error(
      `[sendMessage] Error sending message to phone ${phone}, template ${template}, error: ${error}`
    );
    if (error.response) {
      logger.error(
        `[sendMessage] Error response status: ${error.response.status}, data: ${JSON.stringify(error.response.data)}`
      );
    }
    return error;
  }
};

export const updateHilosContact = async ({ hilosId, requestId }: any) => {
  try {
    await fetch(`https://api.hilos.io/api/contact/${hilosId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Token ${HILOS_API_KEY}`,
      },
      body: JSON.stringify({
        meta: {
          request_id: requestId,
        },
      }),
    });
  } catch (error) {
    logger.error(`[updateHilosContact] Error updating contact ${hilosId}: ${error})`);
    console.error(`Error actualizando contacto ${hilosId}:`, error);
    return error;
  }
  return true;
};
