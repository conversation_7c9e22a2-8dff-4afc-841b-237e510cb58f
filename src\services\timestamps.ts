import { DateTime } from 'luxon';
import mt from 'moment-timezone';

const timeZone = 'America/Mexico_City';

export function getCurrentDateTime() {
  return mt().tz(timeZone).format('YYYY-MM-DDTHH:mm:ss.SSS');
}

/**
 * @param {boolean} deleteHours - Si se quiere eliminar las horas, minutos y segundos de la fecha
 * @param {string} useIsoString - Si se quiere usar una fecha en formato ISO, requiere el tiempo en hora UTC para funcionar correctamente
 */

export function getCurrentDateObject({
  deleteHours = false,
  useIsoString,
}: { deleteHours?: boolean; useIsoString?: string } = {}) {
  const today = useIsoString ? new Date(useIsoString) : new Date();
  if (deleteHours) {
    today.setHours(0, 0, 0, 0);
  }

  const year = today.getFullYear();
  const month = today.getMonth();
  const day = today.getDate();
  const hour = today.getHours();
  const minute = today.getMinutes();
  const second = today.getSeconds();

  const fechaDate = new Date(Date.UTC(year, month, day, hour, minute, second));

  return fechaDate;
}

export function removeTimeFromDate(date: Date) {
  const isoString = date.toISOString();
  const onlyDate = isoString.split('T')[0];

  const newDate = new Date(`${onlyDate}T00:00:00.000Z`);
  return newDate;
}

/*
  Esto es para ejecutar el archivo directamente cuando se requiera
  ya que con import no funciona el comando: "node timestamps.ts"
 */
/* const mt = require('moment-timezone');
function getCurrentDateTime() {
  const timeZone = 'America/Mexico_City';
  return mt().tz(timeZone).format('YYYY-MM-DDTHH:mm:ss.SSS');
}
console.log(getCurrentDateTime()); */

export function getCurrentDateFromTimezone(timezone: string = 'America/Mexico_City') {
  const now = `${DateTime.now().setZone(timezone).toISO()!.split('-').slice(0, 3).join('-').toString()}Z`;

  return new Date(now);
}

export function getCurrentDateFromTimezoneSetTime(
  { hour, minute }: { hour?: number; minute?: number },
  timezone: string = 'America/Mexico_City'
) {
  if (!hour && !minute) {
    return getCurrentDateFromTimezone(timezone);
  }

  let objSet = {};

  if (hour) objSet = { ...objSet, hour };

  if (minute) objSet = { ...objSet, minute };

  const now =
    DateTime.now().setZone(timezone).set(objSet).toISO()!.split('-').slice(0, 3).join('-').toString() + 'Z';

  return new Date(now);
}
// type for only accept something like this format: YYYY-MM-DD

export function getCurrentMockDate(customDate: string, timezone = 'America/Mexico_City') {
  // return new Date(customDate);

  const date =
    DateTime.fromISO(customDate).setZone(timezone).toISO()!.split('-').slice(0, 3).join('-').toString() + 'Z';

  return new Date(date);
}
