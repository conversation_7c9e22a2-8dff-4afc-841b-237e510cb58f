import { NextFunction, Request, Response } from 'express';
import { logger } from '../lib/logger';

// Express 4 won't call next(error) automatically,
// until Express 5 is released we need to handle that manually
export const errorHandler = (handler: (req: Request, res: Response, next: NextFunction) => Promise<void>) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await handler(req, res, next);
    } catch (error) {
      logger.error(`[errorHandler] Error: ${error}`);
      next(error);
    }
  };
};
