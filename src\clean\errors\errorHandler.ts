import { NextFunction, Request, Response } from 'express';
import { MyRequest } from '@/types&interfaces/interfaces';
import { logger } from '../lib/logger';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';

// Express 4 won't call next(error) automatically,
// until Express 5 is released we need to handle that manually
export const errorHandlerV2 = (
  handler: (req: MyRequest, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>>>
) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      return await handler(req, res, next);
    } catch (error: any) {
      if (error instanceof HttpException) {
        logger.error('[error handler v2]: ' + JSON.stringify(error));
        return res.status(error.statusCode).json({ message: error.message });
      }

      logger.error('[error handler v2]: ' + JSON.stringify(error));
      return next(error);
    }
  };
};
