import { Schema, model } from 'mongoose';

const stpStatesChangeSchema = new Schema({
  id: {
    type: Number,
    required: [true, 'Id is required'],
  },
  empresa: {
    type: String,
    required: [true, 'Empresa is required'],
  },
  folioOrigen: {
    type: String,
  },
  estado: {
    type: String,
    required: [true, 'Estado is required'],
  },
  causaDevolucion: {
    type: String,
  },
  tsLiquidacion: {
    type: Date,
    required: [true, 'Ts liquidacion is required'],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updateAt: {
    type: Date,
  },
});
stpStatesChangeSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const StpStatesChangeSchema = model('StpStatesChangeSchema', stpStatesChangeSchema);

export default StpStatesChangeSchema;
