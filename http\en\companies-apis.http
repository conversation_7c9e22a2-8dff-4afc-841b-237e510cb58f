# @baseUrl = https://dev-api.onecarnow.com
@baseUrl = http://localhost:3000

@authToken = 'admin auth token here'
@vendorAuthToken = 'vendor auth token here'

@organizationId = 507f1f77bcf86cd799439011
@userId = 67e702b79d547db84ea03a09
@companyId = 67e6e91b5fc0062804cc33d0
@cityId = 67e6eb5a3d4b1f68feb580d6
@crewId = 67e6f1a6e03dc33e296536ec
@neighborhoodId = 67f41be5c4703b9aa9e03a76

### ==================== COMPANIES ====================

### Basic company operations
### Create new company
POST {{baseUrl}}/vendor-platform/companies
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Example Company",
    "contactInfo": {
        "email": "<EMAIL>",
        "phone": "+525512345678"
    }
}

### Get all companies
GET {{baseUrl}}/vendor-platform/companies
Authorization: Bearer {{vendorAuthToken}}

### Get company by ID
GET {{baseUrl}}/vendor-platform/companies/{{companyId}}
Authorization: Bearer {{vendorAuthToken}}

### Update company
PUT {{baseUrl}}/vendor-platform/companies/{{companyId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Updated Example Company"
}

### Delete company
DELETE {{baseUrl}}/vendor-platform/companies/{{companyId}}
Authorization: Bearer {{vendorAuthToken}}

### Company users and permissions
### Get company users
GET {{baseUrl}}/vendor-platform/companies/{{companyId}}/users
Authorization: Bearer {{vendorAuthToken}}

### Invite user to company
POST {{baseUrl}}/vendor-platform/companies/{{companyId}}/users/invite
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json
origin: http://localhost:5000

{
    "email": "<EMAIL>",
    "name": "Pedro Guest",
    "role": "operator"
}

### Remove user from company
DELETE {{baseUrl}}/vendor-platform/companies/{{companyId}}/users/{{userId}}
Authorization: Bearer {{vendorAuthToken}}

### Update user role in company
PATCH {{baseUrl}}/vendor-platform/companies/{{companyId}}/users/{{userId}}/role
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "role": "admin"
}

### ==================== CITIES ====================

### Get all cities
GET {{baseUrl}}/vendor-platform/cities
Authorization: Bearer {{vendorAuthToken}}

### Get city by ID
GET {{baseUrl}}/vendor-platform/cities/{{cityId}}
Authorization: Bearer {{vendorAuthToken}}

### Create city
POST {{baseUrl}}/vendor-platform/cities
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Mexico City",
    "state": "CDMX",
    "country": "Mexico"
}

### Update city
PUT {{baseUrl}}/vendor-platform/cities/{{cityId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Updated Mexico City"
}

### Delete city
DELETE {{baseUrl}}/vendor-platform/cities/{{cityId}}
Authorization: Bearer {{vendorAuthToken}}

### ==================== CREWS ====================

### Get all crews
GET {{baseUrl}}/vendor-platform/crews
Authorization: Bearer {{vendorAuthToken}}

### Get crew by ID
GET {{baseUrl}}/vendor-platform/crews/{{crewId}}
Authorization: Bearer {{vendorAuthToken}}

### Create crew
POST {{baseUrl}}/vendor-platform/crews
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "North Zone Crew",
    "cityId": "{{cityId}}",
    "members": ["{{userId}}"]
}

### Update crew
PUT {{baseUrl}}/vendor-platform/crews/{{crewId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Updated North Zone Crew"
}

### Delete crew
DELETE {{baseUrl}}/vendor-platform/crews/{{crewId}}
Authorization: Bearer {{vendorAuthToken}}

### ==================== NEIGHBORHOODS ====================

### Get all neighborhoods
GET {{baseUrl}}/vendor-platform/neighborhoods
Authorization: Bearer {{vendorAuthToken}}

### Get neighborhood by ID
GET {{baseUrl}}/vendor-platform/neighborhoods/{{neighborhoodId}}
Authorization: Bearer {{vendorAuthToken}}

### Create neighborhood
POST {{baseUrl}}/vendor-platform/neighborhoods
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Downtown",
    "cityId": "{{cityId}}",
    "zipCode": "12345"
}

### Update neighborhood
PUT {{baseUrl}}/vendor-platform/neighborhoods/{{neighborhoodId}}
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "name": "Updated Downtown"
}

### Delete neighborhood
DELETE {{baseUrl}}/vendor-platform/neighborhoods/{{neighborhoodId}}
Authorization: Bearer {{vendorAuthToken}}


### ==================== INSTALLATION APPOINTMENTS ====================


### Get installation appointments
GET {{baseUrl}}/vendor-platform/installation-appointments?startDate=2025-03-01&endDate=2025-05-31
Authorization: Bearer {{vendorAuthToken}}

### Filter installation appointments
GET {{baseUrl}}/vendor-platform/installation-appointments/filter?startDate=2025-04-01&endDate=2025-04-30&status=scheduled
Authorization: Bearer {{vendorAuthToken}}

### Create installation appointment
POST {{baseUrl}}/vendor-platform/installation-appointments
Authorization: Bearer {{vendorAuthToken}}
Content-Type: application/json

{
    "neighborhoodId": "{{neighborhoodId}}",
    "startTime": "2025-03-20T10:00:00.000-06:00",
    "associateId": "67ab59dea4a734d375f0da9a",
    "stockId": "6513183dfbeb9ab9e3a62f48",
}

### Search installation appointments with pagination and filters ----- Query: ?page=1&limit=10&query=ABC123
GET {{baseUrl}}/vendor-platform/installation-appointments/search?page=1&limit=10
Authorization: Bearer {{vendorAuthToken}}

### Search installation appointments with additional filters
GET {{baseUrl}}/vendor-platform/installation-appointments/search?page=1&limit=10&query=ABC123&startDate=2025-04-01&endDate=2025-04-30&status=scheduled
Authorization: Bearer {{vendorAuthToken}}


