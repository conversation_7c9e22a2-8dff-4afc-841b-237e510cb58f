import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const AdmissionRequestsMedia = new Schema({
  requestId: {
    type: Schema.Types.ObjectId,
    ref: 'AdmissionRequests',
    required: true,
  },
  documentId: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
    requred: true,
  },
  createdAt: { type: String, default: getCurrentDateTime },
  updatedAt: { type: String, default: getCurrentDateTime },
});

const admissionRequestsMedia = model('admissionRequestsMedia', AdmissionRequestsMedia);

export default admissionRequestsMedia;
