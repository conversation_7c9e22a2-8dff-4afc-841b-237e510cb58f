export interface DispersionDTO {
  state: string;
  city: string;
  quantity: number;
  deliveryDate: string; // ISO date string
  amount: number;
}

export interface UpdateDispersionDTO {
  dispersion: DispersionDTO[];
}

/**
 * Valida los datos para actualizar la dispersión de una orden de flota
 */
export function validateUpdateDispersionDTO(data: any): {
  isValid: boolean;
  errors: string[];
  validatedData?: UpdateDispersionDTO;
} {
  const errors: string[] = [];

  // Validar que dispersion sea un array
  if (!data.dispersion || !Array.isArray(data.dispersion)) {
    errors.push('La dispersión es requerida y debe ser un array');
    return { isValid: false, errors };
  }

  if (data.dispersion.length === 0) {
    errors.push('Debe incluir al menos una dispersión');
    return { isValid: false, errors };
  }

  // Validar cada elemento de dispersión
  data.dispersion.forEach((dispersion: any, index: number) => {
    const dispersionErrors = validateDispersionDTO(dispersion, index);
    errors.push(...dispersionErrors);
  });

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    validatedData: {
      dispersion: data.dispersion,
    },
  };
}

/**
 * Valida los datos de una dispersión individual
 */
function validateDispersionDTO(dispersion: any, index: number): string[] {
  const errors: string[] = [];
  const prefix = `Dispersión ${index + 1}:`;

  // Validar state
  if (!dispersion.state || typeof dispersion.state !== 'string') {
    errors.push(`${prefix} el estado es requerido`);
  }

  // Validar city
  if (!dispersion.city || typeof dispersion.city !== 'string') {
    errors.push(`${prefix} la ciudad es requerida`);
  }

  // Validar quantity
  if (!dispersion.quantity || typeof dispersion.quantity !== 'number') {
    errors.push(`${prefix} la cantidad es requerida y debe ser un número`);
  } else if (dispersion.quantity < 1) {
    errors.push(`${prefix} la cantidad debe ser mayor a 0`);
  }

  // Validar deliveryDate
  if (!dispersion.deliveryDate || typeof dispersion.deliveryDate !== 'string') {
    errors.push(`${prefix} la fecha de entrega es requerida`);
  } else {
    const deliveryDate = new Date(dispersion.deliveryDate);
    if (isNaN(deliveryDate.getTime())) {
      errors.push(`${prefix} la fecha de entrega debe ser una fecha válida`);
    } else if (deliveryDate < new Date()) {
      errors.push(`${prefix} la fecha de entrega no puede ser en el pasado`);
    }
  }

  // Validar amount
  if (!dispersion.amount || typeof dispersion.amount !== 'number') {
    errors.push(`${prefix} el monto es requerido y debe ser un número`);
  } else if (dispersion.amount < 0) {
    errors.push(`${prefix} el monto debe ser mayor o igual a 0`);
  }

  return errors;
}

/**
 * Valida que la dispersión sea consistente con la orden original
 */
export function validateDispersionConsistency(
  dispersion: DispersionDTO[],
  totalUnits: number,
  totalAmount: number
): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Calcular totales de la dispersión
  const dispersionTotalUnits = dispersion.reduce((sum, d) => sum + d.quantity, 0);
  const dispersionTotalAmount = dispersion.reduce((sum, d) => sum + d.amount, 0);

  // Validar que las unidades coincidan
  if (dispersionTotalUnits !== totalUnits) {
    errors.push(
      `El total de unidades en la dispersión (${dispersionTotalUnits}) no coincide con el total de la orden (${totalUnits})`
    );
  }

  // Validar que los montos coincidan (con tolerancia de 1 peso por redondeo)
  const amountDifference = Math.abs(dispersionTotalAmount - totalAmount);
  if (amountDifference > 1) {
    errors.push(
      `El total del monto en la dispersión ($${dispersionTotalAmount}) no coincide con el total de la orden ($${totalAmount})`
    );
  }

  // Validar que no haya duplicados de ciudad/estado
  const cityStateKeys = new Set();
  dispersion.forEach((d, index) => {
    const key = `${d.state}-${d.city}`;
    if (cityStateKeys.has(key)) {
      errors.push(`Dispersión ${index + 1}: combinación de estado y ciudad duplicada (${d.state}, ${d.city})`);
    }
    cityStateKeys.add(key);
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
}
