# Fleet Orders API Documentation

## Descripción General

El módulo Fleet Orders gestiona el ciclo completo de adquisición mensual de flotas vehiculares, incluyendo control de SLAs, evidencias y notificaciones automáticas.

## Base URL

```
/vendor-platform/fleet-orders
```

## Autenticación

Todas las rutas requieren autenticación mediante token Bearer y permisos específicos del sistema de roles.

### Headers requeridos:
```
Authorization: Bearer <token>
Content-Type: application/json
```

## Endpoints Principales

### 1. <PERSON><PERSON><PERSON> Flot<PERSON>

**POST** `/vendor-platform/fleet-orders`

Crea una nueva orden de flota para un mes específico.

**Permisos requeridos:** Solo usuarios OCN

**Body:**
```json
{
  "month": 3,
  "year": 2024,
  "vehicles": [
    {
      "brand": "BYD",
      "dealer": "BYD Mexico",
      "model": "Dolphin Mini",
      "version": "Comfort",
      "quantity": 50,
      "unitPrice": 350000
    }
  ],
  "notificationEmails": [
    "<EMAIL>",
    "<EMAIL>"
  ]
}
```

**Response:**
```json
{
  "message": "Orden de flota creada exitosamente",
  "data": {
    "orderNumber": "FO-2024-03-001",
    "month": 3,
    "year": 2024,
    "status": "created",
    "totalUnits": 50,
    "totalAmount": 17500000,
    "sentDeadline": "2024-03-06T23:59:59.999Z",
    "dispersionDeadline": "2024-03-22T23:59:59.999Z"
  }
}
```

### 2. Listar Órdenes

**GET** `/vendor-platform/fleet-orders`

Lista órdenes con filtros y paginación.

**Query Parameters:**
- `status` (opcional): Estado de la orden
- `month` (opcional): Mes
- `year` (opcional): Año
- `page` (opcional): Página (default: 1)
- `limit` (opcional): Límite por página (default: 10)

**Response:**
```json
{
  "message": "Órdenes obtenidas exitosamente",
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3
  }
}
```

### 3. Obtener Orden por ID

**GET** `/vendor-platform/fleet-orders/:id`

**Response:**
```json
{
  "message": "Orden obtenida exitosamente",
  "data": {
    "orderNumber": "FO-2024-03-001",
    "status": "created",
    "vehicles": [...],
    "statusHistory": [...],
    "dispersion": [...]
  }
}
```

### 4. Actualizar Estado de Orden

**PATCH** `/vendor-platform/fleet-orders/:id/status`

Actualiza el estado de una orden con evidencia opcional.

**Body:**
```json
{
  "status": "sent",
  "evidence": {
    "type": "log",
    "description": "Orden enviada a proveedores por email",
    "url": "https://..."
  },
  "notes": "Enviado a BYD y MG"
}
```

### 5. Actualizar Dispersión

**PATCH** `/vendor-platform/fleet-orders/:id/dispersion`

Actualiza la dispersión de vehículos por ciudad y fecha.

**Body:**
```json
{
  "dispersion": [
    {
      "state": "CDMX",
      "city": "Ciudad de México",
      "quantity": 25,
      "deliveryDate": "2024-03-25T00:00:00.000Z",
      "amount": 8750000
    },
    {
      "state": "Jalisco",
      "city": "Guadalajara",
      "quantity": 25,
      "deliveryDate": "2024-03-27T00:00:00.000Z",
      "amount": 8750000
    }
  ]
}
```

## Endpoints de SLA y Alertas

### 1. Obtener Alertas Pendientes

**GET** `/vendor-platform/fleet-orders/sla/alerts`

**Query Parameters:**
- `type` (opcional): `sla_warning` | `sla_exceeded`

### 2. Ejecutar Verificación de SLA

**POST** `/vendor-platform/fleet-orders/sla/check`

Ejecuta verificación manual de SLAs (solo usuarios OCN).

### 3. Obtener Estadísticas de SLA

**GET** `/vendor-platform/fleet-orders/sla/statistics`

**Response:**
```json
{
  "data": {
    "totalActiveOrders": 5,
    "ordersOnTime": 3,
    "ordersWithWarnings": 1,
    "ordersExceeded": 1,
    "alertsByStatus": {
      "created": 2,
      "sent": 1
    }
  }
}
```

## Estados de Orden

| Estado | Descripción | SLA |
|--------|-------------|-----|
| `created` | Orden creada | Enviar antes del día 6 |
| `sent` | Orden enviada a OEM | Dispersión antes del día 22 |
| `dispersion` | Dispersión registrada | Solicitar cartas antes del día 1 del siguiente mes |
| `invoice_letter_request` | Cartas solicitadas | Recibir cartas antes del día 4 del siguiente mes |
| `invoice_letter_arrival` | Cartas recibidas | Notificar proveedores en 2 días |
| `supplier_notification` | Proveedores notificados | - |
| `waiting_for_cars` | Esperando vehículos | Variable según dispersión |
| `delivered` | Vehículos entregados | Estado final |

## Tipos de Evidencia

- `log`: Registro de actividad
- `photo`: Fotografía
- `pdf`: Documento PDF
- `document`: Documento general

## Códigos de Error

| Código | Descripción |
|--------|-------------|
| `VALIDATION_ERROR` | Datos de entrada inválidos |
| `ORDER_ALREADY_EXISTS` | Ya existe orden para el período |
| `ORDER_NOT_FOUND` | Orden no encontrada |
| `INVALID_STATUS_TRANSITION` | Transición de estado inválida |
| `EVIDENCE_REQUIRED` | Evidencia requerida para el estado |
| `INSUFFICIENT_PERMISSIONS` | Permisos insuficientes |
| `OCN_ONLY_ACCESS` | Solo usuarios OCN |

## Notificaciones Slack

El sistema envía automáticamente notificaciones a Slack para:

- Nuevas órdenes creadas
- Cambios de estado
- Alertas de SLA (advertencias y críticas)
- Resumen diario

### Configuración de Slack

Variables de entorno requeridas:
```
SLACK_WEBHOOK_URL=https://hooks.slack.com/...
FLEET_ORDERS_SLACK_CHANNEL=#fleet-orders
FLEET_ORDERS_SLACK_ENABLED=true
```

## Cron Jobs

El sistema ejecuta automáticamente:

- **Verificación de SLA**: Cada hora (8 AM - 6 PM, Lun-Vie)
- **Resumen diario**: 9:00 AM todos los días
- **Alertas críticas**: Cada 30 minutos
- **Limpieza**: 2:00 AM todos los domingos

## Ejemplos de Uso

### Crear orden mensual
```bash
curl -X POST /vendor-platform/fleet-orders \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "month": 4,
    "year": 2024,
    "vehicles": [
      {
        "brand": "BYD",
        "dealer": "BYD Mexico",
        "model": "Dolphin Mini",
        "version": "Comfort",
        "quantity": 30,
        "unitPrice": 350000
      }
    ],
    "notificationEmails": ["<EMAIL>"]
  }'
```

### Actualizar a estado "enviado"
```bash
curl -X PATCH /vendor-platform/fleet-orders/507f1f77bcf86cd799439011/status \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "sent",
    "evidence": {
      "type": "log",
      "description": "Orden enviada por email a BYD Mexico"
    }
  }'
```
