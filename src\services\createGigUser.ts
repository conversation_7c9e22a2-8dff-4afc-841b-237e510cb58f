import axios from 'axios';
import { associatePaymentsConsts, stockVehiclesText } from '../constants';
import Associate from '../models/associateSchema';
import { ValidRegion, tokenAssignGigstack } from '../services/tokenAssignGigstack';
import AssociatePayments from '../models/associatePayments';
import getAllWire4Accounts from '../services/getAllWire4Accounts';
import StockVehicle from '../models/StockVehicleSchema';

export async function createGigUser(clabe: string, region: string) {
  try {
    const monexAccount = await getAllWire4Accounts(clabe);
    if (!monexAccount) {
      throw new Error('Usuario no encontrado');
    }

    const associate = await Associate.findOne({ email: monexAccount[0] });
    if (!associate) {
      throw new Error(associatePaymentsConsts.errors.mainContract404);
    }

    const associatePayments = await AssociatePayments.findOne({ associateEmail: monexAccount[0] });
    if (!associatePayments) {
      throw new Error(associatePaymentsConsts.errors.mainContract404);
    }

    const stockVehicle = await StockVehicle.findById(associatePayments.vehiclesId);
    if (!stockVehicle) {
      throw new Error(stockVehiclesText.errors.vehicleNotFound);
    }

    const gigToken = tokenAssignGigstack(region as ValidRegion);
    if (!region) {
      throw new Error(associatePaymentsConsts.errors.notValidRegion);
    }
    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };
    const company = stockVehicle.extensionCarNumber
      ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
      : stockVehicle.carNumber;

    const associateData = {
      email: associate.email,
      rfc: associate.rfc,
      company,
      name: `${associate.firstName} ${associate.lastName}`,
      phone: associate.phone,
      metadata: {
        clabe: clabe,
        internalId: associate._id,
      },
      address: {
        street: associate.addressStreet,
        exterior: associate.exterior,
        neighborhood: associate.delegation,
        city: associate.city,
        state: associate.state,
        zip: associate.postalCode,
      },
    };

    const { data } = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/client',
      associateData,
      config
    );
    if (associatePayments) {
      associatePayments.gigId = data.client.id;
      await associatePayments.save();
    }

    return { message: 'usuario creado correctamente', data };
  } catch (error: any) {
    console.error(error);
    return error.response.data.message;
  }
}
