import { v4 as uuidv4 } from 'uuid';

/**
 * Generates a unique file name for S3 storage, typically by prepending a UUID
 * and sanitizing the original file name.
 * @param originalFileName The original name of the file.
 * @returns A unique and sanitized file name.
 */
export const generateUniqueFileNameForS3 = (originalFileName: string): string => {
  const sanitizedFileName = originalFileName
    .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace invalid characters with underscore
    .replace(/\s+/g, '_'); // Replace spaces with underscore

  const uniqueId = uuidv4();
  return `${uniqueId}-${sanitizedFileName}`;
};
