import { CompanyPermissionsService } from '../services/company-permissions.service';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import { MiddlewareController } from '@/types&interfaces/types';

const permissionsService = new CompanyPermissionsService();

export const validateCompanyAccess: MiddlewareController = async (req, _, next) => {
  const userId = req.userVendor.userId;
  const { companyId, cityId, crewId } = req.params;

  // const hasAccess = await permissionsService.validateUserAccess(userId, companyId, cityId, crewId);
  const hasAccess = await permissionsService.validateUserAccess({
    userId,
    companyId,
    cityId,
    crewId,
  });
  if (!hasAccess) {
    throw HttpException.Forbidden('Insufficient permissions');
  }

  next();
};
