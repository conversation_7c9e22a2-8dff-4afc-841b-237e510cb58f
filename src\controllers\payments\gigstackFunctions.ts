import axios from 'axios';
import { associatePaymentsConsts, associateText, stockVehiclesText } from '../../constants';
import Associate from '../../models/associateSchema';
import RegionsPayments from '../../models/regionPaymentsSchema';
import { ValidRegion, getGigstackConfig, tokenAssignGigstack } from '../../services/tokenAssignGigstack';
import { AsyncController } from '../../types&interfaces/types';
import AssociatePayments from '../../models/associatePayments';
import getAllWire4Accounts from '../../services/getAllWire4Accounts';
import StockVehicle from '../../models/StockVehicleSchema';
import MainContractSchema from '../../models/mainContractSchema';
import { parse } from 'date-fns';
import moment from 'moment';

export const getClientsList: AsyncController = async (req, res) => {
  const { regionCode, id } = req.query;
  if (!regionCode) return res.status(400).send({ message: 'Region no válida' });

  const baseURL = 'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/clients/list';

  const url = new URL(baseURL);

  if (id) url.searchParams.append('id', id as string);

  try {
    const gigConfig = getGigstackConfig(regionCode as any);

    const { data: result } = await axios.get(url.toString(), gigConfig);

    const data = {
      total: result.clients.length,
      ...result,
    };

    return res.status(200).send(data);
  } catch (error) {
    return res.status(400);
  }
};

export const getRecurrentPayments: AsyncController = async (req, res) => {
  const { regionCode, id, startAfter, limit, all = 'false' } = req.query;
  if (!regionCode) return res.status(400).send({ message: 'Region no válida' });
  const allParse = JSON.parse(all as string);

  const baseURL = 'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/recurring/list';

  const url = new URL(baseURL);

  if (id) url.searchParams.append('id', id as string);
  if (startAfter) url.searchParams.append('startAfter', startAfter as string);
  if (limit) url.searchParams.append('limit', limit as string);
  const gigConfig = getGigstackConfig(regionCode as any);

  try {
    if (allParse) {
      /* get all data recursive */
      let data: any = {
        data: [],
        total: 0,
      };
      let next = true;
      let startAfterParam: string = '';
      while (next) {
        const { data: resultAll } = await axios.get(url.toString(), gigConfig);
        // console.log('result.data.length', resultAll.data.length);
        data.total += resultAll.data.length;

        data.data = [...data.data, ...resultAll.data];
        startAfterParam = resultAll.data[resultAll.data.length - 1].id;
        // console.log('startAfterParam', startAfterParam);
        url.searchParams.set('startAfter', startAfterParam);
        const Limit = limit ? limit : 10;
        if (resultAll.data.length < Limit) {
          next = false;
          data = {
            ...data,
            message: resultAll.message,
            totalEvents: resultAll.totalEvents,
            startAfter: resultAll.startAfter,
          };
          break;
        }
      }

      return res.status(200).send(data);
    }

    const { data: result } = await axios.get(url.toString(), gigConfig);

    if (Array.isArray(result.data)) {
      // const ids = result.data.map((item: any) => item.id);
      // console.log('ids 1', ids);
      const data = {
        total: result.data.length,
        ...result,
      };

      return res.status(200).send(data);
    }
    return res.status(200).send(result);
  } catch (error) {
    console.error(error);
    return res.status(500);
  }
};

export const gigstackRecurrentPayment: AsyncController = async (req, res) => {
  const { regionCode } = req.params;
  const { associateID } = req.body;
  try {
    const region = await RegionsPayments.findOne({ region: regionCode });
    if (!region) {
      return res.status(400).json({ error: associatePaymentsConsts.errors.notValidRegion });
    }
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);
    const associate = await Associate.findById(associateID);
    if (!associate) {
      return res.status(404).send({ message: associateText.errors.associateNotFound });
    }

    const mainContract = await MainContractSchema.findOne({ associatedId: associateID });

    if (!mainContract)
      return res.status(404).send({ message: associatePaymentsConsts.errors.mainContract404 });

    const associatePayment = await AssociatePayments.findOne({ contractId: mainContract._id });

    if (!associatePayment) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.paymentNotFound });
    }

    if (!associatePayment.model) {
      return res.status(400).send({ message: associatePaymentsConsts.errors.modelNotFound });
    }

    // const formatStartDate = parse(mainContract.allPayments[0].day, 'dd-MM-yyyy', new Date());
    const formatEndDate = parse(
      mainContract.allPayments[mainContract.allPayments.length - 1].day,
      'dd-MM-yyyy',
      new Date()
    );

    // const timestampEnMilisegundos = formatStartDate.getTime();
    const timestampEnMilisegundosEnd = formatEndDate.getTime();

    const startDate = moment().valueOf();
    const endDate = timestampEnMilisegundosEnd;

    const gigConfig = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const gigData = {
      payment: {
        client: {
          name: `${associate.firstName} ${associate.lastName}`,
          email: associate.email,
        },
        items: [
          {
            id: region.models[associatePayment.model].rentID,
            quantity: 1,
          },
          {
            id: region.models[associatePayment.model].assistanceID,
            quantity: 1,
          },
        ],
        currency: 'MXN',
        methodsTypesOptions: ['bank'],
        automateInvoiceOnComplete: true,
      },
      useClientBankAccount: true,
      temporality: 'weekly',
      onWeekday: 'thursday',
      onTime: '17:00',
      startDate,
      endDate,
    };

    const { data } = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/recurring/payment',
      gigData,
      gigConfig
    );

    const suscriptionId = data.data.id;
    associatePayment.suscriptionId = suscriptionId;

    await associatePayment.save();

    return res.status(200).send(data);
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const firstAssociatePayment: AsyncController = async (req, res) => {
  const { email } = req.body;
  const { regionCode } = req.params;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: email });
    if (!associatePayments) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }
    const associate = await Associate.findById(associatePayments.associateId);

    if (!associate) {
      return res.status(404).send({ message: associateText.errors.associateNotFound });
    }

    const region = await RegionsPayments.findOne({ region: regionCode });
    if (!region) {
      return res.status(400).json({ error: associatePaymentsConsts.errors.notValidRegion });
    }

    if (!associatePayments.model) {
      return res.status(400).send({ message: associatePaymentsConsts.errors.modelNotFound });
    }

    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const dataGig = {
      client: {
        name: `${associate.firstName} ${associate.lastName}`,
        email: associate.email,
      },
      items: [
        {
          id: region.models[associatePayments.model].rentID,
          quantity: 1,
        },
        {
          id: region.models[associatePayments.model].assistanceID,
          quantity: 1,
        },
      ],
      automateInvoiceOnComplete: true,
      currency: 'MXN',
      useClientBankAccont: true,
      methodsTypesOptions: ['bank'],
    };
    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const { data } = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/create',
      dataGig,
      config
    );
    return res.status(200).send({ message: associatePaymentsConsts.success.paymentCreated, data });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const getPendingPaymentsSimplified: AsyncController = async (req, res) => {
  const { regionCode } = req.params;

  const region = await RegionsPayments.findOne({ region: regionCode });
  if (!region) {
    return res.status(400).json({ error: 'Región no válida' });
  }
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);

    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const response = await axios.get(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/payments/list?limit=100&status=requires_payment_method',
      config
    );

    const simplifiedData = response.data.data.map((item: any) => ({
      fecha: new Date(item.timestamp),
      status: item.status,
      phone: item.client.phone,
      totalmontoPagar: item.items.total,
      shortURL: item.shortUrl,
    }));

    return res.status(200).send(simplifiedData);
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: 'Ocurrio un error', error });
  }
};

export const createGigstackAssociate: AsyncController = async (req, res) => {
  const { clabe } = req.body;
  try {
    const monexAccount = await getAllWire4Accounts(clabe);
    if (!monexAccount) {
      return res.status(404).send({ message: 'Usuario no encontrado' });
    }

    const associate = await Associate.findOne({ email: monexAccount[0] });
    if (!associate) {
      return res.status(400).send({ message: associatePaymentsConsts.errors.mainContract404 });
    }

    const associatePayments = await AssociatePayments.findOne({ associateEmail: monexAccount[0] });
    if (!associatePayments) {
      return res.status(400).send({ message: associatePaymentsConsts.errors.mainContract404 });
    }

    const stockVehicle = await StockVehicle.findById(associatePayments.vehiclesId);
    if (!stockVehicle) {
      return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
    }
    const region = await RegionsPayments.findOne({ region: associatePayments.region });
    if (!region) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.notValidRegion });
    }

    const gigToken = tokenAssignGigstack(region.region as ValidRegion);
    if (!region) {
      return res.status(400).json({ error: associatePaymentsConsts.errors.notValidRegion });
    }
    const config = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };
    const company = stockVehicle.extensionCarNumber
      ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
      : stockVehicle.carNumber;

    const associateData = {
      email: associate.email,
      rfc: associate.rfc,
      company,
      name: `${associate.firstName} ${associate.lastName}`,
      phone: associate.phone,
      metadata: {
        clabe: clabe,
        internalId: associate._id,
      },
      address: {
        street: associate.addressStreet,
        exterior: associate.exterior,
        neighborhood: associate.delegation,
        city: associate.city,
        state: associate.state,
        zip: associate.postalCode,
      },
    };

    const { data } = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/client',
      associateData,
      config
    );
    if (associatePayments) {
      associatePayments.gigId = data.client.id;
      await associatePayments.save();
    }

    return res.status(200).send({ message: 'usuario creado correctamente', data });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const gigExistingUsers: AsyncController = async (req, res) => {
  const { regionCode } = req.params;
  try {
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);
    const config = {
      headers: {
        Authorization: gigToken,
      },
    };
    const { data } = await axios.get('https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/clients/list', config);

    data.clients.map(async (associate: any) => {
      const existingAssociate = await Associate.findOne({ email: associate.email });

      AssociatePayments.create({
        gigId: associate.id,
        vehiclesId: existingAssociate?.vehiclesId,
      });
    });
    return res.status(200).send({ message: 'Agregado cliente basado en gigstack', data });
  } catch (error) {
    console.error(error);
    return res.status(400).send({ error });
  }
};
