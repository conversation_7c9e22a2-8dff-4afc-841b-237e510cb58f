import { ENCR<PERSON><PERSON>ON_IV, ENCRYP<PERSON>ON_KEY } from '@/constants';
import { createCipheriv, createDecipheriv } from 'crypto';

const algorithm = 'aes-256-cbc';

const key = Buffer.from(ENCRYPTION_KEY, 'hex');
const iv = Buffer.from(ENCRYPTION_IV, 'hex');

export const encodeString = (refreshToken: string): string => {
  const cipher = createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(refreshToken, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

export const decodeString = (encryptedToken: string): string => {
  const decipher = createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encryptedToken, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};
