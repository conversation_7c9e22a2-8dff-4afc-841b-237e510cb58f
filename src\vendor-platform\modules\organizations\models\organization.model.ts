import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';
import { ScheduleConfig } from '@/constants/vendor-platform';
import { scheduleConfigSchema } from '../../workshop/models/workshops.model';
export interface IOrganization extends Document {
  name: string;
  users: mongoose.Types.ObjectId[]; // Referencias a los usuarios de la organización
  country: string;
  website?: string;
  created: Date;
  updated: Date;
  globalScheduleConfig: ScheduleConfig;
}

const OrganizationSchema = new Schema<IOrganization>({
  name: { type: String, required: true },
  users: [{ type: Schema.Types.ObjectId, ref: 'Users' }],
  country: { type: String, required: true },
  website: { type: String, required: false },

  globalScheduleConfig: {
    type: scheduleConfigSchema,
  },

  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now },
});

// const OrganizationModel = mongoose.model<IOrganization>('Organization', OrganizationSchema);
const OrganizationModel = vendorDB.model<IOrganization>('Organization', OrganizationSchema);

export default OrganizationModel;
