import { uploadFile } from '../aws/s3';
import Document from '../models/documentSchema';
import User from '../models/userSchema';
import PermissionSet from '../models/permissionSetSchema';
import UserVehicleRestrictions from '../models/userVehicleRestriction';
import StockVehicle, { UpdatedVehicleStatus, VehicleCategory } from '../models/StockVehicleSchema';
import { replaceDocWithUrl } from '../services/getPropertyWithUrls';
import { removeEmptySpacesNameFile } from '../services/removeEmptySpaces';
import type { AsyncController } from '../types&interfaces/types';
import bcrypt from 'bcrypt';
import { CountriesEnum, homeVisitorsColors, Roles } from '../constants';
import { logger } from '@/clean/lib/logger';
import { calendarService } from '@/modules/Calendar/services/calendar.service';
import { Types } from 'mongoose';
import AuditLog from '../models/AuditLogSchema';

export const getUserData: AsyncController = async (req, res) => {
  const { id } = req.params;
  const user = await User.findById(id, { password: 0 }).lean();

  if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });

  let image;

  if (user.image) {
    image = await replaceDocWithUrl(user.image._id.toString());
  }

  const permissionSet = await PermissionSet.findOne({
    role: user.role,
    area: user.area,
  }).lean();

  if (permissionSet) {
    let permissions = [...(permissionSet?.permissions || []), ...(user.permissions || [])];
    const uniquePermissions = Array.from(
      new Map(permissions.map((p) => [`${p.section}.${p.subSection}.${p.capability}`, p])).values()
    );
    user.permissions = uniquePermissions;
  }

  const userData = {
    ...user,
    image,
  };
  return res.status(200).send({ message: 'Datos del usuario', user: userData });
};

// DEVUELVE LA LISTA DE USUARIOS SI LA PETICIÓN VIENE DE UN ADMIN, **FUNCION GET

export const getAllUsers: AsyncController = async (req, res) => {
  const { adminId } = req.query;
  if (!adminId) return res.status(400).send({ message: 'Id del usuario administrador requerido' });

  const adminUser = await User.findById(adminId);
  if (!adminUser) return res.status(400).send({ message: 'Usuario no encontrado' });

  const filter = adminUser.role === Roles.superadmin ? {} : { area: adminUser.area ?? '' };

  const allUsers = await User.find(filter, { password: 0, googleRefreshToken: 0 }).lean();
  const processedUsers = [];

  for (const user of allUsers) {
    if (user.image) {
      const image = await replaceDocWithUrl(user.image._id.toString());
      processedUsers.push({ ...user, image });
    } else {
      processedUsers.push({ ...user, image: { docId: null, originalName: null, url: null } });
    }
  }

  return res.status(200).send({
    message: `Total de ${allUsers.length} Usuarios`,
    users: processedUsers,
  });
};

export const sideData: AsyncController = async (req, res) => {
  try {
    logger.info(`[sideData] Starting to fetch side data with country: ${req.query.country || 'default'}`);
    const { country } = req.query || '';

    const queryCountry = country || CountriesEnum.Mexico;

    const countryCondition =
      queryCountry === CountriesEnum['United States']
        ? { country: CountriesEnum['United States'] }
        : { $or: [{ country: CountriesEnum.Mexico }, { country: { $exists: false } }] };

    const vehicleCounts = await Promise.all([
      StockVehicle.countDocuments({
        vehicleStatus: UpdatedVehicleStatus.active,
        ...countryCondition,
      }),
      StockVehicle.countDocuments({
        vehicleStatus: UpdatedVehicleStatus.inactive,
        ...countryCondition,
      }),
      ...Object.values(VehicleCategory).map((category) =>
        StockVehicle.countDocuments({
          category,
          ...countryCondition,
        })
      ),
    ]);

    const sideBarData = {
      vehicleStatusCounts: {
        active: vehicleCounts[0],
        inactive: vehicleCounts[1],
      },
      categoryCounts: {
        withdrawn: vehicleCounts[2],
        sold: vehicleCounts[3],
        insurance: vehicleCounts[4],
        collection: vehicleCounts[5],
        legal: vehicleCounts[6],
        workshop: vehicleCounts[7],
        revision: vehicleCounts[8],
        adendum: vehicleCounts[9],
        'in-preparation': vehicleCounts[10],
        stock: vehicleCounts[11],
        assigned: vehicleCounts[12],
        delivered: vehicleCounts[13],
        utilitary: vehicleCounts[14],
      },
    };

    logger.info(
      `[sideData] Successfully fetched side data with ${vehicleCounts[0]} active and ${vehicleCounts[1]} inactive vehicles`
    );
    return res.status(200).send({ message: 'success', sideBarData });
  } catch (error) {
    logger.error(`[sideData] Error fetching side data: ${error}`);
    return res.status(500).send({ message: 'Server Error', error });
  }
};
// EDIT USER

export const editUserByAdmin: AsyncController = async (req, res) => {
  const { allowedRegions, role, area } = req.body;
  const { id: userId } = req.params;
  const { userId: currentUser } = req.userId;

  try {
    const userToEdit = await User.findById(userId);
    const admin = await User.findById(currentUser);
    if (!admin) return res.status(404).send({ message: 'Usuario administrador no encontrado' });
    if (!userToEdit) return res.status(404).send({ message: 'Usuario no encontrado' });

    if (admin.role !== 'superadmin')
      if (userToEdit.area !== admin.area)
        return res.status(409).send({ message: 'No tienes permitido realizar esta acción' });

    if (allowedRegions) {
      userToEdit.settings = {
        allowedRegions: allowedRegions,
      };
    }

    if (role) {
      userToEdit.role = role;
    }
    if (area) {
      userToEdit.area = area;
    }
    userToEdit.save();

    await AuditLog.create({
      action: 'USER_UPDATE',
      performedBy: currentUser,
      timestamp: new Date(),
      metadata: {
        targetUserID: userToEdit._id,
        reason: 'update via admin panel',
      },
    });

    return res.status(200).send({ message: 'Usuario editado correctamente' });
  } catch (error) {
    return res.status(500).send({ message: 'Error al editar el usuario', error });
  }
};

/* UPDATE USER */

export const updateUserById: AsyncController = async (req, res) => {
  const { id } = req.params;
  const image: Express.Multer.File | undefined = req.file;
  const { password } = req.body;

  try {
    const user = await User.findById(id, { password: 0 });
    if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });
    if (image) {
      const removeSpace = removeEmptySpacesNameFile(image);
      const imageDoc = new Document({
        originalName: removeSpace,
        path: `users/${user._id}/profile/${removeSpace}`,
        userId: user._id,
      });
      await uploadFile(image, removeSpace, `users/${user._id}/profile/`);
      await imageDoc.save();

      user.image = imageDoc._id;
    }

    if (password) {
      const hashedPassword = await bcrypt.hash(password, 12);
      user.password = hashedPassword;
    }

    await user.save();

    return res.status(200).send({ message: 'Usuario Actualizado!', user });
  } catch (error) {
    return res.status(500).send({ message: 'Error al actualizar el usuario', error });
  }
};

export const verifyPassword: AsyncController = async (req, res) => {
  const userRequest = req.userId;
  const { password } = req.body;
  try {
    const currentUser = await User.findById(userRequest.userId, { password: 1 });
    if (!currentUser) return res.status(404).send({ message: 'Usuario no encontrado' });

    const rightPassword = await bcrypt.compare(password, currentUser.password);

    if (!rightPassword) return res.status(400).send({ message: 'Contraseña incorrecta' });

    return res.status(200).send({ message: 'Contraseña correcta' });
  } catch (error) {
    console.log('[VERIFY PASSWORD]', error);
    return res.status(500).send({ message: 'Algo salió mal', error });
  }
};

export const createUserVehiclesRestrictions: AsyncController = async (req, res) => {
  const { userId, stockRestrictions } = req.body;
  try {
    const user = await User.findById(userId);
    if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });

    const newRestrictions = new UserVehicleRestrictions({
      userId: user._id,
      stockRestrictions,
    });

    await newRestrictions.save();
    return res
      .status(201)
      .send({ message: 'Restricciones creadas correctamente', restrictions: newRestrictions });
  } catch (error) {
    return res.status(500).send({ message: 'Error al crear las restricciones', error });
  }
};

export const getUserVehiclesRestrictions: AsyncController = async (req, res) => {
  const { userId } = req.params;
  try {
    const user = await User.findById(userId);
    if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });

    const hasRestrictions = await UserVehicleRestrictions.findOne({ userId: user._id });
    if (!hasRestrictions)
      return res.status(200).send({ message: 'No hay restricciones', restrictions: null });

    return res.status(200).send({ message: 'Restricciones', restrictions: hasRestrictions });
  } catch (error) {
    return res.status(500).send({ message: 'Error al obtener las restricciones', error });
  }
};

export const updateUserHomeVisitStatus: AsyncController = async (req, res) => {
  const { id: userId } = req.params;
  const { userId: currentUser } = req.userId;

  try {
    const userToEdit = await User.findById(userId);
    const admin = await User.findById(currentUser);
    if (!admin) return res.status(404).send({ message: 'Usuario administrador no encontrado' });
    if (!userToEdit) return res.status(404).send({ message: 'Usuario no encontrado' });

    if (req.body.homeVisitor === false) {
      /**
       * needs to check if this user has any home visit appointment in future
       */
      const appointmentExists = await calendarService.checkUserFutureAppointment(
        userToEdit._id as unknown as string
      );
      if (appointmentExists) {
        logger.warn(
          `[updateUserHomeVisitStatus] User ${userToEdit._id} has future appointments, cannot revoke home visitor status`
        );
        return res.status(400).send({
          message: `You cannot revoke this user's home visitor status as they have future scheduled appointments.`,
          errorKey: 'futureHomeVisitAppointments',
        });
      }
      /**
       * delete all future available slots for this user
       */
      await calendarService.deleteUserFutureAvailableSlots(userToEdit._id as unknown as string);
    }

    if (req.body?.homeVisitor === true && !userToEdit.homeVisitorColor) {
      const existingHomeVisitos = await User.find({ homeVisitor: true })
        .select(['_id', 'homeVisitor', 'homeVisitorColor'])
        .lean();

      const existingHomeVisitosColors = existingHomeVisitos.map((user) => user.homeVisitorColor);
      const randomColor = homeVisitorsColors.find((color) => !existingHomeVisitosColors.includes(color));
      if (randomColor) {
        userToEdit.homeVisitorColor = randomColor;
      }
    }

    userToEdit.homeVisitor = req.body.homeVisitor;
    await userToEdit.save();
    return res.status(200).send({ message: 'Usuario editado correctamente' });
  } catch (error) {
    return res.status(500).send({ message: 'Error al editar el usuario', error });
  }
};

export const updateUserVehiclesRestrictions: AsyncController = async (req, res) => {
  /* method could be add or remove */
  const { stockRestrictions, userId, method } = req.body;
  try {
    const user = await User.findById(userId);
    if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });

    const hasRestrictions = await UserVehicleRestrictions.findOne({ userId: user._id });
    if (!hasRestrictions) return res.status(404).send({ message: 'No hay restricciones' });

    if (method === 'add') {
      hasRestrictions.stockRestrictions.push(...stockRestrictions);
    }

    if (method === 'remove') {
      const newRestrictions = hasRestrictions.stockRestrictions.filter(
        (restriction) =>
          !stockRestrictions.some((item: any) => restriction._id && item._id == restriction._id.toString())
      );
      hasRestrictions.stockRestrictions = newRestrictions;
    }

    await hasRestrictions.save();
    return res.status(200).send({ message: 'Restricciones actualizadas', restrictions: hasRestrictions });
  } catch (error) {
    console.log('[UPDATE RESTRICTIONS]', error);
    return res.status(500).send({ message: 'Error al actualizar las restricciones', error });
  }
};

export const getAllHomeVisitors: AsyncController = async (req, res) => {
  try {
    const { adminId } = req.query;
    if (!adminId) return res.status(400).send({ message: 'Id del usuario administrador requerido' });

    const adminUser = await User.findById(adminId);
    if (!adminUser) return res.status(400).send({ message: 'Usuario no encontrado' });

    const allHomeVisitors = await User.find(
      { homeVisitor: true },
      { password: 0, googleRefreshToken: 0, settings: 0, __v: 0 }
    ).lean();

    logger.info(`[getAllHomeVisitors]: Successfully fetched home visitors. total: ${allHomeVisitors.length}`);

    return res.status(200).send({
      message: `Total de ${allHomeVisitors.length} Usuarios`,
      users: allHomeVisitors,
    });
  } catch (error: any) {
    logger.error(`[getAllHomeVisitors]: Error occured while getting home visitors`, {
      message: error.message,
      stack: error.stack,
    });

    return res.status(500).send({ message: 'Error al obtener los visitantes', error });
  }
};

export const getUserAssignPermissions: AsyncController = async (req, res) => {
  const { id } = req.params;
  const user = await User.findById(id).lean();

  if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });

  const permissionSet = await PermissionSet.findOne({
    role: user.role,
    area: user.area,
  });

  return res
    .status(200)
    .send({ message: 'Datos del usuario', user: user, permissions: permissionSet?.permissions });
};

export const deleteUser: AsyncController = async (req, res) => {
  const { id } = req.params;
  const { userId } = req.authUser;

  if (!Types.ObjectId.isValid(id)) {
    return res.status(400).json({ message: 'Invalid user ID format' });
  }

  try {
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.isActive = false;
    await user.save();

    await AuditLog.create({
      action: 'USER_DEACTIVATE',
      performedBy: userId,
      timestamp: new Date(),
      metadata: {
        targetUserID: user._id,
        reason: 'Soft delete via admin panel', // optional
      },
    });

    return res.status(200).send({ message: 'User deleted successfully' });
  } catch (error: any) {
    return res.status(500).json({ message: 'Error deleting user', error: error.message });
  }
};

// export const deleteSomeRestrictions: AsyncController = async (req, res) => {
//   const { userId, restrictions } = req.body;
//   try {
//     const user = await User.findById(userId);
//     if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });

//     const hasRestrictions = await UserVehicleRestrictions.findOne({ userId: user._id });
//     if (!hasRestrictions) return res.status(404).send({ message: 'No hay restricciones' });

//     const newRestrictions = hasRestrictions.stockRestrictions.filter(
//       (restriction) => restriction._id && !restrictions.includes(restriction._id.toString())
//     );

//     hasRestrictions.stockRestrictions = newRestrictions;
//     await hasRestrictions.save();
//     return res.status(200).send({ message: 'Restricciones actualizadas', restrictions: hasRestrictions });
//   } catch (error) {
//     return res.status(500).send({ message: 'Error al actualizar las restricciones', error });
//   }
// };
