import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const legalProcessStockSchema = new Schema({
  date: {
    type: String,
    required: true,
  },

  dateFinished: {
    type: String,
  },

  pausePayments: Boolean,

  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
  },

  isCanceled: {
    type: Boolean,
    default: false,
  },

  associateId: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
  },

  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});

const LegalProcessStock = model('legal-process', legalProcessStockSchema);

export default LegalProcessStock;
