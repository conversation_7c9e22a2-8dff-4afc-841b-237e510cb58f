import { DateTime } from 'luxon';

export function formatDateLongSpanish({ date, zone, local }: { date: Date; zone: string; local: string }) {
  return DateTime.fromJSDate(date).setZone(zone).setLocale(local).toFormat("dd 'de' LLLL 'de' yyyy");
}

export function formatTimeInHourAndMinutes({ date, zone }: { date: Date; zone: string }) {
  return DateTime.fromJSDate(date).setZone(zone).toFormat('hh:mm a');
}
