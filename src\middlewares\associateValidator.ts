import { validationResult } from 'express-validator';
import { NextFunction, Response } from 'express';
import { MyRequest } from '../types&interfaces/interfaces';
import fs from 'fs';

type Controller = (
  req: MyRequest,
  res: Response,
  next: NextFunction
) => void | Response<any, Record<string, any>>;

export const associateUpdateValidation: Controller = (req, res, next) => {
  const errors = validationResult(req.params);
  if (!errors.isEmpty()) {
    const files: any = req.files;

    for (let file in files) {
      fs.unlink(files[file][0].path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    }
    return res.status(400).json({
      errors: errors.array(),
    });
  }
  return next();
};

export const associateUnassignValidator: Controller = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      errors: errors.array(),
    });
  }
  return next();
};
