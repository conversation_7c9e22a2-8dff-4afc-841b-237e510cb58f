import { getHomeVisitScheduleLinkSendDate } from '@/clean/data/mongoRepositories';
import { AppointmentMongoAdapter, ScheduleMongoAdapter, SlotsMongoAdapter } from '@/clean/domain/adapters';
import {
  getAdmissionRequestByIdOrThrow,
  notifyCustomerAboutHomeVisitAppointmentAboutFiveMinutesBefore,
  notifyCustomerAboutHomeVisitAppointmentApology,
  notifyCustomerAboutHomeVisitAppointmentBooking,
  notifyCustomerAboutHomeVisitAppointmentCancel,
  notifyCustomerAboutHomeVisitAppointmentFinish,
  notifyCustomerAboutHomeVisitAppointmentNoShow,
  notifyCustomerAboutHomeVisitAppointmentOneNightAgo,
} from '@/clean/domain/usecases';
import {
  AppointmentAlreadyExistException,
  AppointmentNotFoundException,
  ScheduleNotFoundException,
  SlotBookingTimeLimitException,
  SlotIsNotAvailableException,
  SlotNotFoundException,
} from '@/clean/errors/exceptions';
import { logger } from '@/clean/lib/logger';
import {
  AppointmentSerializer,
  ScheduleSerializer,
  SlotsSerializer,
  UsersWithAvailableSlotSerializer,
} from '@/clean/presentation/serializers';
import {
  minBookingNoticeMinutes,
  RUN_HOME_VISIT_APPOINTMENTS_REMINDER_CRON,
  RUN_HOME_VISITORS_ADD_SLOTS_CRON,
} from '@/constants';
import {
  Appointment,
  AppointmentStatus,
  HomeVisitSchedulingActionSource,
  IAppointment,
} from '@/models/appointment';
import HubspotBatch from '@/models/hubspotBatch';
import { ISchedule, Schedule } from '@/models/Schedules';
import { ISlots, Slots } from '@/models/Slots';
import User from '@/models/userSchema';
import { getMeetingLink } from '@/services/googleAuth/googleAuthClient';
import { dealUpdate } from '@/services/hubspot';
import { DateTime } from 'luxon';
import { NO_HOME_VISITOR_AVAILABLE } from '../dtos/appointment.dto';
import mongoose from 'mongoose';
import PermissionSet from '@/models/permissionSetSchema';

class Calendar {
  async getSchedule(userId: string) {
    try {
      const schedule = await Schedule.findOne({ user: userId });
      if (!schedule) {
        return ScheduleSerializer({});
      }
      const scheduleAdapter = ScheduleMongoAdapter(schedule!);
      const weeklySchedule = this.convertScheduleTo12Hours(scheduleAdapter.weeklySchedule as any);
      scheduleAdapter.weeklySchedule = weeklySchedule as unknown as any;

      const breakTimes = this.convertBreakScheduleTo12Hours(scheduleAdapter.breakTimes as any);
      scheduleAdapter.breakTimes = breakTimes as unknown as any;

      const serialized = ScheduleSerializer(scheduleAdapter!);
      return serialized;
    } catch (error: any) {
      logger.error(`[getSchedule] - error occured while fetching schedule for user: ${userId}`, {
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async createSchedule(schedule: Record<string, any>) {
    try {
      const isUserHomeVisitor = await User.findOne({
        _id: schedule.user,
        homeVisitor: true,
      });
      if (!isUserHomeVisitor) {
        throw new Error('User is not a home visitor');
      }

      const existingSchedule = await Schedule.findOne({ user: schedule.user });
      const weeklySchedule24Hours = this.convertScheduleTo24Hours(schedule.weeklySchedule);
      schedule.weeklySchedule = weeklySchedule24Hours;
      const breakTime = this.convertBreakScheduleTo24Hours(schedule.breakTimes);
      schedule.breakTimes = breakTime;

      if (existingSchedule) {
        /**
         * need to add update logic for slots, will be done in V1
         *  1. If Schedule has been updated, then update the slots // will be done if Ops team asks for it.
         *  2. if blockSlots array is present, then block the slots
         *  3. if addedSlots array is present, then add the slots
         */
        // Starting block Slot Login
        const blockSlots = schedule.blockSlots;
        let blockSlotsErrors: Array<Record<string, string>> = [];
        if (blockSlots && blockSlots?.length > 0) {
          blockSlotsErrors = await this.blockSlots({ ...schedule, id: existingSchedule._id });
        }

        const addedSlots = schedule.addedSlots;
        let addedSlotsErrors: Array<Record<string, string>> = [];
        if (addedSlots && addedSlots?.length > 0) {
          addedSlotsErrors = await this.addSlots({ ...schedule, id: existingSchedule._id });
        }

        existingSchedule.set(schedule);
        await existingSchedule.save();
        const scheduleAdapter = ScheduleMongoAdapter(existingSchedule, blockSlotsErrors, addedSlotsErrors);
        const serialized = ScheduleSerializer(scheduleAdapter);
        return serialized;
      }

      const newSchedule = await Schedule.create(schedule);
      /**
       * start date is of one day after schedule is being setup for now (as per discussion with PM)
       * this is to ensure that slots are generated from the next day.
       */
      const startDate = DateTime.local({ zone: newSchedule.timezone }).set({
        year: DateTime.local().year,
        month: DateTime.local().month,
        day: DateTime.local().day,
        hour: 9,
        minute: 0,
        second: 0,
      });

      const TWO_WEEKS_DAYS = 10; // 5 days a week for 2 weeks
      const generatedSlots = this.generateSlots({
        schedule: newSchedule,
        startDate,
        daysToCompute: TWO_WEEKS_DAYS,
      });
      await this.createSlots(generatedSlots);

      const newScheduleAdapter = ScheduleMongoAdapter(newSchedule!);
      const serialized = ScheduleSerializer(newScheduleAdapter!);
      return serialized;
    } catch (error: any) {
      logger.error(`[createSchedule] - error occured while creating schedule for user: ${schedule.user}`, {
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async addSlots(schedule: Record<string, any>) {
    const addedSlots: Array<{
      startDate: string;
      endDate: string;
      startTime: string;
      endTime: string;
    }> = schedule.addedSlots;

    const addSlotErrors: any = [];
    for (const newSlotMetaData of addedSlots) {
      const slots = this.generateSlotsFromTimeRange(schedule, newSlotMetaData);

      for (const newSlot of slots) {
        const targetStartTime = DateTime.fromISO(newSlot?.startTime as unknown as string).set({
          millisecond: 0,
        });
        const targetStartTimeNextSecond = targetStartTime.plus({ second: 1 });

        const targetEndTime = DateTime.fromISO(newSlot?.endTime as unknown as string).set({
          millisecond: 0,
        });
        const targetEndTimeNextSecond = targetEndTime.plus({ second: 1 });
        const existingSlot = await Slots.findOne({
          user: schedule.user,
          date: {
            $gte: newSlot.date,
            $lte: newSlot.date,
          },
          startTime: {
            $gte: targetStartTime.toJSDate(),
            $lt: targetStartTimeNextSecond.toJSDate(),
          },
          endTime: {
            $gte: targetEndTime.toJSDate(),
            $lte: targetEndTimeNextSecond.toJSDate(),
          },
        });
        if (existingSlot) {
          if (existingSlot.isAvailable === true) {
            existingSlot.set({ isBlocked: false });
            await existingSlot.save();
            logger.info(
              `[addSlots] - Slot already exists for user: ${schedule.user}, just setting isBlocked to false, for date: ${newSlot.date} and time: ${targetStartTime} - ${targetEndTime}`
            );
          } else if (existingSlot.isAvailable === false) {
            const slotAlreadyExistsMessage = 'Slot already exists';
            const slotAlreadyExistsCode = 'SLOT_ALREADY_EXISTS';
            addSlotErrors.push({
              startDate: newSlotMetaData.startDate,
              endDate: newSlotMetaData.endDate,
              startTime: newSlotMetaData.startTime,
              endTime: newSlotMetaData.endTime,
              slotAlreadyExistsCode: slotAlreadyExistsCode,
              slotAlreadyExistsMessage: slotAlreadyExistsMessage,
            });
            logger.info(
              `[addSlots] - Slot already exists for user: ${schedule.user} for date: ${newSlot.date} and time: ${targetStartTime} - ${targetEndTime}`
            );
          }
          continue;
        }
        await Slots.create(newSlot);
        logger.info(
          `[addSlots] - added slots for user: ${schedule.user} for date: ${newSlot.date} , time: ${targetStartTime} - ${targetEndTime}`
        );
      }
    }
    return addSlotErrors;
  }

  private generateSlotsFromTimeRange(
    schedule: Record<string, any>,
    slotMetaData: {
      startDate: string;
      endDate: string;
      startTime: string;
      endTime: string;
    }
  ) {
    const startDate = DateTime.fromISO(slotMetaData.startDate);
    const startTime = DateTime.fromFormat(slotMetaData.startTime, 'hh:mma', { zone: schedule.timezone })
      .set({ year: startDate.year, month: startDate.month, day: startDate.day })
      .toUTC();
    const endTime = DateTime.fromFormat(slotMetaData.endTime, 'hh:mma', { zone: schedule.timezone })
      .set({ year: startDate.year, month: startDate.month, day: startDate.day })
      .toUTC();

    const slots = [];

    let currentSlotTime = startTime;
    while (currentSlotTime < endTime) {
      slots.push({
        user: schedule.user,
        date: startDate.toISODate(),
        startTime: currentSlotTime.toISO({ suppressMilliseconds: true })!, // Remove milliseconds
        endTime: currentSlotTime.plus({ minutes: schedule.duration }).toISO({ suppressMilliseconds: true })!,
        isAvailable: true,
        maxAppointments: 1,
        currentAppointments: 0,
        timezone: schedule.timezone,
        scheduleId: schedule.id,
      });
      currentSlotTime = currentSlotTime.plus({ minutes: schedule.duration });
    }
    return slots;
  }

  async blockSlots(schedule: Record<string, any>) {
    const blockSlots = schedule.blockSlots;
    const blockSlotErrors: any = [];

    for (const blockSlot of blockSlots) {
      const slots = this.generateSlotsFromTimeRange(schedule, blockSlot);
      for (const newSlot of slots) {
        const targetStartTime = DateTime.fromISO(newSlot?.startTime as unknown as string).set({
          millisecond: 0,
        });
        const targetStartTimeNextSecond = targetStartTime.plus({ second: 1 });

        const targetEndTime = DateTime.fromISO(newSlot?.endTime as unknown as string).set({
          millisecond: 0,
        });
        const targetEndTimeNextSecond = targetEndTime.plus({ second: 1 });

        const slot = await Slots.findOne({
          user: schedule.user,
          date: {
            $gte: newSlot.date,
            $lte: newSlot.date,
          },
          startTime: {
            $gte: targetStartTime.toJSDate(),
            $lt: targetStartTimeNextSecond.toJSDate(),
          },
          endTime: {
            $gte: targetEndTime.toJSDate(),
            $lte: targetEndTimeNextSecond.toJSDate(),
          },
        });

        if (!slot || slot.isAvailable === false) {
          const logMessage = !slot
            ? 'Slot not found'
            : slot.isAvailable === false
              ? 'Slot is not available to block'
              : '';
          logger.info(
            `[blockSlots] - ${logMessage}: ${schedule.user} for date: ${newSlot.date} and time: ${targetStartTime} - ${targetEndTime}`
          );
          const slotNotFoundMessage = !slot ? 'Slot not found' : '';
          const slotIsNotAvailableToBlockMessage =
            slot && slot?.isAvailable === false ? 'Slot is not available to block' : '';
          blockSlotErrors.push({
            slotNotFoundMessage,
            slotIsNotAvailableToBlockMessage,
            slotNotFoundCode: slotNotFoundMessage ? 'SLOT_NOT_FOUND_WITH_GIVEN_DATE_TIME' : '',
            slotIsNotAvailableToBlockCode: slotIsNotAvailableToBlockMessage
              ? 'SLOT_IS_NOT_AVAILABLE_TO_BLOCK'
              : '',
            startDate: blockSlot.startDate,
            endDate: blockSlot.endDate,
            startTime: blockSlot.startTime,
            endTime: blockSlot.endTime,
          });
          continue;
        }

        slot.set({ isBlocked: true });
        await slot.save();
        logger.info(
          `[blockSlots] - blocked slots for user: ${schedule.user} for date: ${newSlot.date} and time: ${targetStartTime} - ${targetEndTime}`
        );
      }
    }
    return blockSlotErrors;
  }

  async getAvailableSlots(
    admissionRequestId: string,
    requestedDateForAvailableSlots: string,
    clientTimezone: string
  ) {
    try {
      const isCustomerEligibleForAdvanceSlots = await this.validateCustomerAdvanceSlotsAvailability(
        admissionRequestId,
        requestedDateForAvailableSlots
      );

      if (!isCustomerEligibleForAdvanceSlots) {
        logger.info(
          `[getAvailableSlots] - customer with id ${admissionRequestId} is not eligible for advance slots due to max advance booking days limit`
        );
        const emptySlotsResponse = SlotsSerializer([]);
        return emptySlotsResponse;
      }

      const availableSlots = await Slots.aggregate([
        {
          $match: {
            date: new Date(requestedDateForAvailableSlots),
            isAvailable: true,
            $or: [
              { isBlocked: false }, // Match documents where isBlocked is explicitly false
              { isBlocked: { $exists: false } }, // Match documents where isBlocked does not exist
            ],
          },
        },
        {
          $addFields: {
            startTime: {
              $dateTrunc: {
                date: '$startTime',
                unit: 'second',
              },
            },
            endTime: {
              $dateTrunc: {
                date: '$endTime',
                unit: 'second',
              },
            },
          },
        },
        {
          $group: {
            _id: {
              startTime: '$startTime',
              endTime: '$endTime',
            },
            slot: {
              $first: '$$ROOT',
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: '$slot',
          },
        },
      ]).sort({
        startTime: 1,
      });
      const slotsAdapter = SlotsMongoAdapter(availableSlots as any);
      const convertedSlots = this.convertSlotsToUserTimezone(slotsAdapter as any, clientTimezone);
      const serialized = SlotsSerializer(convertedSlots);
      return serialized;
    } catch (error: any) {
      logger.error(
        `[getAvailableSlots] - error occured while fetching available slots for request: ${admissionRequestId}`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async validateCustomerAdvanceSlotsAvailability(
    admissionRequestId: string,
    requestedDateForAvailableSlots: string
  ) {
    const homeVisitScheduleLinkSendDate = await getHomeVisitScheduleLinkSendDate(admissionRequestId);
    if (homeVisitScheduleLinkSendDate) {
      const daysSinceLinkSent = DateTime.now().diff(
        DateTime.fromJSDate(homeVisitScheduleLinkSendDate),
        'days'
      ).days;
      const maxDaysAllowedToShowSlots = 30;
      if (daysSinceLinkSent > maxDaysAllowedToShowSlots) {
        return false;
      }
    }

    const maxAdvanceSlotAvailableDays = 5;
    const nextFiveDayDate = DateTime.now().plus({ days: maxAdvanceSlotAvailableDays });
    const requestedDate = DateTime.fromISO(requestedDateForAvailableSlots);
    return requestedDate < nextFiveDayDate;
  }

  async bookSlot(slotPayload: {
    admissionRequestId: string;
    slotId: string;
    isAppointmentReschedule: boolean;
    source?: string;
    entity?: mongoose.Types.ObjectId;
  }) {
    try {
      const slot = await this.checkSlotAvailability(slotPayload.slotId);
      const existingAppointment = await Appointment.findOne({
        admissionRequestId: slotPayload.admissionRequestId,
      });
      if (
        slotPayload.isAppointmentReschedule &&
        existingAppointment &&
        existingAppointment.status !== AppointmentStatus.completed
      ) {
        const existingSlotId = existingAppointment.slot;
        const updatedExistingAppointment = await this.updateExistingAppointment(existingAppointment, slot, {
          source: slotPayload.source,
          entity: slotPayload.entity,
        });

        const existingSlot = await Slots.findById(existingSlotId);
        if (existingSlot) {
          existingSlot.set({ isAvailable: true });
          await existingSlot.save();
        }
        slot.set({ isAvailable: false });
        await slot.save();

        const meetingLink = existingAppointment.meetingLink;
        await notifyCustomerAboutHomeVisitAppointmentBooking(slotPayload.admissionRequestId, {
          meetingLink: meetingLink!,
          date: slot.date.toISOString(),
          startTime: DateTime.fromJSDate(slot.startTime).toFormat('hh:mm a'),
        });

        return updatedExistingAppointment;
      }

      if (existingAppointment) {
        throw new AppointmentAlreadyExistException({});
      }

      const meetingLink = await this.makeMeetingLink(slot, slotPayload.admissionRequestId);
      const newAppointment = await this.createAppointment(slot, {
        admissionRequestId: slotPayload.admissionRequestId,
        meetingLink: meetingLink!,
        source: slotPayload.source,
        entity: slotPayload.entity,
      });
      slot.set({ isAvailable: false });
      await slot.save();

      await notifyCustomerAboutHomeVisitAppointmentBooking(slotPayload.admissionRequestId, {
        meetingLink: meetingLink!,
        date: slot.date.toISOString(),
        startTime: DateTime.fromJSDate(slot.startTime).toFormat('hh:mm a'),
      });

      return newAppointment;
    } catch (error: any) {
      logger.error(
        `[bookSlot] - error occured while booking slot for request: ${slotPayload.admissionRequestId}`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async makeMeetingLink(slot: ISlots, admissionRequestId: string) {
    const client = await getAdmissionRequestByIdOrThrow(admissionRequestId);
    const clientEmail = client.personalData.email;

    const meetingLink = await getMeetingLink({
      summary: 'Home Visit',
      start: slot.startTime.toISOString(),
      end: slot.endTime.toISOString(),
      attendees: [clientEmail],
      userId: slot.user,
    });
    return meetingLink;
  }

  async createAppointment(
    slot: ISlots,
    payload: {
      admissionRequestId: string;
      meetingLink: string;
      source?: string;
      entity?: mongoose.Types.ObjectId;
    }
  ) {
    try {
      const appointmentPayload = {
        title: 'Home Visit',
        admissionRequestId: payload.admissionRequestId,
        date: slot.date,
        startTime: slot.startTime.toISOString(),
        endTime: slot.endTime.toISOString(),
        duration: this.getDateDiffInMinutes(new Date(slot.endTime), new Date(slot.startTime)),
        user: slot.user,
        status: AppointmentStatus.scheduled,
        description: 'Home visit appointment',
        slot: slot._id,
        meetingLink: payload.meetingLink,
        statusHistory: [
          {
            status: AppointmentStatus.scheduled,
            date: new Date(),
            actionBy: payload.entity,
            source: payload.source,
          },
        ],
      };
      const appointment = await Appointment.create(appointmentPayload);
      const appointmentAdapter = AppointmentMongoAdapter(appointment);
      const appointments = this.convertAppointmentsTo12Hours(appointmentAdapter as any);
      const serialized = AppointmentSerializer(appointments);

      const hubspotBtach = await HubspotBatch.findOne({ requestId: payload.admissionRequestId });
      if (hubspotBtach && hubspotBtach.dealId) {
        await dealUpdate({
          dealId: hubspotBtach.dealId,
          properties: {
            dealstage: 'decisionmakerboughtin',
          },
        });
      }
      return serialized;
    } catch (error) {
      throw error;
    }
  }

  async updateExistingAppointment(
    existingAppointment: IAppointment,
    slot: ISlots,
    appointmentPayload: {
      source?: string;
      entity?: mongoose.Types.ObjectId;
    }
  ) {
    try {
      const { entity, source } = appointmentPayload;
      const history = {
        status: AppointmentStatus.rescheduled,
        date: new Date(),
        actionBy: entity,
        source: source,
      };

      existingAppointment.rescheduleDates.push({
        previousScheduledDate: existingAppointment.date,
        rescheduleDate: new Date(),
      });
      existingAppointment.statusHistory.push(history);

      existingAppointment.set({
        slot: slot._id,
        date: slot.date,
        startTime: slot.startTime.toISOString(),
        endTime: slot.endTime.toISOString(),
        rescheduleCount: existingAppointment.rescheduleCount + 1,
        user: slot.user,
        status: AppointmentStatus.scheduled,
      });
      await existingAppointment.save();

      const appointmentAdapter = AppointmentMongoAdapter(existingAppointment);
      const appointments = this.convertAppointmentsTo12Hours(appointmentAdapter as any);
      const serialized = AppointmentSerializer(appointments);
      return serialized;
    } catch (error) {
      throw error;
    }
  }

  async getAppointmentsByUserId(userId: string, selectedUsersIds: Array<string>) {
    try {
      const user = await User.findOne({ _id: userId });
      if (!user) {
        throw new Error('User not found');
      }

      const userPermissionSet = await PermissionSet.findOne({
        role: user.role,
        area: user.area,
      }).lean();

      if (!userPermissionSet || Object.keys(userPermissionSet).length === 0) {
        throw new Error('Permission set not found for user');
      }

      const doesUserHavePermissionToViewAllAppointments = userPermissionSet.permissions.some(
        (permission: any) =>
          permission.section === 'calendar' &&
          permission.subSection === 'calendarPreview' &&
          permission.capability === 'viewAllCalendar'
      );

      // const isUserInPreSelectedHomeVisitorsHeads = preSelectedHomeVisitorsHeads.includes(user.email);
      if (doesUserHavePermissionToViewAllAppointments) {
        let query: any = {
          status: {
            $ne: AppointmentStatus.canceled,
          },
        };
        if (selectedUsersIds.length > 0) {
          query.user = { $in: selectedUsersIds };
        }
        const events = await Appointment.find(query).populate(
          'user',
          'name email homeVisitor homeVisitorColor'
        );
        logger.info(
          `[getAppointmentsByUserId] - user: ${userId} is querying appointment of these home visitors ids ${JSON.stringify(
            selectedUsersIds
          )}. Total appointments found: ${events.length}`
        );

        const eventsAdapter = events.map((appointment) => ({
          id: appointment._id.toString(),
          user: appointment.user,
          startTime: appointment.startTime.toISOString(),
          endTime: appointment.endTime.toISOString(),
          status: appointment.status,
          description: appointment.description,
          admissionRequestId: appointment.admissionRequestId,
          title: appointment.title,
          date: appointment.date.toISOString(),
          slot: appointment.slot.toString(),
          meetingLink: appointment.meetingLink,
        }));
        const serialized = AppointmentSerializer(eventsAdapter);
        return serialized;
      }

      const schedule = await Schedule.findOne({ user: userId });
      if (!schedule) throw new Error('Schedule not found');
      const events = await Appointment.find({
        user: userId,
        status: {
          $ne: AppointmentStatus.canceled,
        },
      }).populate('user', 'name email homeVisitor homeVisitorColor');
      const eventsAdapter = events.map((appointment) => ({
        id: appointment._id.toString(),
        user: appointment.user,
        startTime: appointment.startTime.toISOString(),
        endTime: appointment.endTime.toISOString(),
        status: appointment.status,
        description: appointment.description,
        admissionRequestId: appointment.admissionRequestId,
        title: appointment.title,
        date: appointment.date.toISOString(),
        slot: appointment.slot.toString(),
        meetingLink: appointment.meetingLink,
      }));

      const serialized = AppointmentSerializer(eventsAdapter);
      return serialized;
    } catch (error: any) {
      logger.error(`[getEvents] - error occured while fetching events for user: ${userId}`, {
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async statusChangeAppointment(appointmentPayload: {
    id: string;
    status: keyof typeof AppointmentStatus;
    source?: string;
    entity?: mongoose.Types.ObjectId;
  }) {
    try {
      const { id, status, entity } = appointmentPayload;
      const appointment = await Appointment.findById(id);
      if (!appointment) throw new AppointmentNotFoundException({});
      appointment.set({ status: status });
      await appointment.save();

      if (status === AppointmentStatus.completed) {
        await notifyCustomerAboutHomeVisitAppointmentFinish(
          appointment.admissionRequestId as unknown as string
        );
      }

      if (status === AppointmentStatus.noshow) {
        const history = {
          status: AppointmentStatus.noshow,
          date: new Date(),
          actionBy: entity,
          source: HomeVisitSchedulingActionSource.admin_portal,
        };
        appointment.statusHistory.push(history);

        appointment.noShow.count += 1;
        appointment.noShow.dates.push(new Date());

        await appointment.save();
        const hubspotBtach = await HubspotBatch.findOne({ requestId: appointment.admissionRequestId });
        if (hubspotBtach && hubspotBtach.dealId) {
          await dealUpdate({
            dealId: hubspotBtach.dealId,
            properties: {
              dealstage: '1016482881',
            },
          });
        }
        await notifyCustomerAboutHomeVisitAppointmentNoShow(
          appointment.admissionRequestId as unknown as string
        );
      }

      logger.info(
        `[statusChangeAppointment] - successfully status changed for appointment: ${id} to ${status}`
      );
      const eventsAdapter = AppointmentMongoAdapter(appointment);
      const serialized = AppointmentSerializer(eventsAdapter);
      return serialized;
    } catch (error: any) {
      logger.error(
        `[finishEvent] - error occured while finishing event for appointment: ${appointmentPayload.id}`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async homeVisitorChangeApointment(appointmentPayload: {
    slotId: string;
    homeVisitorId: string;
    appointmentId: string;
    userId: string | undefined;
  }) {
    try {
      const { slotId, homeVisitorId, appointmentId, userId } = appointmentPayload;
      const appointment = await Appointment.findById(appointmentId);
      if (!appointment) throw new AppointmentNotFoundException({});

      if (homeVisitorId === NO_HOME_VISITOR_AVAILABLE) {
        await notifyCustomerAboutHomeVisitAppointmentApology(
          appointment.admissionRequestId as unknown as string
        );
        const eventsAdapter = AppointmentMongoAdapter(appointment);
        const serialized = AppointmentSerializer(eventsAdapter);
        return serialized;
      }

      const newSlot = await Slots.findById(slotId);
      if (!newSlot) throw new SlotNotFoundException({});

      const previousSlot = await Slots.findById(appointment.slot);
      if (previousSlot) {
        previousSlot.set({ isAvailable: true });
        await previousSlot.save();
      }

      appointment.previousHomeVisitors.push({
        homeVisitorId: appointment.user,
        changeDate: new Date(),
        changeBy: userId! as any,
      });
      appointment.set({
        user: homeVisitorId,
        slot: slotId,
      });
      appointment.statusHistory.push({
        status: AppointmentStatus.reassigned,
        date: new Date(),
        actionBy: userId! as any,
        source: HomeVisitSchedulingActionSource.admin_portal,
      });
      await appointment.save();

      newSlot.set({ isAvailable: false });
      await newSlot.save();

      logger.info(
        `[statusChangeAppointment] - successfully updated home visitor for appointment: ${appointmentId} to homeVisitor ${homeVisitorId}`
      );
      const eventsAdapter = AppointmentMongoAdapter(appointment);
      const serialized = AppointmentSerializer(eventsAdapter);
      return serialized;
    } catch (error: any) {
      logger.error(
        `[finishEvent] - error occured while updating home visitor for appointment: ${appointmentPayload.appointmentId}`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async getAppointment(requestId: string) {
    try {
      const appointment = await Appointment.findOne({
        admissionRequestId: requestId,
      })
        .populate('user', 'name email')
        .populate({
          path: 'statusHistory.actionBy',
          select: 'name email',
          options: { lean: true }, // Returns plain JS objects
        });

      if (!appointment) throw new AppointmentNotFoundException({});
      const appointmentAdapter = AppointmentMongoAdapter(appointment, true);
      const serialized = AppointmentSerializer(appointmentAdapter);
      return serialized;
    } catch (error: any) {
      logger.error(`[getAppointment] - error occured while fetching appointment for request: ${requestId}`, {
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async getAppointmentForDriverWebApp(requestId: string, clientTimezone: string) {
    try {
      const appointment = await Appointment.findOne({
        admissionRequestId: requestId,
      });
      if (!appointment) throw new AppointmentNotFoundException({});
      const appointmentAdapter = AppointmentMongoAdapter(appointment);
      const appointmentWithClientTimezone = this.convertAppointmentsToClientTimezone(
        appointmentAdapter,
        clientTimezone
      );
      const serialized = AppointmentSerializer(appointmentWithClientTimezone);
      return serialized;
    } catch (error: any) {
      logger.error(
        `[getAppointmentForDriverWebApp] - error occured while fetching appointment for request: ${requestId}`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async cancelAppointment(cancelAppointmentPayload: {
    id: string;
    admissionRequestId: string;
    slot: string;
  }) {
    try {
      const { id, slot, admissionRequestId } = cancelAppointmentPayload;

      const appointment = await Appointment.findById(id);
      if (!appointment) throw new AppointmentNotFoundException({});

      const slotToFree = await Slots.findById(slot);
      if (!slotToFree) throw new SlotNotFoundException({});

      appointment.canceled.count += 1;
      appointment.canceled.dates.push(new Date());
      appointment.set({ status: AppointmentStatus.canceled });
      await appointment.save();

      slotToFree.set({ isAvailable: true });
      await slotToFree.save();

      await notifyCustomerAboutHomeVisitAppointmentCancel(admissionRequestId);
      const appointmentAdapter = AppointmentMongoAdapter(appointment);
      const appointmentWithClientTimezone = this.convertAppointmentsTo12Hours(appointmentAdapter);
      const serialized = AppointmentSerializer(appointmentWithClientTimezone);
      return serialized;
    } catch (error: any) {
      logger.error(
        `[cancelAppointment] - error occured while cancelling appointment for request: ${cancelAppointmentPayload.admissionRequestId}`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async checkUserFutureAppointment(userId: string) {
    try {
      const today = DateTime.now().toUTC().toISODate();
      const appointment = await Appointment.find({
        user: userId,
        status: AppointmentStatus.scheduled,
        date: {
          $gte: today,
        },
      });
      if (appointment.length === 0) {
        return false;
      }
      return true;
    } catch (error: any) {
      logger.error(`[checkUserAppointment] - error occured while checking appointment for user: ${userId}`, {
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async deleteUserFutureAvailableSlots(userId: string) {
    try {
      const today = DateTime.now().toUTC().toISODate();

      const deletedSlots = await Slots.deleteMany({
        date: {
          $gte: today,
        },
        user: userId,
        isAvailable: true,
      });

      logger.info(
        `[deleteUserFutureAvailableSlots] - deleted slots for user: ${userId}, slots deleted: ${deletedSlots.deletedCount}`
      );
    } catch (error: any) {
      logger.error(
        `[deleteUserFutureAvailableSlots] - error occured while deleting slots for user: ${userId}`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async updateSchedule(schedule: Record<string, any>, scheduleId: string) {
    try {
      const existingSchedule = await Schedule.findOne({ _id: scheduleId });
      if (!existingSchedule) {
        throw new Error('Schedule not found');
      }
      existingSchedule.set(schedule);
      await existingSchedule.save();
    } catch (error) {
      console.log('error: ', error);
      throw error;
    }
  }

  async getUsersWithSameAvailableSlot(slotId: string) {
    try {
      logger.info(
        `[getUsersWithSameAvailableSlot] - fetching users with same available slot for slot: ${slotId}`
      );
      const existingSlot = await Slots.findOne({
        _id: slotId,
      });
      if (!existingSlot) throw new SlotNotFoundException({});

      const targetDate = DateTime.fromJSDate(existingSlot.startTime).set({ millisecond: 0 });
      const nextSecond = targetDate.plus({ second: 1 });

      const usersWithWithSameAvailableSlot = await Slots.find({
        date: existingSlot.date,
        startTime: {
          $gte: targetDate.toJSDate(),
          $lt: nextSecond.toJSDate(),
        },
        isAvailable: true,
        $or: [{ isBlocked: false }, { isBlocked: { $exists: false } }],
      })
        .select('user date startTime endTime isAvailable')
        .populate('user', 'name email');

      const users = usersWithWithSameAvailableSlot.map((slot) => {
        const slotOwner = slot.user as any;
        return {
          id: slot._id,
          user: {
            id: slotOwner?._id,
            name: slotOwner?.name,
            email: slotOwner?.email,
          },
          date: slot.date,
          startTime: slot.startTime,
          endTime: slot.endTime,
          isAvailable: slot.isAvailable,
        };
      });

      logger.info(
        `[getUsersWithSameAvailableSlot] - Users with same available slot: ${users.length}, for slot: ${slotId}`
      );
      const serialized = UsersWithAvailableSlotSerializer(users);
      return serialized;
    } catch (error: any) {
      logger.error(
        `[getUsersWithSameAvailableSlot] - error occured  while fetching Users with same available slot`,
        {
          message: error.message,
          stack: error.stack,
        }
      );
      throw error;
    }
  }

  async createSlots(slots: Array<Record<string, any>>) {
    try {
      await Slots.create(slots);
    } catch (error) {
      console.log('error: ', error);
      throw error;
    }
  }

  async checkSlotAvailability(slotId: string) {
    const slot = await Slots.findById(slotId).populate('scheduleId');
    if (!slot) throw new SlotNotFoundException({});
    if (!slot.isAvailable) throw new SlotIsNotAvailableException({});

    const startTime = DateTime.fromJSDate(slot.startTime);
    const localDateNow = DateTime.now();
    const timeDifference = startTime.diff(localDateNow, 'minutes').minutes;

    if (slot.scheduleId === null) {
      throw new ScheduleNotFoundException({
        message: 'Schedule not found for this slot, Please select another slot or contact support',
      });
    }
    /**
     * this is being instructed by Product Manager
     * that user can book slot after 30 minutes
     */

    if (timeDifference < minBookingNoticeMinutes) {
      throw new SlotBookingTimeLimitException({
        message: `Please select a time after ${minBookingNoticeMinutes} minutes`,
      });
    }
    return slot;
  }

  convertSlotsToUserTimezone(slots: Array<any>, userTimezone: string) {
    return slots.map((slot) => {
      const { id, date, startTime, endTime, isAvailable, timezone } = slot;
      const userStartTime = DateTime.fromISO(startTime, { zone: timezone })
        .setZone(userTimezone)
        .toFormat('hh:mm a');
      const userEndTime = DateTime.fromISO(endTime, { zone: timezone })
        .setZone(userTimezone)
        .toFormat('hh:mm a');
      return {
        id,
        date,
        startTime: userStartTime,
        endTime: userEndTime,
        isAvailable,
        timezone: userTimezone,
      };
    });
  }

  convertAppointmentsTo12Hours(appointments: any | Array<any>) {
    if (Array.isArray(appointments)) {
      return appointments.map((appointment) => {
        const { startTime, endTime } = appointment;
        const appointmentStartTime = DateTime.fromISO(startTime).toFormat('hh:mm a');
        const appointmentEndTime = DateTime.fromISO(endTime).toFormat('hh:mm a');
        return {
          ...appointment,
          startTime: appointmentStartTime,
          endTime: appointmentEndTime,
        };
      });
    }

    const { startTime, endTime } = appointments;
    const appointmentStartTime = DateTime.fromISO(startTime).toFormat('hh:mm a');
    const appointmentEndTime = DateTime.fromISO(endTime).toFormat('hh:mm a');
    return {
      ...appointments,
      startTime: appointmentStartTime,
      endTime: appointmentEndTime,
    };
  }

  convertAppointmentsToClientTimezone(appointments: any | Array<any>, clientTimezone: string) {
    if (Array.isArray(appointments)) {
      return appointments.map((appointment) => {
        const { startTime, endTime } = appointment;
        const appointmentStartTime = DateTime.fromISO(startTime, { zone: 'utc' })
          .setZone(clientTimezone) // Convert to Karachi time
          .toFormat('hh:mm a'); // Format it

        const appointmentEndTime = DateTime.fromISO(endTime, { zone: 'utc' })
          .setZone(clientTimezone)
          .toFormat('hh:mm a');

        return {
          ...appointment,
          startTime: appointmentStartTime,
          endTime: appointmentEndTime,
        };
      });
    }

    const { startTime, endTime } = appointments;

    const appointmentStartTime = DateTime.fromISO(startTime, { zone: 'utc' })
      .setZone(clientTimezone)
      .toFormat('hh:mm a');

    const appointmentEndTime = DateTime.fromISO(endTime, { zone: 'utc' })
      .setZone(clientTimezone)
      .toFormat('hh:mm a');
    return {
      ...appointments,
      startTime: appointmentStartTime,
      endTime: appointmentEndTime,
    };
  }

  addBusinessDays(startDate: DateTime, daysToCompute: number) {
    let addedDays = 0;
    let date = startDate;
    const noOfWorkingDaysInAWeek = 5;
    while (addedDays < daysToCompute) {
      date = date.plus({ days: 1 });
      if (date.weekday <= noOfWorkingDaysInAWeek) {
        addedDays++;
      }
    }
    return date;
  }

  // generateSlots({
  //   schedule,
  //   startDate,
  //   daysToCompute,
  //   considerBusinessDays = true,
  // }: {
  //   schedule: ISchedule;
  //   startDate: DateTime;
  //   daysToCompute: number;
  //   considerBusinessDays?: boolean;
  // }) {
  //   const slots = [];
  //   const endDate = considerBusinessDays
  //     ? this.addBusinessDays(startDate, daysToCompute)
  //     : startDate.plus({ days: daysToCompute });

  //   for (let currentDate = startDate; currentDate <= endDate; currentDate = currentDate.plus({ days: 1 })) {
  //     const dayKey = this.getDayKey(currentDate);
  //     const dailySchedule = schedule.weeklySchedule[dayKey];

  //     if (dailySchedule && dailySchedule.start && dailySchedule.end) {
  //       let slotTime = currentDate.set({
  //         hour: parseInt(dailySchedule.start.split(':')[0]),
  //         minute: parseInt(dailySchedule.start.split(':')[1]),
  //       });

  //       const endTime = currentDate.set({
  //         hour: parseInt(dailySchedule.end.split(':')[0]),
  //         minute: parseInt(dailySchedule.end.split(':')[1]),
  //       });

  //       const breakTime = schedule?.breakTimes?.[0];
  //       let breakStartTime: any;
  //       let breakEndTime: any;
  //       let breakDuration: any;

  //       if (breakTime) {
  //         breakStartTime = currentDate.set({
  //           hour: parseInt(breakTime.start.split(':')[0]),
  //           minute: parseInt(breakTime.start.split(':')[1]),
  //         });
  //         breakEndTime = currentDate.set({
  //           hour: parseInt(breakTime.end.split(':')[0]),
  //           minute: parseInt(breakTime.end.split(':')[1]),
  //         });
  //         breakDuration = breakEndTime.diff(breakStartTime, 'minutes').minutes;
  //       }

  //       while (slotTime < endTime) {
  //         if (breakTime && slotTime >= breakStartTime! && slotTime < breakEndTime!) {
  //           slotTime = slotTime.plus({ minutes: breakDuration });
  //           continue;
  //         }

  //         slots.push({
  //           user: schedule.user,
  //           date: currentDate.toISODate(),
  //           startTime: slotTime.toISO({ suppressMilliseconds: true }), // Remove milliseconds
  //           endTime: slotTime.plus({ minutes: schedule.duration }).toISO({ suppressMilliseconds: true }),
  //           isAvailable: true,
  //           maxAppointments: 1,
  //           currentAppointments: 0,
  //           timezone: schedule.timezone,
  //           scheduleId: schedule._id,
  //         });
  //         slotTime = slotTime.plus({ minutes: schedule.duration });
  //       }
  //     }
  //   }
  //   return slots;
  // }

  generateSlots({
    schedule,
    startDate,
    daysToCompute,
    considerBusinessDays = true,
  }: {
    schedule: ISchedule;
    startDate: DateTime;
    daysToCompute: number;
    considerBusinessDays?: boolean;
  }) {
    const slots = [];
    const endDate = considerBusinessDays
      ? this.addBusinessDays(startDate, daysToCompute)
      : startDate.plus({ days: daysToCompute });

    for (let currentDate = startDate; currentDate <= endDate; currentDate = currentDate.plus({ days: 1 })) {
      const dayKey = this.getDayKey(currentDate);
      const dailySchedule = schedule.weeklySchedule[dayKey];

      if (dailySchedule && dailySchedule.start && dailySchedule.end) {
        const slotTime = currentDate
          .setZone(schedule.timezone)
          .set({
            hour: parseInt(dailySchedule.start.split(':')[0]),
            minute: parseInt(dailySchedule.start.split(':')[1]),
          })
          .toUTC();

        const endTime = currentDate
          .setZone(schedule.timezone)
          .set({
            hour: parseInt(dailySchedule.end.split(':')[0]),
            minute: parseInt(dailySchedule.end.split(':')[1]),
          })
          .toUTC();

        const breakTime = schedule?.breakTimes?.[0];
        let breakStartTime: any;
        let breakEndTime: any;
        let breakDuration: any;

        if (breakTime) {
          breakStartTime = currentDate
            .setZone(schedule.timezone)
            .set({
              hour: parseInt(breakTime.start.split(':')[0]),
              minute: parseInt(breakTime.start.split(':')[1]),
            })
            .toUTC();

          breakEndTime = currentDate
            .setZone(schedule.timezone)
            .set({
              hour: parseInt(breakTime.end.split(':')[0]),
              minute: parseInt(breakTime.end.split(':')[1]),
            })
            .toUTC();
          breakDuration = breakEndTime.diff(breakStartTime, 'minutes').minutes;
        }

        let currentSlotTime = slotTime;

        while (currentSlotTime < endTime) {
          if (breakTime && currentSlotTime >= breakStartTime! && currentSlotTime < breakEndTime!) {
            currentSlotTime = currentSlotTime.plus({ minutes: breakDuration });
            continue;
          }

          slots.push({
            user: schedule.user,
            date: currentDate.toISODate(),
            startTime: currentSlotTime.toISO({ suppressMilliseconds: true }), // Remove milliseconds
            endTime: currentSlotTime
              .plus({ minutes: schedule.duration })
              .toISO({ suppressMilliseconds: true }),
            isAvailable: true,
            maxAppointments: 1,
            currentAppointments: 0,
            timezone: schedule.timezone,
            scheduleId: schedule._id,
            isBlocked: false,
          });

          currentSlotTime = currentSlotTime.plus({ minutes: schedule.duration });
        }
      }
    }
    return slots;
  }

  getDateDiffInMinutes(startDate: Date, endDate: Date) {
    const noOfMillisecondsInOneSecond = 1000;
    const noOfSecondsInOneMinute = 60;
    return (endDate.getTime() - startDate.getTime()) / (noOfMillisecondsInOneSecond * noOfSecondsInOneMinute);
  }

  getDayKey(dateTime: DateTime) {
    const day = dateTime.weekday;
    const days: Record<number, any> = {
      1: 'monday',
      2: 'tuesday',
      3: 'wednesday',
      4: 'thursday',
      5: 'friday',
      6: 'saturday',
      7: 'sunday',
    };
    return days[day] || 'monday';
  }

  convertScheduleTo12Hours(weeklySchedule: Record<string, { start: string; end: string }>) {
    const convertedSchedule: Record<string, { start: string; end: string }> = {};

    for (const [day, timeRange] of Object.entries(weeklySchedule)) {
      const { start, end } = timeRange;

      // Parse the 24-hour format times
      const startTime = DateTime.fromFormat(start, 'HH:mm');
      const endTime = DateTime.fromFormat(end, 'HH:mm');

      // Convert to 12-hour format with am/pm
      convertedSchedule[day] = {
        start: startTime.toFormat('hh:mma').toLowerCase(),
        end: endTime.toFormat('hh:mma').toLowerCase(),
      };
    }

    return convertedSchedule;
  }

  convertBreakScheduleTo12Hours(breakSchedule: Array<{ start: string; end: string }>) {
    const convertedSchedule: Array<{ start: string; end: string }> = [];
    for (const timeRange of breakSchedule) {
      const { start, end } = timeRange;

      const startTime = DateTime.fromFormat(start, 'HH:mm');
      const endTime = DateTime.fromFormat(end, 'HH:mm');
      const breakSlot = {
        start: startTime.toFormat('hh:mma').toLowerCase(),
        end: endTime.toFormat('hh:mma').toLowerCase(),
      };
      convertedSchedule.push(breakSlot);
    }
    return convertedSchedule;
  }

  convertBreakScheduleTo24Hours(breakSchedule: Array<{ start: string; end: string }>) {
    const convertedSchedule: Array<{ start: string; end: string }> = [];
    for (const timeRange of breakSchedule) {
      const { start, end } = timeRange;

      const startTime = DateTime.fromFormat(start, 'hh:mma');
      const endTime = DateTime.fromFormat(end, 'hh:mma');
      const breakSlot = {
        start: startTime.toFormat('HH:mm'),
        end: endTime.toFormat('HH:mm'),
      };
      convertedSchedule.push(breakSlot);
    }
    return convertedSchedule;
  }

  convertScheduleTo24Hours(schedule: Record<string, { start: string; end: string }>) {
    const convertedSchedule: Record<string, { start: string; end: string }> = {};

    for (const [day, timeRange] of Object.entries(schedule)) {
      const { start, end } = timeRange;

      const startTime = DateTime.fromFormat(start, 'hh:mma');
      const endTime = DateTime.fromFormat(end, 'hh:mma');

      convertedSchedule[day] = {
        start: startTime.toFormat('HH:mm'),
        end: endTime.toFormat('HH:mm'),
      };
    }

    return convertedSchedule;
  }

  async notifyCustomersAboutTheirAppointmentOneNightAgoCron() {
    try {
      if (!RUN_HOME_VISIT_APPOINTMENTS_REMINDER_CRON) {
        logger.info(`[notifyCustomersAboutTheirAppointmentOneNightAgoCron] - cron job is disabled`);
        return;
      }

      logger.info(
        '[notifyCustomersAboutTheirAppointmentOneNightAgoCron] - running cron job to send reminders for appointments one night before'
      );

      const now = DateTime.now().set({ millisecond: 0 });
      const tomorrowDate = now.plus({ days: 1 }).toISODate();

      const appointments = await Appointment.find({
        date: tomorrowDate,
        status: AppointmentStatus.scheduled,
      });
      logger.info(
        `[notifyCustomersAboutTheirAppointmentOneNightAgoCron] - total appointments found ${appointments.length} to send reminders one night before`
      );

      await Promise.all(
        appointments.map(async (appointment) => {
          try {
            await notifyCustomerAboutHomeVisitAppointmentOneNightAgo(
              appointment.admissionRequestId as unknown as string,
              {
                meetingLink: appointment.meetingLink!,
                date: appointment.date.toISOString(),
                startTime: DateTime.fromJSDate(appointment.startTime).toFormat('hh:mm a'),
              }
            );
          } catch (error: any) {
            logger.error(
              `[notifyCustomersAboutTheirAppointmentOneNightAgoCron] - error occured while sending reminder for appointment: ${appointment._id}`,
              {
                message: error.message,
                stack: error.stack,
              }
            );
          }
        })
      );
    } catch (error: any) {
      logger.error(
        '[notifyCustomersAboutTheirAppointmentOneNightAgoCron] - error occured while sending reminders one night before',
        {
          message: error.message,
          stack: error.stack,
        }
      );
    }
  }

  async notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore() {
    try {
      if (!RUN_HOME_VISIT_APPOINTMENTS_REMINDER_CRON) {
        logger.info(`[notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore] - cron job is disabled`);
        return;
      }

      logger.info(
        '[notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore] - running cron job to send reminders for appointments one hour before'
      );
      const now = DateTime.utc().set({ millisecond: 0 });
      const todayDate = now.toISODate();

      const appointments = await Appointment.find({
        date: todayDate,
        status: AppointmentStatus.scheduled,
        startTime: {
          $gte: now.plus({ minutes: 5 }).toJSDate(),
          $lte: now.plus({ minutes: 10 }).toJSDate(),
        },
      });
      logger.info(
        `[notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore] - total appointments found ${appointments.length} to send reminders one hour before`
      );

      await Promise.all(
        appointments.map(async (appointment) => {
          try {
            await notifyCustomerAboutHomeVisitAppointmentAboutFiveMinutesBefore(
              appointment.admissionRequestId as unknown as string,
              {
                meetingLink: appointment.meetingLink!,
                date: appointment.date.toISOString(),
                startTime: DateTime.fromJSDate(appointment.startTime).toFormat('hh:mm a'),
              }
            );
          } catch (error: any) {
            logger.error(
              `[notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore] - error occured while sending reminder for appointment: ${appointment._id}`,
              {
                message: error.message,
                stack: error.stack,
              }
            );
          }
        })
      );
    } catch (error: any) {
      logger.error(
        '[notifyCustomersAboutTheirAppointmentAlmostFiveMinutesBefore] - error occured while sending reminders one hour before',
        {
          message: error.message,
          stack: error.stack,
        }
      );
    }
  }

  async addSlotsOfNextTwoWeeksForHomeVisitosCron() {
    try {
      /**
       * cron job code for adding next two weeks slots.
       */
      if (!RUN_HOME_VISITORS_ADD_SLOTS_CRON) {
        logger.info(`[addSlotsOfNextTwoWeeksForHomeVisitosCron] - cron job is disabled`);
        return;
      }
      logger.info(
        '[addSlotsOfNextTwoWeeksForHomeVisitosCron] - Running cron job to add slots for the next two weeks'
      );

      const homeVisitors = await User.find({ homeVisitor: true });
      logger.info(
        `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - Total home visitors found: ${homeVisitors.length}`
      );

      await Promise.all(
        homeVisitors.map(async (homeVisitor) => {
          try {
            const homeVisitorSchedule = await Schedule.findOne({ user: homeVisitor._id });
            if (!homeVisitorSchedule) {
              logger.warn(
                `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - Schedule not found for home visitor: ${homeVisitor._id}. Skipping adding slots`
              );
              return;
            }

            const startDate = DateTime.utc();
            logger.info(`[addSlotsOfNextTwoWeeksForHomeVisitosCron] - startDate: ${startDate}`);

            const slots = await Slots.find({
              user: homeVisitor._id,
              date: { $gt: startDate.toJSDate() },
            }).sort({ date: -1 });

            const advanceDateWhichAlreadyHasSlots =
              slots.length > 0 ? DateTime.fromJSDate(slots[0].date, { zone: 'utc' }) : startDate;
            logger.info(
              `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - advanceDateWhichAlreadyHasSlots: ${advanceDateWhichAlreadyHasSlots}`
            );

            const TWO_WEEKS_DAYS = 14;
            const noOfDaysToAddSlots = advanceDateWhichAlreadyHasSlots.diff(startDate, 'days').days;

            logger.info(
              `[addSlotsOfNextTwoWeeksForHomeVisitosCron] -  for user ${homeVisitor._id},  noOfDaysToAddSlots: ${noOfDaysToAddSlots}`
            );

            if (noOfDaysToAddSlots >= TWO_WEEKS_DAYS) return;
            const totalNoOfDaysToAddSlots = TWO_WEEKS_DAYS - noOfDaysToAddSlots;

            logger.info(
              `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - totalNoOfDaysToAddSlots: ${totalNoOfDaysToAddSlots}`
            );

            const newStartDateToAddSlotsFrom = advanceDateWhichAlreadyHasSlots.plus({ days: 1 });

            logger.info(
              `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - for user ${homeVisitor._id}, newStartDateToAddSlotsFrom: ${newStartDateToAddSlotsFrom}`
            );
            const slotsStartDate = newStartDateToAddSlotsFrom
              .set({
                hour: 9,
                minute: 0,
                second: 0,
              })
              .setZone(homeVisitorSchedule.timezone)
              .toUTC();

            const generatedSlots = this.generateSlots({
              schedule: homeVisitorSchedule,
              startDate: slotsStartDate,
              daysToCompute: totalNoOfDaysToAddSlots,
              considerBusinessDays: false,
            });
            logger.info(
              `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - for user ${homeVisitor._id}, generatedSlots.length: ${generatedSlots.length}`
            );

            await this.createSlots(generatedSlots);
            logger.info(
              `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - Successfully slots added for home visitor: ${
                homeVisitor._id
              }, first slot date: ${generatedSlots.length > 0 ? generatedSlots[0].date : ''}`
            );
          } catch (error: any) {
            logger.error(
              `[addSlotsOfNextTwoWeeksForHomeVisitosCron] - Error while adding slots for home visitor: ${homeVisitor._id}`,
              { message: error.message, stack: error.stack }
            );
          }
        })
      );
    } catch (error: any) {
      logger.error(
        '[addSlotsOfNextTwoWeeksForHomeVisitosCron] - error occured while adding slots for next two weeks',
        {
          message: error.message,
          stack: error.stack,
        }
      );
    }
  }
}

export const calendarService = new Calendar();
