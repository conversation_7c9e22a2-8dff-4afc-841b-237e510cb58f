/* eslint-disable import/no-extraneous-dependencies */
import axios from 'axios';
import { PDFDocument, PDFPage } from 'pdf-lib';
import { AsyncController } from '../../../types&interfaces/types';
import Associate from '../../../models/associateSchema';
import { Readable } from 'stream';
import { removeEmptySpacesNameFile, removeEmptySpacesNameFileV2 } from '../../../services/removeEmptySpaces';
import StockVehicle from '../../../models/StockVehicleSchema';
import Document from '../../../models/documentSchema';
import { uploadFileReadable } from '../../../aws/s3';
import { SignDocsKeys } from '../../../controllers/associate';
import { getCurrentDateTime } from '../../../services/timestamps';
import { Types } from 'mongoose';
import WeetrustWebhook from './model';
import { CountriesEnum } from '../../../constants';
import { documentCompletedForAdendum, documentSignEventForAdendum } from './services/weetrust.service';
import { getDocumentById } from '@/modules/Associate/services/weetrust';
import { getFileFromUrl } from '@/changes';

export const receiveCompletedDocument: AsyncController = async (req, res) => {
  try {
    // console.log('[START] Receive Completed Document Weetrust Webhook', req.body);
    console.log('-------------------------------------------------------------------------------------');

    // Check if there is already an existing WeetrustWebhook record, if not, create it
    let weetrust = await WeetrustWebhook.findOne({
      'body.Document.documentID': req.body.Document.documentID,
      'body.type': 'completedDocument',
    });

    if (!weetrust) {
      weetrust = new WeetrustWebhook({ body: req.body });
      await weetrust.save();
    }

    const { Document: Data } = req.body;
    const isAdendum = Data?.title.toLowerCase().includes('adendum');
    console.log('isAdendum', isAdendum);
    if (isAdendum) {
      console.log('Processing adendum completed document');
      await documentCompletedForAdendum(Data, weetrust);
      return res.status(200).send({ message: 'Completed' });
    }

    const url = req.body?.Document?.documentFileObj?.url;
    const splitChildDocumentId = req.body.Document.splitChildDocumentId as string | undefined;
    const { documentID, documentFileObj } = req.body?.Document;

    if (!url) return res.status(400).json({ message: 'Missing fields' });

    const associate = await Associate.findOne(
      { 'digitalSignature.documentID': documentID },
      {
        vehiclesId: 1,
        email: 1,
        digitalSignature: 1,
        signDocs: 1,
        firstName: 1,
        lastName: 1,
        country: 1,
      }
    );
    // console.log('associate', associate);

    if (associate?.digitalSignature.documentID !== documentID) {
      console.log('Document ID does not match');
      console.log('RECEIVED DOCUMENT WEETRUST WEBHOOK', '-------------------');
      return res.status(404).json({ message: 'Document ID does not match' });
    }

    if (!associate) {
      console.log('Associate not found');
      console.log('RECEIVED DOCUMENT WEETRUST WEBHOOK', '-------------------');
      return res.status(404).json({ message: 'Associate not found' });
    }

    const stockId = associate.vehiclesId[associate.vehiclesId.length - 1];

    const stockVehicle = await StockVehicle.findById(stockId, {
      carNumber: 1,
      updateHistory: 1,
      digitalSignature: 1,
      step: 1,
    });

    if (!stockVehicle) {
      console.log('Stock Vehicle not found');
      console.log('RECEIVED DOCUMENT WEETRUST WEBHOOK', req.body, '-------------------');
      return res.status(404).json({ message: 'Stock Vehicle not found' });
    }

    // get the weetrust document by id
    const response = await getDocumentById(documentID);
    if (!response) {
      console.log('Document not found');
      console.log('RECEIVED DOCUMENT WEETRUST WEBHOOK', req.body, '-------------------');
      return res.status(404).json({ message: 'Document not found' });
    }

    // console.log('====================================================================');
    // console.log('RESPONSE URL', response.responseData.documentFileObj.url);
    // console.log('====================================================================');

    const urlUpdated = response.responseData.documentFileObj.url;

    const file = await axios.get(urlUpdated, { responseType: 'arraybuffer' });
    // console.log('file downloaded', file.data);
    const pdfDoc = await PDFDocument.load(file.data);

    // let registryConsentPage: PDFPage | null = null;
    let pdfDocRegistry: PDFDocument | null = null;
    // if (Data.documentURL) {
    //   const documentURL = Data.documentURL;
    //   const doc = await axios.get(documentURL, { responseType: 'arraybuffer' });

    if (urlUpdated) {
      const doc = await axios.get(urlUpdated, { responseType: 'arraybuffer' });

      // get last page of this document to add it to the contract and promissory note
      pdfDocRegistry = await PDFDocument.load(doc.data);
      // eslint-disable-next-line prettier/prettier, comma-dangle, max-len
      // const registryConsentPages = await pdfDocRegistry.copyPages(pdfDocRegistry, [pdfDocRegistry.getPageCount() - 1,]);
      // registryConsentPage = registryConsentPages[0];
    }

    // const totalPages = pdfDoc.getPageCount();f

    if (associate.country === CountriesEnum['United States']) {
      const pageRanges = [
        // Page 1-15 (índices 0-14)
        { start: 0, end: 14, name: '1-15 y 19', filename: documentFileObj.key, fieldname: 'contract' },
      ];

      for (const range of pageRanges) {
        const newPdfDoc = await PDFDocument.create();
        let pages: PDFPage[];

        pages = await newPdfDoc.copyPages(
          pdfDoc,
          Array.from({ length: range.end - range.start + 1 }, (_, i) => i + range.start)
        );

        pages.forEach((page) => newPdfDoc.addPage(page));
        const pdfBytes = Buffer.from(await newPdfDoc.save());
        const pdfStream = Readable.from(pdfBytes);
        const fileMock = {
          originalname: range.filename + '.pdf',
        } as Express.Multer.File;
        const removeSpacesFileName = removeEmptySpacesNameFile(fileMock);
        const documentPath = `associate/${stockVehicle.carNumber}/${associate.email}/${range.fieldname}/${removeSpacesFileName}`;
        const document = new Document({
          originalName: removeSpacesFileName,
          path: documentPath,
          associateId: associate,
          vehicleId: stockVehicle._id,
        });
        await document.save();
        await uploadFileReadable(pdfStream, documentPath);

        const field = range.fieldname as SignDocsKeys;

        if (!associate.signDocs) associate.signDocs = {};

        if (associate.signDocs) {
          associate.signDocs[field] = document._id;
        }
      }
    } else {
      const pageRangesForContract = [
        { start: 0, end: 10, name: '1-11 y 19', filename: documentFileObj.key, fieldname: 'contract' }, // Páginas 1-11 (índices 0-10)
        {
          start: 11,
          end: 11,
          name: '12',
          filename: 'Acta de Entrega Voluntaria',
          fieldname: 'deliveryReceipt',
        }, // Página 12
        { start: 12, end: 13, name: '13-14', filename: 'Garantia', fieldname: 'warranty' }, // Páginas 19-20
        { start: 14, end: 14, name: '15', filename: 'Aviso de Privacidad', fieldname: 'privacy' }, // Página 21
        { start: 15, end: 15, name: '16', filename: 'Uso de Factura', fieldname: 'invoice' }, // Página 22
        { start: 16, end: 16, name: '17', filename: 'Datos de Contacto', fieldname: 'contactInfo' }, // Página 23
        // { start: 17, end: 17, name: '18', filename: 'Pagaré', fieldname: 'promissoryNote' }, // Página 24
      ];

      for (const range of pageRangesForContract) {
        const newPdfDoc = await PDFDocument.create();
        // console.log('newPdfDoc', newPdfDoc);
        // console.log('---------------------------------------');
        let pages: PDFPage[];

        if (range.fieldname === 'contract' || range.fieldname === 'promissoryNote') {
          pages = await newPdfDoc.copyPages(
            pdfDoc,
            Array.from({ length: range.end - range.start + 1 }, (_, i) => i + range.start)
          );
          if (pdfDocRegistry) {
            const registryConsentPages = await newPdfDoc.copyPages(
              pdfDocRegistry,
              Array.from({ length: pdfDocRegistry.getPageCount() }, (_, i) => i)
            );

            pages.push(registryConsentPages.slice(-1)[0]);
          }
        } else {
          pages = await newPdfDoc.copyPages(
            pdfDoc,
            Array.from({ length: range.end - range.start + 1 }, (_, i) => i + range.start)
          );
        }
        // console.log('---------------------------------------', 'pages step');
        pages.forEach((page) => newPdfDoc.addPage(page));

        // const pdfBytes = await newPdfDoc.save();
        const pdfBytes = Buffer.from(await newPdfDoc.save());

        // console.log('---------------------------------------', 'pdfBytes step');

        // Convertir el buffer en un ReadableStream
        const pdfStream = Readable.from(pdfBytes);

        // console.log('---------------------------------------', 'pdfStream step');

        const fileMock = {
          originalname: range.filename + '.pdf',
        } as Express.Multer.File;

        const removeSpacesFileName = removeEmptySpacesNameFile(fileMock);
        // console.log('removeSpacesFileName', removeSpacesFileName);
        // console.log('---------------------------------------', 'removeSpacesFileName step');
        const documentPath = `associate/${stockVehicle.carNumber}/${associate.email}/${range.fieldname}/${removeSpacesFileName}`;

        /*  */
        // const localFilePath = path.join(__dirname, removeSpacesFileName); // Asegúrate de que la ruta esté correcta para tu proyecto
        // fs.writeFileSync(localFilePath, pdfBytes);
        // console.log(`File saved locally at ${localFilePath}`);
        /*  */

        const document = new Document({
          originalName: removeSpacesFileName,
          path: documentPath,
          associateId: associate,
          vehicleId: stockVehicle._id,
        });
        // console.log('---------------------------------------', 'save document step');
        await document.save();

        await uploadFileReadable(pdfStream, documentPath);

        const field = range.fieldname as SignDocsKeys;

        if (!associate.signDocs) associate.signDocs = {};

        if (associate.signDocs) {
          associate.signDocs[field] = document._id;
        }
      }

      // Promisory note now comes in splitChildDocumentId
      if (splitChildDocumentId) {
        const promissoryNote = await getDocumentById(splitChildDocumentId);
        // do the same as above but uploading the complete file to s3 and saving the document in the db with "promissoryNote" as fieldname
        if (!promissoryNote) {
          console.log('no promissoryNote', stockVehicle.carNumber);
          return res.status(404).json({ message: 'Promissory note not found' });
        }

        // create filename using associate fullname and vehicle contract number

        const contractNumber = stockVehicle.extensionCarNumber
          ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
          : stockVehicle.carNumber;
        const associateName = `${associate.firstName} ${associate.lastName}`;
        const fileName = `${associateName}-${contractNumber}-pagare.pdf`;

        const removeSpacesFileName = removeEmptySpacesNameFileV2(fileName);
        const documentPath = `associate/${stockVehicle.carNumber}/${associate.email}/promissoryNote/${removeSpacesFileName}`;
        const document = new Document({
          originalName: removeSpacesFileName,
          path: documentPath,
          associateId: associate,
          vehicleId: stockVehicle._id,
        });
        await document.save();

        const promissoryNoteFileUrl = await getFileFromUrl(promissoryNote.responseData.documentFileObj.url);

        const pdfStream = Readable.from(promissoryNoteFileUrl);

        await uploadFileReadable(pdfStream, documentPath);

        if (!associate.signDocs) associate.signDocs = {};

        if (associate.signDocs) {
          associate.signDocs.promissoryNote = document._id;
        } else {
          associate.signDocs = {
            promissoryNote: document._id,
          };
        }
      }
    }

    stockVehicle.updateHistory.push({
      step: 'Documentos firmados agregados automaticamente',
      time: getCurrentDateTime(),
      // userId: new Types.ObjectId('647f69776a98b82801ddcc45'), // ocn user prod
      // userId: new Types.ObjectId('64f9fca5d0a417208d2b8e78'), // dev
      userId: process.env.PAYMENTS_API_URL?.includes('develop')
        ? new Types.ObjectId('64f9fca5d0a417208d2b8e78')
        : new Types.ObjectId('647f69776a98b82801ddcc45'),
    });

    // stockVehicle.step.stepName = steps.delivered.name;
    // stockVehicle.step.stepNumber = steps.delivered.number;

    await stockVehicle.save();
    // await associate.save();

    associate.digitalSignature.url = url;
    associate.digitalSignature.signed = true;
    associate.digitalSignature.promissoryNoteDocID = Data.splitChildDocumentId;

    await Associate.updateOne(
      { _id: associate._id },
      {
        $set: {
          signDocs: associate.signDocs,
          digitalSignature: associate.digitalSignature,
        },
      }
    );

    weetrust.associateId = associate._id;
    await weetrust.save();

    console.log('[START] Receive Completed Document Weetrust Webhook');
    console.log('-------------------------------------------------------------------------------------');
    return res.status(200).send({
      message: 'Completed',
    });
  } catch (error: any) {
    console.log('full error', error);
    console.log('error', error.message || error.response?.data);
    return res.status(500).json({ message: 'Something went wrong' });
  }
};

/**
 * This controller is to handle the event of a document being signed by every participant in the contract
 * for example, if it has 3 participants, this event will trigger when every participant has signed the document
 */
export const receiveDocumentSignEvent: AsyncController = async (req, res) => {
  try {
    console.log('[START] Receive Document Sign By Participant', req.body);
    // const path = require('path');
    // const fs = require('fs');

    // const json = JSON.stringify(req.body, null, 2);
    // const randomId = Math.floor(Math.random() * 1000);
    // const namefile = `signed-${randomId}.json`;
    // fs.writeFileSync(path.join(process.cwd(), namefile), json);

    let weetrust = await WeetrustWebhook.findOne({
      'body.Document.documentID': req.body.Document.documentID,
      'body.type': 'completedDocument',
    });

    if (!weetrust) {
      weetrust = new WeetrustWebhook({ body: req.body });
      await weetrust.save();
    }

    const { Document: Data } = req.body;

    if (Data.title.toLowerCase().includes('adendum')) {
      await documentSignEventForAdendum(Data, weetrust);
      return res.status(200).send({ message: 'Document signed by participant' });
    }

    const documentID = Data._id || Data?.documentID;

    const signatory = Data?.signatory;

    if (!signatory) return res.status(400).json({ message: 'Missing fields' });

    const associate = await Associate.findOne({ 'digitalSignature.documentID': documentID })
      .select({
        email: 1,
      })
      .select('+digitalSignature')
      .lean();
    // console.log('associate', associate);
    if (!associate || !associate.digitalSignature?.documentID)
      return res.status(404).json({ message: 'Associate not found' });

    const alreadySigned = signatory.filter((s: any) => s.isSigned);

    for (const signatoryParticipant of alreadySigned) {
      const participant = associate.digitalSignature.participants.find(
        (p: any) => p.email === signatoryParticipant.emailID
      );

      if (participant) {
        participant.signed = true;
      }
    }

    await Associate.updateOne(
      { _id: associate._id },
      {
        $set: {
          'digitalSignature.participants': associate.digitalSignature.participants,
        },
      }
    );

    weetrust.associateId = associate._id;
    await weetrust.save();

    console.log('[END] Receive Document Sign By Participant');
    return res.status(200).send({
      message: 'Document signed by participant',
    });
  } catch (error: any) {
    console.log('full error', error);
    return res.status(500).json({ message: 'Something went wrong' });
  }
};

export const retryProcessAdendumDocumentCompleted: AsyncController = async (req, res) => {
  try {
    const { documentID, associateId } = req.body;

    let weetrust = await WeetrustWebhook.findOne({
      $or: [
        {
          'body.Document.documentID': documentID,
          associateId,
        },
        {
          associateId,
        },
        {
          'body.Document.documentID': documentID,
        },
      ],
    }).sort({ createdAt: -1 });
    if (!weetrust) return res.status(404).json({ message: 'Data not found' });

    await documentCompletedForAdendum(weetrust.body.Document, weetrust, true);

    return res.status(200).send({ message: 'Completed' });
  } catch (error: any) {
    console.log('full error', error);
    return res.status(500).json({ message: 'Something went wrong' });
  }
};
