export const createAdmissionRequestPayload = {
  firstName: 'Test Admission Request User',
  lastName: 'sdsdm',
  phone: '+523*********',
  email: '<EMAIL>',
  state: 'cdmx',
  city: 'cdmx',
};

export const admissionRequestResponse = {
  success: true,
  data: {
    id: '67738e4449f20af48711bdda',
    status: 'created',
    rejectionReason: null,
    personalData: {
      status: 'pending',
      firstName: createAdmissionRequestPayload.firstName,
      lastName: createAdmissionRequestPayload.lastName,
      phone: createAdmissionRequestPayload.phone,
      email: createAdmissionRequestPayload.email,
      birthdate: null,
      taxId: null,
      nationalId: null,
      postalCode: null,
      city: 'cdmx',
      state: 'cdmx',
      neighborhood: null,
      street: null,
      streetNumber: null,
      department: null,
      country: 'mx',
      ssn: null,
      rideShareTotalRides: null,
      avgEarningPerWeek: null,
      termsAndConditions: null,
      dataPrivacyConsentForm: null,
      nationality: null,
      age: null,
      occupation: null,
      homePhone: null,
      timeInResidency: null,
      municipality: null,
      maritalStatus: 'Single',
      dependents: null,
      spouseOrPartnerIncome: null,
      partnerSourceOfIncome: null,
      partnerName: null,
      partnerPhone: null,
      noOfDependents: null,
      dependendsInfo: null,
      ownACar: null,
      carLeasingtime: null,
      carMake: null,
      carModel: null,
      ownDebt: null,
      outStandingDebt: null,
      doesDebtAffectPersonalFinance: null,
      references: {
        reference1Address: '',
        reference1Name: '',
        reference1Phone: '',
        reference1Relationship: '',
        reference2Address: '',
        reference2Name: '',
        reference2Phone: '',
        reference2Relationship: '',
        reference3Address: '',
        reference3Name: '',
        reference3Phone: '',
        reference3Relationship: '',
      },
      learnAboutOcn: null,
    },
    documentsAnalysis: {
      status: 'pending',
      documents: [
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'identity_card_front',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'identity_card_back',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'proof_of_address',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'bank_statement_month_1',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'bank_statement_month_2',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'bank_statement_month_3',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'proof_of_tax_situation',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'drivers_license_front',
        },
        {
          media: null,
          mediaId: null,
          status: 'pending',
          type: 'drivers_license_back',
        },
        {
          mediaId: null,
          status: 'pending',
          media: null,
          type: 'selfie_photo',
        },
        {
          mediaId: null,
          status: 'pending',
          media: null,
          type: 'garage_photo',
        },
        {
          mediaId: null,
          status: 'pending',
          media: null,
          type: 'solidarity_obligor_identity_card_front',
        },
        {
          mediaId: null,
          status: 'pending',
          media: null,
          type: 'solidarity_obligor_identity_card_back',
        },
      ],
    },
    palenca: {
      widgetId: '04d4f458-9991-4c44-9464-8786cb9e046e',
      externalId: '67738e4449f20af48711bdd9',
      accounts: [],
    },
    homeVisit: null,
    earningsAnalysis: { status: 'pending', earnings: [] },
    riskAnalysis: { status: 'pending', scorecardVersion: 'v1', scorecard: null },
    createdAt: '2024-12-31T06:25:08.498Z',
    updatedAt: '2024-12-31T06:25:08.498Z',
    screenshots: [],
    source: null,
    clientIpAddress: null,
  },
  error: null,
  pagination: null,
};

export const homeVisitPersonalInformationPayload = {
  personalData: {
    firstName: 'Test',
    lastName: 'Test',
    nationality: 'Mexico',
    birthdate: '2001-05-01',
    age: 23,
    occupation: '',
    avgEarningPerWeek: 8000,
  },
  homeVisitData: {
    visitDate: '2024-12-31',
    visitTime: '',
    homeVisitStepsStatus: {
      personal: 'incomplete',
    },
  },
  isPersonalData: true,
  isHomeVisitData: true,
};

export const homeVisitPersonalInformationResponse = {
  status: 'created',
  rejectionReason: null,
  personalData: {
    status: 'pending',
    firstName: 'Test',
    lastName: 'Test',
    phone: '+523*********',
    email: '<EMAIL>',
    birthdate: '2001-05-01',
    taxId: null,
    nationalId: null,
    postalCode: null,
    city: 'cdmx',
    state: 'cdmx',
    neighborhood: null,
    street: null,
    streetNumber: null,
    department: null,
    country: 'mx',
    ssn: null,
    rideShareTotalRides: null,
    avgEarningPerWeek: 8000,
    termsAndConditions: null,
    dataPrivacyConsentForm: null,
    nationality: 'Mexico',
    age: 23,
    occupation: null,
    homePhone: null,
    timeInResidency: null,
    municipality: null,
    maritalStatus: 'Single',
    dependents: null,
    spouseOrPartnerIncome: null,
    partnerSourceOfIncome: null,
    partnerName: null,
    partnerPhone: null,
    noOfDependents: null,
    dependendsInfo: [],
    ownACar: null,
    carLeasingtime: null,
    carMake: null,
    carModel: null,
    ownDebt: null,
    outStandingDebt: null,
    doesDebtAffectPersonalFinance: null,
    references: {
      reference1Address: '',
      reference1Name: '',
      reference1Phone: '',
      reference1Relationship: '',
      reference2Address: '',
      reference2Name: '',
      reference2Phone: '',
      reference2Relationship: '',
      reference3Address: '',
      reference3Name: '',
      reference3Phone: '',
      reference3Relationship: '',
    },
    learnAboutOcn: null,
  },
  documentsAnalysis: { status: 'pending', documents: [Array] },
  palenca: {
    widgetId: '04d4f458-9991-4c44-9464-8786cb9e046e',
    externalId: '67738e4449f20af48711bdd9',
    accounts: [],
  },
  homeVisit: {
    residentOwnershipStatus: '',
    comments: '',
    images: [],
    status: 'pending',
    visitDate: '2024-12-31T00:00:00.000Z',
    media: null,
    visitTime: null,
    houseInformation: {
      audioSystem: '',
      dinningRoom: '',
      kitchen: '',
      livingRoom: '',
      nameOfOwner: '',
      noOfBedrooms: 0,
      ownProperty: '',
      ownerPhone: '',
      ownerRelative: '',
      ownerRelativeRelation: '',
      refrigerator: '',
      stove: '',
      television: '',
      typeOfHousing: 'Apartment',
      washingMachine: '',
    },
    proofOfPropertyOwnership: [],
    visitorEmailAddress: '',
    doesProofOfAddressMatchLocation: '',
    characteristicsOfGarage: '',
    behaviourOfCustomerDuringCall: '',
    homeVisitStepsStatus: {
      personal: 'incomplete',
    },
    reasonOfRejection: '',
    statusReason: '',
    suggestedStatus: '',
  },
  earningsAnalysis: { status: 'pending', earnings: [] },
  riskAnalysis: { status: 'pending', scorecardVersion: 'v1', scorecard: null },
  createdAt: '2024-12-31T09:15:57.469Z',
  updatedAt: '2024-12-31T09:15:57.603Z',
  screenshots: [],
  source: null,
  clientIpAddress: null,
};

export const homeVisitContactInformationPayload = {
  personalData: {
    phone: '+522343760404',
    homePhone: '+524344888888',
    email: '<EMAIL>',
  },
  homeVisitData: {
    homeVisitStepsStatus: {
      personal: 'incomplete',
      contact: 'complete',
      address: '',
      family: '',
      property: '',
      automobile: '',
      debt: '',
      references: '',
      outcome: '',
    },
  },
  isPersonalData: true,
  isHomeVisitData: true,
};

export const homeVisitContactInformationResponse = {
  status: 'created',
  rejectionReason: null,
  personalData: {
    status: 'pending',
    firstName: 'Test Admission Request User',
    lastName: 'sdsdm',
    phone: '+522343760404',
    email: '<EMAIL>',
    birthdate: null,
    taxId: null,
    nationalId: null,
    postalCode: null,
    city: 'cdmx',
    state: 'cdmx',
    neighborhood: null,
    street: null,
    streetNumber: null,
    department: null,
    country: 'mx',
    ssn: null,
    rideShareTotalRides: null,
    avgEarningPerWeek: null,
    termsAndConditions: null,
    dataPrivacyConsentForm: null,
    nationality: null,
    age: null,
    occupation: null,
    homePhone: '+524344888888',
    timeInResidency: null,
    municipality: null,
    maritalStatus: 'Single',
    dependents: null,
    spouseOrPartnerIncome: null,
    partnerSourceOfIncome: null,
    partnerName: null,
    partnerPhone: null,
    noOfDependents: null,
    dependendsInfo: [],
    ownACar: null,
    carLeasingtime: null,
    carMake: null,
    carModel: null,
    ownDebt: null,
    outStandingDebt: null,
    doesDebtAffectPersonalFinance: null,
    references: {
      reference1Address: '',
      reference1Name: '',
      reference1Phone: '',
      reference1Relationship: '',
      reference2Address: '',
      reference2Name: '',
      reference2Phone: '',
      reference2Relationship: '',
      reference3Address: '',
      reference3Name: '',
      reference3Phone: '',
      reference3Relationship: '',
    },
    learnAboutOcn: null,
  },
  documentsAnalysis: { status: 'pending', documents: [Array] },
  palenca: {
    widgetId: '04d4f458-9991-4c44-9464-8786cb9e046e',
    externalId: '67738e4449f20af48711bdd9',
    accounts: [],
  },
  homeVisit: {
    residentOwnershipStatus: '',
    comments: '',
    images: [],
    status: 'pending',
    visitDate: '2024-12-31T00:00:00.000Z',
    media: null,
    visitTime: null,
    houseInformation: {
      audioSystem: '',
      dinningRoom: '',
      kitchen: '',
      livingRoom: '',
      nameOfOwner: '',
      noOfBedrooms: 0,
      ownProperty: '',
      ownerPhone: '',
      ownerRelative: '',
      ownerRelativeRelation: '',
      refrigerator: '',
      stove: '',
      television: '',
      typeOfHousing: 'Apartment',
      washingMachine: '',
    },
    proofOfPropertyOwnership: [],
    visitorEmailAddress: '',
    doesProofOfAddressMatchLocation: '',
    characteristicsOfGarage: '',
    behaviourOfCustomerDuringCall: '',
    homeVisitStepsStatus: {
      personal: 'incomplete',
      contact: 'complete',
      address: '',
      family: '',
      property: '',
      automobile: '',
      debt: '',
      references: '',
      outcome: '',
    },
  },
  earningsAnalysis: { status: 'pending', earnings: [] },
  riskAnalysis: { status: 'pending', scorecardVersion: 'v1', scorecard: null },
  createdAt: '2024-12-31T09:15:57.469Z',
  updatedAt: '2024-12-31T09:15:57.603Z',
  screenshots: [],
  source: null,
  clientIpAddress: null,
};

export const homeVisitAddressInformationPayload = {
  personalData: {
    streetNumber: 'sdf',
    department: 'apartment 23',
    street: 'Muer',
    postalCode: '45000',
    timeInResidency: '2 times',
    neighborhood: 'house no 12',
    municipality: 'nopi',
    state: 'Aguascalientes',
  },
  homeVisitData: {
    homeVisitStepsStatus: {
      personal: 'complete',
      contact: 'complete',
      address: 'complete',
      family: '',
      property: '',
      automobile: '',
      debt: '',
      references: '',
      outcome: '',
    },
  },
  isPersonalData: true,
  isHomeVisitData: true,
};

export const homeVisitFamilyInformationPayload = {
  personalData: {
    maritalStatus: 'Single',
    dependents: 'No',
    spouseOrPartnerIncome: 'Not-Selected',
    partnerSourceOfIncome: '',
    partnerName: '',
    partnerPhone: '',
    noOfDependents: 0,
    dependendsInfo: [],
  },
  homeVisitData: {
    homeVisitStepsStatus: {
      personal: 'complete',
      contact: 'complete',
      address: 'complete',
      family: 'complete',
      property: '',
      automobile: '',
      debt: '',
      references: '',
      outcome: '',
    },
  },
  isPersonalData: true,
  isHomeVisitData: true,
};

export const homeVisitPropertyInformationPayload = {
  homeVisitData: {
    houseInformation: {
      ownProperty: 'No',
      nameOfOwner: 'chaki',
      ownerRelative: 'No',
      ownerRelativeRelation: '',
      ownerPhone: '6756897865',
      typeOfHousing: 'Apartment',
      noOfBedrooms: 2,
      livingRoom: 'Yes',
      dinningRoom: 'No',
      kitchen: 'Yes',
      television: 'Yes',
      audioSystem: 'No',
      stove: 'Yes',
      refrigerator: 'Yes',
      washingMachine: 'No',
    },
    images: [],
    proofOfPropertyOwnership: [],
    homeVisitStepsStatus: {
      personal: 'complete',
      contact: 'complete',
      address: 'complete',
      family: 'complete',
      property: 'incomplete',
      automobile: '',
      debt: '',
      references: '',
      outcome: '',
    },
  },
  isHomeVisitData: true,
};

export const homeVisitAutoMobileInformationPayload = {
  personalData: {
    ownACar: 'Yes',
    carLeasingtime: '',
    carMake: 'jhgsajfd',
    carModel: 'test-5329',
    doesItApplyToElectricCars: 'Yes',
  },
  homeVisitData: {
    homeVisitStepsStatus: {
      personal: 'complete',
      contact: 'complete',
      address: 'complete',
      family: 'complete',
      property: 'incomplete',
      automobile: 'complete',
      debt: '',
      references: '',
      outcome: '',
    },
  },
  isPersonalData: true,
  isHomeVisitData: true,
};

export const homeVisitDebtInformationPayload = {
  personalData: {
    ownDebt: 'No',
    outStandingDebt: '',
    doesDebtAffectPersonalFinance: 'Not-Selected',
  },
  homeVisitData: {
    homeVisitStepsStatus: {
      personal: 'complete',
      contact: 'complete',
      address: 'complete',
      family: 'complete',
      property: 'incomplete',
      automobile: 'complete',
      debt: 'complete',
      references: '',
      outcome: '',
    },
  },
  isPersonalData: true,
  isHomeVisitData: true,
};

export const homeVisitReferencesInformationPayload = {
  personalData: {
    references: {
      reference1Name: 'reference 1',
      reference1Phone: '3178123777',
      reference1Relationship: 'cousin',
      reference1Address: 'cousin-adress',
      reference2Name: 'reference 2',
      reference2Phone: '3178123487',
      reference2Relationship: 'cousin 2',
      reference2Address: 'cousin-adresss',
      reference3Name: 'reference 3',
      reference3Phone: '2881234871',
      reference3Relationship: 'cousin 3',
      reference3Address: 'cousin 3',
    },
    learnAboutOcn: 'testing',
  },
  homeVisitData: {
    homeVisitStepsStatus: {
      personal: 'complete',
      contact: 'complete',
      address: 'complete',
      family: 'complete',
      property: 'incomplete',
      automobile: 'complete',
      debt: 'complete',
      references: 'complete',
      outcome: '',
    },
  },
  isPersonalData: true,
  isHomeVisitData: true,
};

export const homeVisitOutcomeInformationPayload = {
  homeVisitData: {
    visitDate: '2024-12-31',
    visitTime: '10:00',
    visitorEmailAddress: '<EMAIL>',
    doesProofOfAddressMatchLocation: 'Yes',
    characteristicsOfGarage: 'really good',
    behaviourOfCustomerDuringCall: 'great',
    houseInformation: {
      ownProperty: 'No',
      nameOfOwner: 'chaki',
      ownerRelative: 'No',
      ownerRelativeRelation: '',
      ownerPhone: '6756897865',
      typeOfHousing: 'Apartment',
      noOfBedrooms: 2,
      livingRoom: 'Yes',
      dinningRoom: 'No',
      kitchen: 'Yes',
      television: 'Yes',
      audioSystem: 'No',
      stove: 'Yes',
      refrigerator: 'Yes',
      washingMachine: 'No',
    },
    comments: 'this is ',
    homeVisitStepsStatus: {
      personal: 'complete',
      contact: 'complete',
      address: 'complete',
      family: 'complete',
      property: 'complete',
      automobile: 'complete',
      debt: 'complete',
      references: 'complete',
      outcome: 'complete',
    },
    status: 'approved',
  },
  isHomeVisitData: true,
};
