import { HUBSPOT_TOKEN } from '../../constants';
import {
  HUBSPOT_URL,
  HUBSPOT_DEAL_URL,
  HUBSPOT_DEAL_TOKEN,
  HUBSPOT_BATCH_URL,
} from '../../constants/onboarding';
import axios from 'axios';
import { logger } from '../../clean/lib/logger';
import HubspotBatch from '../../models/hubspotBatch';
import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';

export async function changeProperties({ hubspotId, properties }: { hubspotId: string; properties: object }) {
  logger.info(`[changeProperties] Update HubspotId ${hubspotId} status to ${JSON.stringify(properties)}`);
  try {
    await axios.patch(
      `${HUBSPOT_URL}/${hubspotId}`,
      { properties },
      {
        headers: {
          Authorization: `Bearer ${HUBSPOT_TOKEN}`,
        },
      }
    );
  } catch (error: any) {
    logger.error(
      `[changeProperties] Error updating HubspotId ${hubspotId} status to ${JSON.stringify(
        properties
      )}, error: ${error}`
    );
    if (error.response.data.message.includes('already exists')) throw new Error('existingEmail');
    throw new Error('genericError');
  }
}

export async function createDeal({
  dealname,
  ciudad_deal = 'CDMX/EDOMEX',
  dealstage = 'contractsent',
}: {
  dealname: string;
  ciudad_deal?: string;
  dealstage?: string;
}) {
  logger.info(`[createDeal] Create deal with properties ${dealname}, ${ciudad_deal}`);

  const properties = {
    dealname,
    pipeline: 'default',
    ciudad_deal,
    dealstage,
    closedate: new Date(new Date().setMonth(new Date().getMonth() + 2)).toISOString(),
  };

  try {
    const response = await axios.post(
      `${HUBSPOT_DEAL_URL}`,
      { properties },
      {
        headers: {
          Authorization: `Bearer ${HUBSPOT_DEAL_TOKEN}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    logger.error(
      `[createDeal] Error creating deal with properties ${JSON.stringify(properties)}, error: ${error}`
    );
    throw new Error('genericError');
  }
}

export async function dealUpdate({ dealId, properties }: { dealId: string; properties: object }) {
  logger.info(`[dealUpdate] Update dealId ${dealId} status to ${JSON.stringify(properties)}`);
  try {
    await axios.patch(
      `${HUBSPOT_DEAL_URL}/${dealId}`,
      { properties },
      {
        headers: {
          Authorization: `Bearer ${HUBSPOT_DEAL_TOKEN}`,
        },
      }
    );
  } catch (error: any) {
    logger.error(
      `[dealUpdate] Error updating dealId ${dealId} status to ${JSON.stringify(properties)}, error: ${error}`
    );
    throw new Error('genericError');
  }
}

export async function batchDealAndLead({
  dealId,
  hubspotId,
  requestId,
}: {
  dealId: string;
  hubspotId: string;
  requestId: string;
}) {
  logger.info(`[batchDealAndLead] Associate dealId ${dealId} with hubspotId ${hubspotId}`);
  try {
    await HubspotBatch.create({ requestId, hubspotId, dealId });
  } catch (error) {
    console.log(error);
    logger.error(`[batchDealAndLead] Error saving batch with dealId ${dealId} and hubspotId ${hubspotId}`);
    throw new Error('genericError');
  }
  try {
    await axios.post(
      `${HUBSPOT_BATCH_URL}`,
      {
        inputs: [
          {
            from: { id: dealId },
            to: { id: hubspotId },
            type: 'deal_to_contact',
          },
        ],
      },
      {
        headers: {
          Authorization: `Bearer ${HUBSPOT_DEAL_TOKEN}`,
        },
      }
    );
  } catch (error: any) {
    console.error(error);
    logger.error(
      `[batchDealAndLead] Error associating dealId ${dealId} with hubspotId ${hubspotId}, error: ${error}`
    );
    throw new Error('genericError');
  }
}

export async function fileVerification({
  dealId,
  documents,
  requestId,
}: {
  dealId: string;
  documents: any[];
  requestId: string;
}) {
  logger.info(`[fileVerification] Verifying documents ${documents}`);

  const ine =
    documents.find((doc) => doc.type === 'identity_card_front' && doc.mediaId) &&
    documents.find((doc) => doc.type === 'identity_card_back' && doc.mediaId);

  const proofOfAddressApproved = documents.find((doc) => doc.type === 'proof_of_address' && doc.mediaId);

  const monthsBankStatementsApproved =
    documents.find((doc) => doc.type === 'bank_statement_month_1' && doc.mediaId) &&
    documents.find((doc) => doc.type === 'bank_statement_month_2' && doc.mediaId) &&
    documents.find((doc) => doc.type === 'bank_statement_month_3' && doc.mediaId);

  if (ine && proofOfAddressApproved && monthsBankStatementsApproved) {
    await dealUpdate({ dealId, properties: { dealstage: 'qualifiedtobuy' } });
    await AdmissionRequestMongo.findByIdAndUpdate({ _id: requestId }, { firstBlockVerifier: true });
    return true;
  }
  return false;
}
