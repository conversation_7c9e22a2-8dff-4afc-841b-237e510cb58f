import { Router } from 'express';
import {
  createOriginalString,
  orderRegister,
  conciliation,
  getAllConciliations,
  findConciliationsByDate,
  checkAccountBalance,
  createAccount,
  saveChangeState,
  saveChangePayment,
} from '../controllers/stp';
import { verifyToken, verifyExternalToken } from '../middlewares/verifyToken';

const router = Router();

router.post('/change-stp-state', verifyExternalToken, saveChangeState);
router.post('/change-stp-payment', verifyExternalToken, saveChangePayment);
router.post('/order-register', verifyToken, orderRegister);
router.post('/create-original-string', verifyToken, createOriginalString);
router.post('/conciliation', verifyToken, conciliation);
router.post('/conciliation/by-date', verifyToken, findConciliationsByDate);
router.get('/conciliation/send', verifyToken, getAllConciliations);
router.get('/conciliation/received', verifyToken, getAllConciliations);
router.get('/account/balance', verifyToken, checkAccountBalance);
router.post('/account/create', verifyToken, createAccount);

export default router;
