import { Types } from 'mongoose';
import FleetOrderSLAAlert, { IFleetOrderSLAAlert, SLAAlertType } from '../models/fleet-order-sla-alerts.model';
import FleetOrder, { FleetOrderStatus } from '../models/fleet-order.model';
import { calculateSLAStatus, getDeadlineForStatus, calculateSLADates } from '../utils/sla-calculator';

export class SLAMonitoringService {
  
  /**
   * Verificar y crear alertas de SLA para todas las órdenes activas
   */
  async checkAndCreateSLAAlerts(): Promise<IFleetOrderSLAAlert[]> {
    const activeOrders = await FleetOrder.find({
      status: { 
        $in: [
          FleetOrderStatus.CREATED,
          FleetOrderStatus.SENT,
          FleetOrderStatus.DISPERSION,
          FleetOrderStatus.INVOICE_LETTER_REQUEST
        ]
      }
    }).lean();

    const newAlerts: IFleetOrderSLAAlert[] = [];

    for (const order of activeOrders) {
      const alerts = await this.checkOrderSLA(order);
      newAlerts.push(...alerts);
    }

    return newAlerts;
  }

  /**
   * Verificar SLA para una orden específica
   */
  async checkOrderSLA(order: any): Promise<IFleetOrderSLAAlert[]> {
    const slaDate = calculateSLADates(order.year, order.month);
    const slaStatus = calculateSLAStatus(order.status, slaDate);
    
    if (!slaStatus.deadline) {
      return [];
    }

    const newAlerts: IFleetOrderSLAAlert[] = [];

    // Verificar si necesita alerta de advertencia (2 días antes)
    if (slaStatus.isNear && !slaStatus.isExceeded) {
      const existingWarning = await FleetOrderSLAAlert.findOne({
        orderId: order._id,
        alertType: SLAAlertType.SLA_WARNING,
        status: order.status,
      });

      if (!existingWarning) {
        const warningAlert = await this.createSLAAlert({
          orderId: order._id,
          orderNumber: order.orderNumber,
          alertType: SLAAlertType.SLA_WARNING,
          status: order.status,
          deadline: slaStatus.deadline,
          daysRemaining: slaStatus.daysRemaining,
        });
        newAlerts.push(warningAlert);
      }
    }

    // Verificar si necesita alerta de exceso
    if (slaStatus.isExceeded) {
      const existingExceeded = await FleetOrderSLAAlert.findOne({
        orderId: order._id,
        alertType: SLAAlertType.SLA_EXCEEDED,
        status: order.status,
      });

      if (!existingExceeded) {
        const exceededAlert = await this.createSLAAlert({
          orderId: order._id,
          orderNumber: order.orderNumber,
          alertType: SLAAlertType.SLA_EXCEEDED,
          status: order.status,
          deadline: slaStatus.deadline,
          daysRemaining: slaStatus.daysRemaining,
        });
        newAlerts.push(exceededAlert);
      }
    }

    return newAlerts;
  }

  /**
   * Crear una nueva alerta de SLA
   */
  async createSLAAlert(data: {
    orderId: Types.ObjectId;
    orderNumber: string;
    alertType: SLAAlertType;
    status: FleetOrderStatus;
    deadline: Date;
    daysRemaining: number;
  }): Promise<IFleetOrderSLAAlert> {
    const alert = new FleetOrderSLAAlert(data);
    return await alert.save();
  }

  /**
   * Obtener alertas pendientes (no resueltas)
   */
  async getPendingAlerts(): Promise<IFleetOrderSLAAlert[]> {
    return await FleetOrderSLAAlert.find({
      isResolved: false,
    })
    .populate('order', 'orderNumber month year status totalUnits totalAmount')
    .sort({ createdAt: -1 })
    .lean();
  }

  /**
   * Obtener alertas por tipo
   */
  async getAlertsByType(alertType: SLAAlertType): Promise<IFleetOrderSLAAlert[]> {
    return await FleetOrderSLAAlert.find({
      alertType,
      isResolved: false,
    })
    .populate('order', 'orderNumber month year status totalUnits totalAmount')
    .sort({ createdAt: -1 })
    .lean();
  }

  /**
   * Obtener alertas para una orden específica
   */
  async getAlertsForOrder(orderId: Types.ObjectId): Promise<IFleetOrderSLAAlert[]> {
    return await FleetOrderSLAAlert.find({
      orderId,
    })
    .sort({ createdAt: -1 })
    .lean();
  }

  /**
   * Marcar alerta como resuelta
   */
  async resolveAlert(alertId: Types.ObjectId): Promise<IFleetOrderSLAAlert | null> {
    return await FleetOrderSLAAlert.findByIdAndUpdate(
      alertId,
      { isResolved: true },
      { new: true }
    );
  }

  /**
   * Marcar todas las alertas de una orden como resueltas
   */
  async resolveAlertsForOrder(orderId: Types.ObjectId): Promise<number> {
    const result = await FleetOrderSLAAlert.updateMany(
      { orderId, isResolved: false },
      { isResolved: true }
    );
    
    return result.modifiedCount;
  }

  /**
   * Marcar alerta como enviada a Slack
   */
  async markAlertAsSentToSlack(
    alertId: Types.ObjectId, 
    slackMessageId?: string
  ): Promise<IFleetOrderSLAAlert | null> {
    return await FleetOrderSLAAlert.findByIdAndUpdate(
      alertId,
      { 
        sentToSlack: true,
        slackMessageId: slackMessageId || undefined,
      },
      { new: true }
    );
  }

  /**
   * Obtener alertas no enviadas a Slack
   */
  async getAlertsNotSentToSlack(): Promise<IFleetOrderSLAAlert[]> {
    return await FleetOrderSLAAlert.find({
      sentToSlack: false,
      isResolved: false,
    })
    .populate('order', 'orderNumber month year status totalUnits totalAmount')
    .sort({ createdAt: -1 })
    .lean();
  }

  /**
   * Obtener estadísticas de SLA
   */
  async getSLAStatistics(): Promise<{
    totalActiveOrders: number;
    ordersOnTime: number;
    ordersWithWarnings: number;
    ordersExceeded: number;
    alertsByStatus: { [key: string]: number };
  }> {
    const [
      totalActiveOrders,
      warningAlerts,
      exceededAlerts,
      alertsByStatus,
    ] = await Promise.all([
      FleetOrder.countDocuments({
        status: { 
          $in: [
            FleetOrderStatus.CREATED,
            FleetOrderStatus.SENT,
            FleetOrderStatus.DISPERSION,
            FleetOrderStatus.INVOICE_LETTER_REQUEST
          ]
        }
      }),
      FleetOrderSLAAlert.countDocuments({
        alertType: SLAAlertType.SLA_WARNING,
        isResolved: false,
      }),
      FleetOrderSLAAlert.countDocuments({
        alertType: SLAAlertType.SLA_EXCEEDED,
        isResolved: false,
      }),
      FleetOrderSLAAlert.aggregate([
        { $match: { isResolved: false } },
        { $group: { _id: '$status', count: { $sum: 1 } } },
      ]),
    ]);

    const alertsByStatusMap: { [key: string]: number } = {};
    alertsByStatus.forEach((item: any) => {
      alertsByStatusMap[item._id] = item.count;
    });

    return {
      totalActiveOrders,
      ordersOnTime: totalActiveOrders - warningAlerts - exceededAlerts,
      ordersWithWarnings: warningAlerts,
      ordersExceeded: exceededAlerts,
      alertsByStatus: alertsByStatusMap,
    };
  }

  /**
   * Limpiar alertas resueltas antiguas (más de 30 días)
   */
  async cleanupOldResolvedAlerts(): Promise<number> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const result = await FleetOrderSLAAlert.deleteMany({
      isResolved: true,
      updatedAt: { $lt: thirtyDaysAgo },
    });

    return result.deletedCount;
  }

  /**
   * Obtener resumen diario de SLA para reportes
   */
  async getDailySLASummary(): Promise<{
    date: Date;
    totalOrders: number;
    newWarnings: number;
    newExceeded: number;
    resolvedToday: number;
    criticalOrders: any[];
  }> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    const [
      totalOrders,
      newWarnings,
      newExceeded,
      resolvedToday,
      criticalOrders,
    ] = await Promise.all([
      FleetOrder.countDocuments({
        status: { 
          $in: [
            FleetOrderStatus.CREATED,
            FleetOrderStatus.SENT,
            FleetOrderStatus.DISPERSION,
            FleetOrderStatus.INVOICE_LETTER_REQUEST
          ]
        }
      }),
      FleetOrderSLAAlert.countDocuments({
        alertType: SLAAlertType.SLA_WARNING,
        createdAt: { $gte: startOfDay, $lt: endOfDay },
      }),
      FleetOrderSLAAlert.countDocuments({
        alertType: SLAAlertType.SLA_EXCEEDED,
        createdAt: { $gte: startOfDay, $lt: endOfDay },
      }),
      FleetOrderSLAAlert.countDocuments({
        isResolved: true,
        updatedAt: { $gte: startOfDay, $lt: endOfDay },
      }),
      FleetOrderSLAAlert.find({
        alertType: SLAAlertType.SLA_EXCEEDED,
        isResolved: false,
      })
      .populate('order', 'orderNumber month year status')
      .limit(5)
      .lean(),
    ]);

    return {
      date: today,
      totalOrders,
      newWarnings,
      newExceeded,
      resolvedToday,
      criticalOrders,
    };
  }
}
