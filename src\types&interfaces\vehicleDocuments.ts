// Central enum for document categories
export enum DocumentCategory {
  INSURANCE_POLICY = 'INSURANCE_POLICY',
  TENENCIA = 'TENENCIA',
  CIRCULATION_CARD_FRONT = 'CIRCULATION_CARD_FRONT',
  CIRCULATION_CARD_BACK = 'CIRCULATION_CARD_BACK',
  PLATES_ALTA_PLACAS = 'PLATES_ALTA_PLACAS',
  PLATES_FRONT = 'PLATES_FRONT',
  PLATES_BACK = 'PLATES_BACK',
  FACTURE = 'FACTURE',
}

export interface PresignedUrlRequestItem {
  fileName: string;
  contentType: string; // e.g., 'application/pdf', 'image/jpeg'
  documentCategory: DocumentCategory;
}

export interface PresignedUrlResponseItem {
  originalFileName: string;
  presignedUrl: string;
  s3Key: string;
  documentCategory: DocumentCategory;
  contentType: string;
}

export interface UploadedDocumentInfoForProcessing {
  s3Key: string;
  originalFileName: string;
  documentCategory: DocumentCategory;
  contentType: string;
}

export interface ProcessDocumentsRequest {
  documents: UploadedDocumentInfoForProcessing[];
  // documentCategory is now part of each document in the array
  userId: string;
  userName: string;
  userEmail: string;
  country?: string;
  region?: string;
  contractNumber?: string;
}

export interface VehicleDocumentBatchPayload extends ProcessDocumentsRequest {
  slackChannelId: string;
}

export type ProcessedDocumentStatus = 'success' | 'error' | 'skipped' | 'duplicate';

export interface ProcessedDocumentResult {
  originalFileName: string;
  s3Key: string;
  status: ProcessedDocumentStatus;
  vin?: string;
  plates?: string;
  vehicleCarNumber?: string;
  documentId?: string;
  error?: {
    type: string;
    message: string;
    technicalMessage?: string;
  };
}
