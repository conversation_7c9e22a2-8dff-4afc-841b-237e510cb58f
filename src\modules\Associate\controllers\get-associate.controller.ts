import jwt from 'jsonwebtoken';
import axios from 'axios';
import Associate from '../../../models/associateSchema';
import StockVehicle from '../../../models/StockVehicleSchema';
import { AsyncController } from '../../../types&interfaces/types';
import { PAYMENTS_API_URL } from '../../../constants/payments-api';
import { accessTokenSecret } from '../../../constants';
import { replaceDocWithUrl } from '@/services/getPropertyWithUrls';
import { logger } from '@/clean/lib/logger';

export const getAssociateByStockPlates: AsyncController = async (req, res) => {
  const { plates } = req.body;
  try {
    if (!plates && typeof plates !== 'string') {
      return res.status(400).json({ message: 'Missing plates' });
    }

    const stockVehicle = await StockVehicle.findOne({
      'carPlates.plates': plates.trim().toUpperCase(),
    });

    if (!stockVehicle) {
      return res.status(404).json({ message: 'Stock vehicle not found' });
    }

    const lastAssociateId = stockVehicle.drivers[stockVehicle.drivers.length - 1];
    const associate = await Associate.findById(lastAssociateId._id);

    if (!associate) {
      return res.status(404).json({ message: 'Associate not found' });
    }

    let lastPayment, last5Payments, subscription;
    try {
      const payments = await axios.get(`${PAYMENTS_API_URL}/payments?clientId=${associate.clientId}`, {
        headers: {
          Authorization: `Bearer ${process.env.PAYMENTS_API_KEY}`,
        },
      });

      if (payments.data.data.length > 0) {
        lastPayment = payments?.data?.data[0];
        last5Payments = payments?.data?.data.slice(-5);
        subscription = payments?.data?.data[0]?.subscription;
      } else {
        lastPayment = null;
        last5Payments = null;
        subscription = null;
      }
    } catch (error) {
      logger.error(`[getAssociateByStockPlates] Error fetching payments: ${error}`);
      lastPayment = null;
      last5Payments = null;
      subscription = null;
    }

    const data = {
      name: associate.firstName,
      phone: associate.phone,
      email: associate.email,
      clientId: associate.clientId,
      associateId: associate._id,
      curp: associate.curp,
      rfc: associate.rfc,
      carNumber: stockVehicle.carNumber,
      vehicleId: stockVehicle._id,
      brand: stockVehicle.brand,
      model: stockVehicle.model,
      bill: {
        amount: stockVehicle?.billAmount,
        file: stockVehicle?.bill ? await replaceDocWithUrl(stockVehicle.bill.toString()) : null,
      },
      insurance: await Promise.all(
        stockVehicle?.policiesArray.map(async (policy: any) => ({
          policyNumber: policy.policyNumber,
          insurer: policy.insurer,
          validity: policy.validity,
          broker: policy.broker,
          file: await replaceDocWithUrl(policy.policyDocument.toString()),
        }))
      ),
      tenancy: await Promise.all(
        stockVehicle?.tenancy.map(async (tenancy: any) => ({
          payment: tenancy.payment,
          validity: tenancy.validity,
          file: await replaceDocWithUrl(tenancy.tenancyDocument.toString()),
        }))
      ),
      circulationCard: {
        number: stockVehicle.circulationCard?.number,
        validity: stockVehicle.circulationCard?.validity,
        file: {
          frontImg:
            stockVehicle.circulationCard?.frontImg &&
            (await replaceDocWithUrl(stockVehicle.circulationCard.frontImg.toString())),
          backImg:
            stockVehicle?.circulationCard?.backImg &&
            (await replaceDocWithUrl(stockVehicle.circulationCard.backImg.toString())),
        },
      },
      digitalSignature: associate?.signDocs?.contract
        ? await replaceDocWithUrl(associate.signDocs.contract.toString())
        : null,
      payments: {
        subscription: {
          id: subscription?.id,
          isActive: subscription?.isActive,
        },
        lastPayment: {
          id: lastPayment?.id,
          total: lastPayment?.total,
          status: lastPayment?.status,
          isPaid: lastPayment?.isPaid,
          paidAt: lastPayment?.paidAt,
        },
        last5Payments: last5Payments
          ? last5Payments.map((payment: any) => ({
              id: payment?.id,
              total: payment?.total,
              status: payment?.status,
              isPaid: payment?.isPaid,
              paidAt: payment?.paidAt,
            }))
          : [],
      },
    };

    return res.status(200).json({ message: 'Associate by stock plates', data });
  } catch (error) {
    logger.info(`[getAssociateByStockPlates] Error: ${error}`);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// THIS CONTROLLER IS FOR WHATSAPP FLOW
export const validateEmail: AsyncController = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Missing email' });
    }

    const associate = await Associate.findOne({
      email: email.trim(),
    });

    if (!associate) {
      return res.status(404).json({ message: 'Associate not found' });
    }

    const isSameEmail = associate.email.trim() === email.trim();

    return res.status(200).json({ message: 'Associate found', isSameEmail: isSameEmail.toString() });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const validateRFC: AsyncController = async (req, res) => {
  try {
    const { rfc } = req.body;

    if (!rfc || typeof rfc !== 'string') {
      return res.status(400).json({ message: 'Missing email' });
    }

    const associate = await Associate.findOne({
      rfc: rfc.trim().toUpperCase(),
    });

    if (!associate) {
      return res.status(404).json({ message: 'Associate not found' });
    }

    let isSameEmail;
    if (associate?.rfc) {
      isSameEmail = associate.rfc.trim().toUpperCase() === rfc.trim().toUpperCase();
    }

    return res.status(200).json({ message: 'Associate found', isSameEmail: isSameEmail });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const validateCURP: AsyncController = async (req, res) => {
  try {
    const { curp } = req.body;

    if (!curp || typeof curp !== 'string') {
      return res.status(400).json({ message: 'Missing email' });
    }

    const associate = await Associate.findOne({
      curp: curp.trim().toUpperCase(),
    });

    if (!associate) {
      return res.status(404).json({ message: 'Associate not found' });
    }

    let isSameEmail;
    if (associate?.curp) {
      isSameEmail = associate?.curp.trim().toUpperCase() === curp.trim().toUpperCase();
    }

    return res.status(200).json({ message: 'Associate found', isSameEmail: isSameEmail });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const getLastPayment: AsyncController = async (req, res) => {
  try {
    const { clientId, totalMoreThan = 500 } = req.body;
    console.log('clientId', clientId);
    if (!clientId) {
      return res.status(400).json({ message: 'Missing associateId or clientId' });
    }

    console.log('payments api url', PAYMENTS_API_URL);

    const moreThan = `&totalMoreThan=${Number(totalMoreThan)}`;
    const limit = '&limit=1';
    const response = await axios.get(`${PAYMENTS_API_URL}/payments?clientId=${clientId}${limit}${moreThan}`, {
      headers: {
        Authorization: `Bearer ${process.env.PAYMENTS_API_KEY}`,
      },
    });
    // console.log('response data', response.data);
    const paymentArray = response.data.data;
    // console.log('payment array', paymentArray);
    const payment = paymentArray[0];
    console.log('payment', payment);
    if (!payment) {
      return res.status(404).json({ message: 'There is no payments' });
    }

    const obj = {
      id: payment.id,
      total: payment.total,
      status: payment.status,
      isPaid: payment.isPaid,
      paidAt: 'false',
    };

    if (payment.status === 'success' || payment.isPaid) {
      const paidAt = payment.paidAt?.split('T')[0];
      obj.paidAt = paidAt;
    }

    return res.status(200).json({ message: 'Associate found', data: obj });
  } catch (error) {
    console.log('error', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const generate1yeartoken: AsyncController = async (req, res) => {
  try {
    const accessToken = jwt.sign({}, accessTokenSecret, { expiresIn: '1y' });

    return res.status(200).json({ message: 'Token generated', accessToken });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error' });
  }
};

export const getAdendumSignaturesByAssociateId: AsyncController = async (req, res) => {
  try {
    const { associateId } = req.params;

    if (!associateId) {
      return res.status(400).json({ message: 'Missing associateId' });
    }

    const associate = await Associate.findById(associateId).select('+adendumDigitalSignature');

    if (!associate) {
      return res.status(404).json({ message: 'Associate not found' });
    }

    const adendumDigitalSignature = associate.adendumDigitalSignature;

    return res.status(200).json({ message: 'Adendum signatures', data: adendumDigitalSignature });
  } catch (error) {
    return res.status(500).json({ message: 'Internal server error' });
  }
};
