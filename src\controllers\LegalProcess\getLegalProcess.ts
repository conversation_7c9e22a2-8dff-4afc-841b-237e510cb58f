import { genericMessages } from '../../constants';
import LegalProcessStock from '../../models/legalProcessSchema';
import { AsyncController } from '../../types&interfaces/types';

export const getLegalProcessesById: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  try {
    const legalProcesses = await LegalProcessStock.find({ stockId: vehicleId }, null, {
      sort: { createdAt: 1 },
    });

    return res.status(200).send({ legalProcesses });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong });
  }
};
