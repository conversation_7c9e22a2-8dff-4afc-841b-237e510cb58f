import { Router } from 'express';
import {
  getSigntature,
  postSignature,
  sendAddendumToWeetrust,
} from '../controllers/digitalSignature.controller';
import { upload } from '../../../multer/multer';

const associateWeetrustRouter = Router();

associateWeetrustRouter.post('/', postSignature);

associateWeetrustRouter.get('/', getSigntature);

associateWeetrustRouter.post('/adendum', upload.single('adendum'), sendAddendumToWeetrust);

export default associateWeetrustRouter;
