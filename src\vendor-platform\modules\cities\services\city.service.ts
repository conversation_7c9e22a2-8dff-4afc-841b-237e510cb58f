import { CreateCityDto } from '../../company/dtos/create-city.dto';
import CompanyUserPermissions from '../../company/models/company-user-permissions.model';
import { City } from '../models/city.model';

export class CityService {
  static async createCity(data: CreateCityDto) {
    const city = new City(data);
    await city.save();
    return city;
  }

  static async getCities(companyId?: string, userId?: string) {
    const query = companyId ? { companyId } : {};
    if (userId) {
      const userPermissions = await CompanyUserPermissions.findOne({ userId });
      if (userPermissions && !['owner', 'admin'].includes(userPermissions.role)) {
        return City.find({ _id: { $in: userPermissions.allowedCities } });
      }
    }
    return City.find(query);
  }

  static async getCityById(id: string) {
    return City.findById(id);
  }

  static async updateCity(id: string, data: Partial<CreateCityDto>) {
    return City.findByIdAndUpdate(id, data, { new: true });
  }

  static async deleteCity(id: string) {
    return City.findByIdAndDelete(id);
  }
}
