import { Types } from 'mongoose';
import { removeEmptySpacesNameFile } from '../../../services/removeEmptySpaces';
import { IStockVehicle } from '../../../models/StockVehicleSchema';
import Document from '../../../models/documentSchema';
import Associate, { AssociateInstanceType } from '../../../models/associateSchema';
import { uploadFile } from '../../../aws/s3';
import AssociateUS from '../../../models/associateSchemaUS';
import { logger } from '../../../clean/lib/logger';
import { associateText } from '../../../constants';

interface IRegisterAssociateUS {
  associateData: any;
  files: { [fieldname: string]: Express.Multer.File[] };
  newAssociateId: Types.ObjectId;
  vehicle: IStockVehicle;
}

interface IUploadAssociateDocsUS extends Omit<IRegisterAssociateUS, 'associateData' | 'newAssociateId'> {
  associate: AssociateInstanceType;
}

type Docs = { [key: string]: Types.ObjectId | Types.ObjectId[] };

interface ICreateAssociateUS {
  associate: AssociateInstanceType;
  associateData: any;
  docs: Docs;
  bankStatements: Docs;
}

class AssociateServiceUS {
  private DOCS = 'docs';

  private BANK = 'bank';

  async registerAssociateUS(registerAssociate: IRegisterAssociateUS) {
    const { associateData, files, vehicle, newAssociateId } = registerAssociate;
    try {
      const associate = await Associate.findOne({
        _id: newAssociateId,
      });

      if (!associate) {
        throw new Error('Associate not found');
      }

      const { docs, bankStatements } = await this.uploadAssociateDocsUS({
        files: files,
        associate: associate,
        vehicle: vehicle,
      });

      const associateUS = await this.createAssociateUS({
        associate: associate,
        associateData: associateData,
        docs: docs,
        bankStatements: bankStatements,
      });

      logger.info(`AssociateUS registered successfully with id ${associateUS._id}`);

      return associateUS;
    } catch (err) {
      logger.error('Error registering associate US', err);
    }
    return null;
  }

  async updateAssociateUS({
    associateId,
    associateData,
  }: {
    associateId: Types.ObjectId;
    associateData: Record<string, any>;
  }) {
    try {
      await AssociateUS.findOneAndUpdate({ associate: associateId }, associateData);
    } catch (err) {
      logger.error('Error occured while updating associate US', err);
      throw err;
    }
  }

  async updateDriverLicenseAssociateUS({ driverLicenseFront, driverLicenseBack, associate, carNumber }: any) {
    try {
      const associateUS = await AssociateUS.findOne({ associate: associate._id });
      if (!associateUS) {
        return {
          status: 400,
          message: 'Driver not found',
        };
      }

      if (driverLicenseFront) {
        const removeSpacesdriverLicenseFront = removeEmptySpacesNameFile(driverLicenseFront);
        const doc = new Document({
          originalName: removeSpacesdriverLicenseFront,
          path: `associate/${carNumber}/${associate?.email}/driverLicenseFront/${removeSpacesdriverLicenseFront}`,
          associateId: associate?._id,
          vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
        });
        await doc.save();
        await AssociateUS.updateOne(
          { _id: associateUS._id },
          { $set: { 'documents.driverLicenseFront': doc._id } }
        );
        await uploadFile(
          driverLicenseFront,
          removeSpacesdriverLicenseFront,
          `associate/${carNumber}/${associate?.email}/driverLicenseFront/`
        );
      }
      if (driverLicenseBack) {
        const removeSpacesdriverLicenseBack = removeEmptySpacesNameFile(driverLicenseBack);
        const doc = new Document({
          originalName: removeSpacesdriverLicenseBack,
          path: `associate/${carNumber}/${associate?.email}/driverLicenseBack/${removeSpacesdriverLicenseBack}`,
          associateId: associate?._id,
          vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
        });
        await doc.save();

        await AssociateUS.updateOne(
          { _id: associateUS._id },
          { $set: { 'documents.driverLicenseBack': doc._id } }
        );
        await uploadFile(
          driverLicenseBack,
          removeSpacesdriverLicenseBack,
          `associate/${carNumber}/${associate?.email}/driverLicenseBack/`
        );
      }
      return {
        status: 200,
        message: associateText.success.associateUpdated,
      };
    } catch (error) {
      logger.error('Error occured while updating associateUS Driver License', error);
      return {
        status: 400,
        message: associateText.errors.associateNotUpdated,
        error,
      };
    }
  }

  async updateAddressVerificationAssociateUS({ addressVerification, associate, carNumber }: any) {
    try {
      const associateUS = await AssociateUS.findOne({ associate: associate._id });
      if (!associateUS) {
        return {
          status: 400,
          message: 'Driver not found',
        };
      }
      const removeSpacesAdressVeri = removeEmptySpacesNameFile(addressVerification);
      const doc = new Document({
        originalName: removeSpacesAdressVeri,
        path: `associate/${carNumber}/${associate.email}/addressVerification/${removeSpacesAdressVeri}`,
        associateId: associate?._id,
        vehicleId: associate.vehiclesId[associate.vehiclesId.length - 1],
      });
      await doc.save();
      if (associateUS.documents) {
        associateUS.documents.addressVerification = doc._id;
      }
      await associateUS.save();
      await uploadFile(
        addressVerification,
        removeSpacesAdressVeri,
        `associate/${carNumber}/${associate?.email}/addressVerification/`
      );

      return {
        status: 200,
        message: associateText.success.associateUpdated,
      };
    } catch (error) {
      logger.error('Error occured while updating associateUS Address Verification doc.', error);
      return {
        status: 400,
        message: associateText.errors.associateNotUpdated,
      };
    }
  }

  private async uploadAssociateDocsUS(uploadAssociateDocsUS: IUploadAssociateDocsUS) {
    const { vehicle, files, associate } = uploadAssociateDocsUS;

    const documentFieldsUS = [
      { fieldName: 'picture', directory: 'picture', obj: 'picture' },
      { fieldName: 'addressVerification', directory: 'addressVerification', obj: this.DOCS },
      { fieldName: 'driverLicenseFront', directory: 'driverLicense', obj: this.DOCS },
      { fieldName: 'driverLicenseBack', directory: 'driverLicense', obj: this.DOCS },
      { fieldName: 'garage', directory: 'garage', obj: this.DOCS },
      { fieldName: 'bankStatementsOne', directory: 'bankStatements', obj: this.BANK },
      { fieldName: 'bankStatementsTwo', directory: 'bankStatements', obj: this.BANK },
      { fieldName: 'bankStatementsThree', directory: 'bankStatements', obj: this.BANK },
      { fieldName: 'bankStatementsFour', directory: 'bankStatements', obj: this.BANK },
      { fieldName: 'bankStatementsFive', directory: 'bankStatements', obj: this.BANK },
      { fieldName: 'bankStatementsSix', directory: 'bankStatements', obj: this.BANK },
      // {
      //   fieldName: 'proofOfCompletionOfAnyRequiredSafetyCourses',
      //   directory: 'proofOfCompletionOfAnyRequiredSafetyCourses',
      //   obj: this.DOCS,
      // },
      // {
      //   fieldName: 'avgWeeklyIncomeOfLastTwelveWeeks',
      //   directory: 'avgWeeklyIncomeOfLastTwelveWeeks',
      //   obj: this.DOCS,
      // },
      // { fieldName: 'rideShareRideHistory', directory: 'rideShareRideHistory', obj: this.DOCS },
      // { fieldName: 'rideShareDates', directory: 'rideShareDates', obj: this.DOCS },
      // { fieldName: 'drivingRecord', directory: 'drivingRecord', obj: this.DOCS },
      // { fieldName: 'signature', directory: 'signature', obj: this.DOCS },
    ];

    let docs: Docs = {};
    let bankStatements: Docs = {};

    for (const field of documentFieldsUS) {
      if (field.fieldName in files) {
        const fieldConversion = field.fieldName as keyof typeof files;

        if (fieldConversion === 'avgWeeklyIncomeOfLastTwelveWeeks') {
          const weeklyIncomeOfLastTwelveWeeksFiles = files[fieldConversion];
          for (const weeklyIncomeOfLastTwelveWeeksFile of weeklyIncomeOfLastTwelveWeeksFiles) {
            const removeSpacesFileName = removeEmptySpacesNameFile(weeklyIncomeOfLastTwelveWeeksFile);
            const documentPath = `associate/${vehicle.carNumber}/${associate.email}/${field.directory}/${removeSpacesFileName}`;
            const s3Path = `associate/${vehicle.carNumber}/${associate.email}/${field.directory}/`;

            const document = new Document({
              originalName: removeSpacesFileName,
              path: documentPath,
              associateId: associate._id,
            });
            if (field.obj === this.DOCS) {
              const weeklyIncomeOfLastTwelveWeeks = docs[field.fieldName] as Types.ObjectId[];
              if (weeklyIncomeOfLastTwelveWeeks) {
                weeklyIncomeOfLastTwelveWeeks.push(document._id);
                docs[field.fieldName] = weeklyIncomeOfLastTwelveWeeks;
              } else {
                docs[field.fieldName] = [document._id];
              }
            }
            await document.save();
            await uploadFile(weeklyIncomeOfLastTwelveWeeksFile, removeSpacesFileName, s3Path);
          }
        } else {
          const file = files[fieldConversion] ? files[fieldConversion][0] : undefined;
          if (file) {
            const removeSpacesFileName = removeEmptySpacesNameFile(file);
            const documentPath = `associate/${vehicle.carNumber}/${associate.email}/${field.directory}/${removeSpacesFileName}`;
            const s3Path = `associate/${vehicle.carNumber}/${associate.email}/${field.directory}/`;

            const document = new Document({
              originalName: removeSpacesFileName,
              path: documentPath,
              associateId: associate._id,
            });
            if (field.obj === this.DOCS) {
              docs[field.fieldName] = document._id;
            } else if (field.obj === 'bank') {
              bankStatements[field.fieldName] = document._id;
            } else if (field.obj === 'picture') {
              associate.picture = document._id;
            }

            await document.save();
            await uploadFile(file, removeSpacesFileName, s3Path);
          }
        }
      }
    }
    await associate.save();

    return { docs, bankStatements };
  }

  private async createAssociateUS(createAssociateUS: ICreateAssociateUS) {
    const { associate, associateData, docs, bankStatements } = createAssociateUS;

    const associateUSMetadata = {
      associate: associate._id,
      city: associateData.city,
      state: associateData.state,
      documents: docs,
      bankStatement: {
        bankStatementOne: bankStatements.bankStatementsOne,
        bankStatementTwo: bankStatements.bankStatementsTwo,
        bankStatementThree: bankStatements.bankStatementsThree,
        bankStatementFour: bankStatements.bankStatementsFour,
        bankStatementFive: bankStatements.bankStatementsFive,
        bankStatementSix: bankStatements.bankStatementsSix,
      },
      contacts: [
        {
          emergencyContactName: associateData.emergencyContactName,
          emergencyContactPhone: associateData.emergencyContactPhone,
          emergencyContactRelation: associateData.emergencyContactRelation,
        },
      ],
      ssn: associateData.ssn,
      rideShareTotalRides: associateData.rideShareTotalRides,
      avgEarningPerWeek: associateData.avgEarningPerWeek,
      mobilityPlatforms: associateData.mobilityPlatforms,
      termsAndConditions: associateData.termsAndConditions,
      dataPrivacyConsentForm: associateData.dataPrivacyConsentForm,
    };

    const associateUS = new AssociateUS(associateUSMetadata);
    await associateUS.save();

    return associateUS;
  }
}

const associateServiceUS = new AssociateServiceUS();

export { associateServiceUS };
