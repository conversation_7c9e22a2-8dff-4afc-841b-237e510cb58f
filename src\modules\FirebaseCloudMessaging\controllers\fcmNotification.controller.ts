import { Request, Response } from 'express';
import {
  fcmNotificationPayloadDto,
  fcmNotificationUnregisterDto,
  fcmTokenDto,
  fcmTokenStatusUpdateDto,
  fcmUpdateNotificationTokenDto,
} from '../dto/fcmToken.dto';
import { fcmNotificationService } from '../services/fcmNotification.service';
import { AlreadyExistException, NotFoundException } from '@/clean/errors/exceptions';
import { z } from 'zod';
import { NotificationPayload } from '../interfaces/fcmNotificationPayload';
import { errorCodeUtils } from '@/utils/error.utils';

export const registerNotificationToken = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmTokenDto.parse(req.body);
    await fcmNotificationService.registerNotificationToken({
      userId: validatedData.userId,
      fcmToken: validatedData.fcmToken,
      deviceDetails: validatedData.deviceDetails,
      userType: validatedData.userType,
    });
    res.status(200).send({ message: 'FCM token registered successfully.' });
  } catch (error) {
    // Handle validation errors or other errors
    if (error instanceof z.ZodError) {
      res.status(400).send(errorCodeUtils.INVALID_REQUEST_DATA);
    } else if (error instanceof AlreadyExistException) {
      res.status(409).send(errorCodeUtils.NOTIFICATION_TOKEN_ALREADY_EXISTS);
    } else {
      res.status(500).send({ error: 'Error registering FCM token.', code: 500, message: error });
    }
  }
};

export const unregisterNotificationToken = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmNotificationUnregisterDto.parse(req.body);
    await fcmNotificationService.unregisterNotificationToken(validatedData.userId, validatedData.fcmToken);
    res.status(200).send({ message: 'FCM token unregistered successfully.' });
  } catch (error) {
    if (error instanceof NotFoundException) {
      res.status(404).send({ error: 'FCM token not found for the associateId.', code: 404, message: error });
    } else {
      res.status(500).send({ error: 'Error unregistering FCM token.' });
    }
  }
};

export const updateNotificationTokenState = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmTokenStatusUpdateDto.parse(req.body);
    await fcmNotificationService.updateNotificationTokenState(
      validatedData.userId,
      validatedData.fcmToken,
      validatedData.isActive
    );
    res.status(200).json({ message: 'FCM token inactivated successfully.' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).send(errorCodeUtils.INVALID_REQUEST_DATA);
    } else if (error instanceof NotFoundException) {
      res.status(404).send({ error: 'FCM token not found for the associateId.', code: 404, message: error });
    } else {
      res.status(500).json({ error: 'Error inactivating FCM token.' });
    }
  }
};

export const sendNotificationToAssociateById = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmNotificationPayloadDto.parse(req.body);
    const payload: NotificationPayload = {
      title: validatedData.payload.title,
      body: validatedData.payload.body,
      data: validatedData.payload.data,
    };
    await fcmNotificationService.sendNotificationToAssociateById(validatedData.userId, payload);
    res.status(200).json({ message: 'Notification sent successfully.' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).send(errorCodeUtils.INVALID_REQUEST_DATA);
    } else if (error instanceof NotFoundException) {
      if (error.code == 'NO_ACTIVE_TOKENS') {
        res.status(404).send(errorCodeUtils.NO_ACTIVE_TOKENS);
      } else {
        res.status(404).send({ code: error.code, status: 404, message: error });
      }
    } else {
      res.status(500).send(errorCodeUtils.INTERNAL_SERVER_ERROR);
    }
  }
};

export const refreshNotificationToken = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmUpdateNotificationTokenDto.parse(req.body);
    await fcmNotificationService.refreshNotificationToken({
      userId: validatedData.userId,
      oldToken: validatedData.oldToken,
      newToken: validatedData.newToken,
    });
    res.status(200).send({ message: 'FCM token updated successfully.' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).send(errorCodeUtils.INVALID_REQUEST_DATA);
    } else if (error instanceof NotFoundException) {
      res.status(404).send({ error: 'FCM token not found for the associateId.', code: 404, message: error });
    } else {
      res.status(500).send({ error: 'Error updating FCM token.', code: 500, message: error });
    }
  }
};
