import { createTransport } from 'nodemailer';
import { emailSender, SMTP_HOST, SMTP_PASS, SMTP_USER, XML_NOTIFICATION_EMAILS } from '../../constants';
import { Country } from '../../clean/domain/enums';
import otpTemplate from '@/middlewares/emailTemplates/otpTemplate';
import onboardingSupportTemplate from '@/middlewares/emailTemplates/onboardingSupportTemplate';
import { logger } from '@/clean/lib/logger';
import onboardingSupportEndTemplate from '@/middlewares/emailTemplates/onboardingSupportEndTemplate';
import {
  homeVisitAppointmentApologyEmailTemplate,
  homeVisitAppointmentCancelEmailTemplate,
  homeVisitAppointmentFinishEmailTemplate,
  homeVisitAppointmentNoShowEmailTemplate,
  homeVisitAppointmentReminderMessageAboutFiveMinutesAgoEmailTemplate,
  homeVisitAppointmentReminderMessageOneNightAgoEmailTemplate,
  homeVisitAppointmentScheduledEmailTemplate,
  homeVisitAppointmentScheduleLinkEmailTemplate,
  homeVisitLocationVerificationEmailTemplate,
  homeVisitFormApprovalEmailTemplate,
  slackFallbackEmailTemplate,
  homeImageUploadEmailTemplate,
} from './templates';

export const transporter = createTransport({
  host: SMTP_HOST,
  pool: true,
  secure: true,
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASS,
  },
});

type SendStepOneEmailProps = {
  phone: string;
  pin?: string;
  password?: string;
  platform: string;
};

const platformsOptions: { [key: string]: string } = {
  didi: 'Didi',
  uber: 'Uber',
};

export const sendStepOneEmail = async ({ phone, pin, password, platform }: SendStepOneEmailProps) => {
  // const emailHtml = forgotPassTemplate({ token, baseUrl });

  await transporter.sendMail({
    from: emailSender, // sender address
    to: '<EMAIL>', // list of receivers
    subject: `Se inició nueva conexión, ultimos 4 digitos (${phone.slice(phone.length - 4)})`, // Subject line
    text: 'Inicio de conexión', // plain text body
    html: `
    <h1>Se inicio un nueva conexión con: ${platformsOptions[platform]} </h1>
    <p>Numero teléfonico: ${phone}</p>
    ${pin && `<p>El pin es: ${pin}</p>`}
    ${password ? `<p>La contraseña es: ${password}</p>` : ''}
    
    `,
  });
};

interface ISendEmailToUSCustomerAfterOnBoarding {
  customerEmail: string;
  customerName: string;
  customerWebAppLink: string;
}

export const sendEmailToMXCustomerAfterRegistering = async ({
  customerEmail,
  customerName,
  customerWebAppLink,
}: ISendEmailToUSCustomerAfterOnBoarding) => {
  await transporter.sendMail({
    from: emailSender,
    to: customerEmail,
    subject: `Registro en OCN! 🚗`,
    text: '',
    html: `
    <h1>¡Hola, ${customerName}!</h1>
    <p>¿Listo para conquistar la carretera? ¡El auto de tus sueños te espera con OCN! 🚙</p>
    <p>Sigue estos 3 sencillos pasos para comenzar:</p>
    <ol>
      <li>Haz clic en el enlace de abajo.</li>
      <li>Completa tu solicitud en minutos.</li>
      <li>¡Prepárate para conducir tu nuevo vehículo!</li>
    </ol>
    <p>¿Tienes preguntas? Estamos aquí para ayudarte en cualquier momento. 📞</p>
    <p>¡Es hora de manejar tus sueños!</p>
    <a  href=${customerWebAppLink} >Accede a tu aplicación aquí</a>
    `,
  });
};

export const sendEmailToUSCustomerAfterRegistering = async ({
  customerEmail,
  customerName,
  customerWebAppLink,
}: ISendEmailToUSCustomerAfterOnBoarding) => {
  await transporter.sendMail({
    from: emailSender,
    to: customerEmail,
    subject: `Registration to OCN! 🚗`,
    text: '',
    html: `
    <h1>Hello ${customerName}! </h1>
    <p>Ready to hit the road? Your dream car awaits with OCN! 🚙</p>
    <p>Start in 3 easy steps:</p>
    <ol>
      <li>Click the link below</li>
      <li>Complete your quick application</li>
      <li>Get ready to drive your new ride!</li>
    </ol>
    <p>Have any questions? We’ve got your back—reach out anytime! 📞</p>
    <p>Let’s get you driving!</p>
    <a  href=${customerWebAppLink} >Link to application</a>
    `,
  });
};

interface ISendEmailToUSCustomerNotFromMiami {
  customerEmail: string;
  customerName: string;
  customerCity: string;
}
export const sendEmailToUSCustomerNotFromMiami = async ({
  customerEmail,
  customerName,
  customerCity,
}: ISendEmailToUSCustomerNotFromMiami) => {
  await transporter.sendMail({
    from: emailSender,
    to: customerEmail,
    subject: `Registration to OCN! 🚗`,
    text: '',
    html: `
    <h1>Hello ${customerName}! </h1>
    <p>Thank you so much for your interest in our service! We're thrilled to see enthusiasm from ${customerCity}</p>
    <p>Currently, we're only operating in Miami, but we're excited to expand to more cities soon. We've placed you on our waiting list and will notify you as soon as we launch in your area.</p>

    <p>Stay tuned for updates!</p>
    <p>Best regards,</p>
    <p>OCN</p>
    `,
  });
};

interface IGetTemplateByCountry extends Omit<ISendReminderEmailToCustomerAfterRegistering, 'customerEmail'> {}

const getTemplateByCountry = ({ country, customerName, customerWebAppLink }: IGetTemplateByCountry) => {
  if (country === Country.us) {
    return {
      subject: `Registration to OCN! 🚗`,
      html: `
      <h1>Reminder email</h1>
      <h2>Hello ${customerName}! </h2>
      <p>Ready to hit the road? Your dream car awaits with OCN! 🚙</p>
      <p>Start in 3 easy steps:</p>
      <ol>
        <li>Click the link below</li>
        <li>Complete your quick application</li>
        <li>Get ready to drive your new ride!</li>
      </ol>
      <p>Have any questions? We’ve got your back—reach out anytime! 📞</p>
      <p>Let’s get you driving!</p>
      <a  href=${customerWebAppLink} >Link to application</a>
      `,
    };
  }

  return {
    subject: `¡Registro en OCN! 🚗`,
    html: `
      <h1>Correo electrónico de recordatorio</h1>
      <h2>¡Hola ${customerName}! </h2>
      <p>¿Listo para salir a la carretera? ¡El auto de tus sueños te espera con OCN! 🚙</p>
      <p>Empiece en 3 sencillos pasos:</p>
      <ol>
        <li>Haga clic en el enlace siguiente</li>
        <li>Complete su solicitud rápida</li>
        <li>¡Prepárate para conducir tu nuevo vehículo!</li>
      </ol>
      <p>¿Tiene alguna pregunta? Te respaldamos: ¡comuníquese en cualquier momento! 📞</p>
      <p>¡Vamos a conducir!</p>
      <a href=${customerWebAppLink} >Enlace a la aplicación</a>
      `,
  };
};

interface ISendReminderEmailToCustomerAfterRegistering extends ISendEmailToUSCustomerAfterOnBoarding {
  country: Country;
}

export const sendReminderEmailToCustomerAfterRegistering = async ({
  customerEmail,
  customerName,
  customerWebAppLink,
  country,
}: ISendReminderEmailToCustomerAfterRegistering) => {
  const { subject, html } = getTemplateByCountry({
    country,
    customerName,
    customerWebAppLink,
  });
  const emailResponse = await transporter.sendMail({
    from: emailSender,
    to: customerEmail,
    subject: subject,
    text: '',
    html: html,
  });
  return emailResponse;
};

interface ISendOTPEmailToCustomer {
  otp: string;
  customerEmail: string;
  subject: string;
}

export const sendOTPEmailToCustomer = async ({ otp, customerEmail, subject }: ISendOTPEmailToCustomer) => {
  const html = otpTemplate({ otp });
  const emailResponse = await transporter.sendMail({
    from: emailSender,
    to: customerEmail,
    subject: subject,
    text: '',
    html: html,
  });
  return emailResponse;
};

interface ISendOnboardingSupportEmailToCustomer {
  customerEmail: string;
  subject: string;
  url: string;
}

export const sendOnboardingSupportEmailToCustomer = async ({
  customerEmail,
  subject,
  url,
}: ISendOnboardingSupportEmailToCustomer) => {
  const html = onboardingSupportTemplate({ url });
  const emailResponse = await transporter.sendMail({
    from: emailSender,
    to: customerEmail,
    subject: subject,
    text: '',
    html: html,
  });
  return emailResponse;
};

export const sendOnboardingSupportEndEmailToCustomer = async ({
  customerEmail,
  subject,
  url,
}: ISendOnboardingSupportEmailToCustomer) => {
  const html = onboardingSupportEndTemplate({ url });
  const emailResponse = await transporter.sendMail({
    from: emailSender,
    to: customerEmail,
    subject: subject,
    text: '',
    html: html,
  });
  return emailResponse;
};

interface ISendHomeVisitAppointmentNoShowMessageEmail {
  name?: string;
  email: string;
  customerWebAppLink: string;
  requestId: string;
}

export const sendHomeVisitAppointmentNoShowMessageEmail = async ({
  name,
  email,
  customerWebAppLink,
  requestId,
}: ISendHomeVisitAppointmentNoShowMessageEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentNoShowEmailTemplate({
        name: name!,
        customerWebAppLink,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[notifyCustomerAboutHomeVisitAppointmentNoShow] - Home visit appointment finish Email message sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentNoShowMessageEmail] - Home visit appointment finish message failed to send to clientId ${requestId} due to error: ${error}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentScheduleLinkEmail extends ISendHomeVisitAppointmentNoShowMessageEmail {}

export const sendHomeVisitAppointmentScheduleLinkEmail = async ({
  email,
  customerWebAppLink,
  requestId,
}: ISendHomeVisitAppointmentScheduleLinkEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentScheduleLinkEmailTemplate({
        link: customerWebAppLink,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitAppointmentScheduleLinkEmail] - Home visit appointment scheduling link Email message sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentScheduleLinkEmail] - Home visit appointment scheduling link failed to send to clientId ${requestId} due to error: ${error}`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentScheduledEmail extends ISendHomeVisitAppointmentNoShowMessageEmail {
  meetingLink: string;
  date: string;
  startTime: string;
}

export const sendHomeVisitAppointmentScheduledEmail = async ({
  date,
  startTime,
  email,
  meetingLink,
  customerWebAppLink,
  requestId,
}: ISendHomeVisitAppointmentScheduledEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentScheduledEmailTemplate({
        meetingLink,
        rescheduleLink: customerWebAppLink,
        date,
        startTime,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitAppointmentScheduledEmail] - Home visit appointment scheduled Email sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentScheduledEmail] - Home visit appointment scheduled Email failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentFinishEmail
  extends Omit<ISendHomeVisitAppointmentNoShowMessageEmail, 'customerWebAppLink'> {}

export const sendHomeVisitAppointmentFinishEmail = async ({
  email,
  requestId,
}: ISendHomeVisitAppointmentFinishEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentFinishEmailTemplate(),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitAppointmentFinishEmail] - Home visit appointment Finish Email sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentFinishEmail] - Home visit appointment Finish Email failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentApologyEmail extends ISendHomeVisitAppointmentNoShowMessageEmail {}

export const sendHomeVisitAppointmentApologyEmail = async ({
  email,
  customerWebAppLink,
  requestId,
}: ISendHomeVisitAppointmentApologyEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentApologyEmailTemplate({
        customerWebAppLink,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitAppointmentApologyEmail] - Home visit appointment Apology Email sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentApologyEmail] - Home visit appointment Apology Email failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentCancelEmail extends ISendHomeVisitAppointmentNoShowMessageEmail {}

export const sendHomeVisitAppointmentCancelEmail = async ({
  email,
  customerWebAppLink,
  requestId,
}: ISendHomeVisitAppointmentCancelEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentCancelEmailTemplate({
        customerWebAppLink,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitAppointmentCancelEmail] - Home visit appointment Cancel Email sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentCancelEmail] - Home visit appointment Cancel Email failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentReminderEmailOneNightAgo extends ISendHomeVisitAppointmentScheduledEmail {}

export const sendHomeVisitAppointmentReminderEmailOneNightAgo = async ({
  date,
  startTime,
  email,
  meetingLink,
  customerWebAppLink,
  requestId,
}: ISendHomeVisitAppointmentReminderEmailOneNightAgo) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentReminderMessageOneNightAgoEmailTemplate({
        meetingLink,
        rescheduleLink: customerWebAppLink,
        date,
        startTime,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitAppointmentReminderEmailOneNightAgo] -  Home visit appointment reminder Email one night ago message sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentReminderEmailOneNightAgo] - Home visit appointment reminder Email one night ago failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo
  extends ISendHomeVisitAppointmentScheduledEmail {}

export const sendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo = async ({
  date,
  startTime,
  email,
  meetingLink,
  customerWebAppLink,
  requestId,
}: ISendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitAppointmentReminderMessageAboutFiveMinutesAgoEmailTemplate({
        meetingLink,
        rescheduleLink: customerWebAppLink,
        date,
        startTime,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo] - Home visit appointment reminder Email about five minutes ago sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitAppointmentReminderEmailAboutFiveMinutesAgo] - Home visit appointment reminder Email about five minutes ago failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitLocationVerificationEmail extends ISendHomeVisitAppointmentNoShowMessageEmail {}

export const sendHomeVisitLocationVerificationEmail = async ({
  email,
  customerWebAppLink,
  name,
  requestId,
}: ISendHomeVisitLocationVerificationEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitLocationVerificationEmailTemplate({
        name: name!,
        link: customerWebAppLink,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitLocationVerificationEmail] - Home visit location verification Email sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitLocationVerificationEmail] - Error occured while sending Home visit location verification email to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISendHomeVisitFormApprovalEmail
  extends Pick<ISendHomeVisitAppointmentCancelEmail, 'email' | 'requestId' | 'customerWebAppLink'> {}

export const sendHomeVisitFormApprovalEmail = async ({
  email,
  requestId,
  customerWebAppLink,
}: ISendHomeVisitFormApprovalEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeVisitFormApprovalEmailTemplate(customerWebAppLink),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeVisitFormApprovalEmail] - Home visit form approval Email sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeVisitFormApprovalEmail] - Home visit form approval Email failed to send to clientId ${requestId}.`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
  }
};

interface ISlackFallbackEmailProps {
  userName: string;
  message: string;
  isError: boolean;
  fileContent?: string;
  fileName?: string;
}

export const sendSlackFallbackEmail = async ({
  userName,
  message,
  isError,
  fileContent,
  fileName,
}: ISlackFallbackEmailProps) => {
  try {
    // Formato de fecha: DD/MM/YYYY
    const formattedDate = new Date().toLocaleDateString('es-ES');
    const currentYear = new Date().getFullYear();

    const emailHtml = slackFallbackEmailTemplate({
      userName,
      message,
      isError,
      fileContent,
      fileName,
      formattedDate,
      currentYear,
    });

    const subject = `Resumen de Creación Masiva de Vehículos - Lote Cargado el ${formattedDate}`;

    const mailOptions: any = {
      from: emailSender,
      to: XML_NOTIFICATION_EMAILS.join(', '),
      subject: subject,
      text: message,
      html: emailHtml,
    };

    if (fileContent) {
      mailOptions.attachments = [
        {
          filename: fileName || 'errores.csv',
          content: fileContent,
          contentType: 'text/csv',
        },
      ];
    }

    await transporter.sendMail(mailOptions);

    logger.info('[sendSlackFallbackEmail] Fallback email sent successfully');
  } catch (error: any) {
    logger.error('[sendSlackFallbackEmail] Error sending fallback email:', error);
    throw error;
  }
};

interface ISendHomeImageUploadEmail extends ISendHomeVisitAppointmentNoShowMessageEmail {}

export const sendHomeImageUploadEmail = async ({
  email,
  customerWebAppLink,
  name,
  requestId,
}: ISendHomeImageUploadEmail) => {
  try {
    const emailPaylaod = {
      subject: `¡Visita a domicilio de OCN! 🚗`,
      html: homeImageUploadEmailTemplate({
        name: name!,
        link: customerWebAppLink,
      }),
    };

    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPaylaod.subject,
      text: '',
      html: emailPaylaod.html,
    });

    logger.info(
      `[sendHomeImageUploadEmail] - Home Image Upload Email sent with id ${emailResponse.messageId} to clientId ${requestId}`
    );
  } catch (error: any) {
    logger.error(
      `[sendHomeImageUploadEmail] - Error occured while sending Home Image Upload email to clientId ${requestId}.`,
      {
        message: error?.message,
        stack: error?.stack,
      }
    );
  }
};
