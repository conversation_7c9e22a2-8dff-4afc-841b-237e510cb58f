import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';

export enum InstallationStatus {
  scheduled = 'scheduled',
  completed = 'completed',
  canceled = 'canceled',
  'not-attended' = 'not-attended',
  installed = 'installed',
  rescheduled = 'rescheduled',
}

export interface IInstallationAppointment extends Document {
  companyId: mongoose.Types.ObjectId;
  cityId: mongoose.Types.ObjectId;
  crewId: mongoose.Types.ObjectId;
  neighborhoodId: mongoose.Types.ObjectId;
  associateId: string;
  stockId: string;
  company: any;
  city: any;
  crew: any;
  neighborhood: any;
  address: {
    street: string;
    interiorNumber: string;
    exteriorNumber: string;
    colony: string;
    zipCode: string;
    references: string;
  };
  startTime: Date;
  endTime: Date;
  status: keyof typeof InstallationStatus;
  notes?: string;
  notAttendedReason?: string; // Nuevo campo para la razón de no asistencia
  companyProof: {
    images: mongoose.Types.ObjectId[];
    imagesUrls: string[];
    uploadedAt: Date;
    comments: string;
  };
  driverProof: {
    images: mongoose.Types.ObjectId[];
    imagesUrls: string[];
    uploadedAt: Date;
    comments: string;
  };
  notifications: {
    oneNightBefore: {
      eventId: string;
      scheduled: boolean;
      sent: boolean;
    };
    oneHourBefore: {
      eventId: string;
      scheduled: boolean;
      sent: boolean;
    };
  };
  reschedulingHistory: {
    previousStartTime: Date;
    newStartTime: Date;
    rescheduledAt: Date;
    previousNeighborhoodId?: mongoose.Types.ObjectId;
    rescheduledByVendor?: mongoose.Types.ObjectId;
    rescheduledByAdmin?: string;
  }[];
  arrivedAt?: Date;
}

const InstallationAppointmentSchema = new Schema(
  {
    companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
    cityId: { type: Schema.Types.ObjectId, ref: 'City', required: true },
    crewId: { type: Schema.Types.ObjectId, required: true },
    neighborhoodId: { type: Schema.Types.ObjectId, required: true },
    associateId: { type: String, required: true, unique: true },
    stockId: { type: String, required: true, unique: true },
    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    status: {
      type: String,
      enum: Object.values(InstallationStatus),
      default: InstallationStatus.scheduled,
    },
    address: {
      street: { type: String },
      interiorNumber: { type: String },
      exteriorNumber: { type: String },
      colony: { type: String },
      zipCode: { type: String },
      references: { type: String },
    },
    notes: { type: String },
    notAttendedReason: { type: String }, // Nuevo campo en el esquema
    companyProof: {
      images: {
        type: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Document',
          },
        ],
        select: false, // Oculto por defecto
      },
      imagesUrls: [
        {
          type: String,
        },
      ],
      uploadedAt: Date,
      comments: String,
    },
    driverProof: {
      images: {
        type: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Document',
          },
        ],
        select: false, // Oculto por defecto
      },
      imagesUrls: [
        {
          type: String,
        },
      ],
      uploadedAt: Date,
      comments: String,
    },
    notifications: {
      oneNightBefore: {
        eventId: String,
        scheduled: Boolean,
        sent: Boolean,
      },
      oneHourBefore: {
        eventId: String,
        scheduled: Boolean,
        sent: Boolean,
      },
      select: false,
    },
    reschedulingHistory: [
      {
        previousStartTime: { type: Date, required: true },
        previousNeighborhoodId: { type: Schema.Types.ObjectId, ref: 'Neighborhood', required: false },
        newStartTime: { type: Date, required: true },
        rescheduledAt: { type: Date, default: Date.now },
        rescheduledByVendor: { type: Schema.Types.ObjectId, ref: 'User', required: false },
        rescheduledByAdmin: { type: String, required: false },
      },
    ],
    arrivedAt: { type: Date },
  },
  { timestamps: true }
);

InstallationAppointmentSchema.index({ neighborhoodId: 1, startTime: 1 });

// create virtual fields for population
InstallationAppointmentSchema.virtual('company', {
  ref: 'Company',
  localField: 'companyId',
  foreignField: '_id',
  justOne: true,
});

InstallationAppointmentSchema.virtual('city', {
  ref: 'City',
  localField: 'cityId',
  foreignField: '_id',
  justOne: true,
});

InstallationAppointmentSchema.virtual('crew', {
  ref: 'Crew',
  localField: 'crewId',
  foreignField: '_id',
  justOne: true,
});

InstallationAppointmentSchema.virtual('neighborhood', {
  ref: 'Neighborhood',
  localField: 'neighborhoodId',
  foreignField: '_id',
  justOne: true,
});

InstallationAppointmentSchema.set('toObject', { virtuals: true });
InstallationAppointmentSchema.set('toJSON', { virtuals: true });

InstallationAppointmentSchema.virtual('rescheduleCount').get(function () {
  return this.reschedulingHistory?.length || 0;
});

export const InstallationAppointment = vendorDB.model<IInstallationAppointment>(
  'InstallationAppointment',
  InstallationAppointmentSchema
);
