import { Types } from 'mongoose';

import { Document, Schema, model } from 'mongoose';
import { AdmissionRequestStatus } from '../constants/socialScoring';
import { AnalysisStatus, Country, MaritalStatus, MLModels } from '../clean/domain/enums';

export interface Referrer {
  _id?: Types.ObjectId;
  name: string;
  email: string;
  phone: string;
}
export interface AdmissionRequestMongoI extends Document {
  isImported: boolean;
  status: AdmissionRequestStatus;
  convertedToAssociate: boolean;
  rejectionReason: string;
  documentsAnalysis: RequestDocumentsAnalysisMongoI;
  firstBlockVerifier: boolean;
  personalData: RequestPersonalDataMongoI;
  palenca: RequestPalencaMongoI;
  homeVisit: RequestHomeVisitMongoI;
  earningsAnalysis: EarningsAnalysisMongoI;
  modelScores: ModelScoresMongoI;
  riskAnalysis: RiskAnalysisMongoI;
  typeOfPreapproval: string;
  suggestedTypeOfPreapproval: string;
  preApprovalReason?: string;
  hubspot: {
    id: string;
  };
  hubspotDeal: {
    id: string;
  };
  hilos: {
    id: string;
    conversation: string;
    contact: string;
  };
  video: object;
  screenshots: object;
  isRequestLinkOpened: boolean;
  openLinkDates?: Array<Date>;
  createdAt: Date;
  updatedAt: Date;
  source: string;
  clientIpAddress: string;
  associateId?: Types.ObjectId;
  homeVisitScheduleLinkSendDate: Date;
  avalData: RequestAvalDataMongoI;
  locationData: RequestLocationDataMongoI;
  agentId: Types.ObjectId;
}

export interface ModelResultMongoI extends Document {
  modelName: MLModels;
  status: string;
  modelScore: number;
  modelFeatureScores: object;
  modelWeights: object;
  modelResult: object;
}

export interface ModelScoresMongoI extends Document {
  [MLModels.RIDESHARE_PERFORMANCE]: ModelResultMongoI;
  [MLModels.PERSONAL_INFORMATION]: ModelResultMongoI;
  [MLModels.HOMEVISIT_INFORMATION]: ModelResultMongoI;
  [MLModels.FINANCIAL_ASSESSMENT]: ModelResultMongoI;
  // [MLModels.DRIVING_AND_LEGAL_HISTORY]: ModelResultMongoI;
}

export interface ScorecardCategoryMongoI extends Document {
  threshold: { min: number; max: number } | string;
  riskCategory: string;
  riskScore: number;
  weight: number;
  result: number;
}
export interface ScorecardDetailMongoI extends Document {
  variable: string;
  category: ScorecardCategoryMongoI;
}

export interface ScorecardMongoI extends Document {
  totalScore: number;
  scaledScore: number;
  details: ScorecardDetailMongoI[];
  minScore: number;
  maxScore: number;
  minScaledScore: number;
  maxScaledScore: number;
}

export interface RiskAnalysisMongoI extends Document {
  status: string;
  scorecardVersion: string;
  scorecard?: ScorecardMongoI;
}

export interface DailyEarningMongoI {
  amount: number;
  countTrips: number;
  earningDate: Date;
  currency: string;
}
export interface WeeklyEarningMongoI extends Document {
  totalAmount: number;
  totalTrips: number;
  fromDate: Date;
  toDate: Date;
  week: number;
  year: number;
  currency: string;
  dailyEarnings: DailyEarningMongoI[];
}
export interface EarningsAnalysisMongoI extends Document {
  status: string;
  totalEarnings: number;
  earnings: WeeklyEarningMongoI[];
  platforms: number;
  earningsNotificationSent?: boolean;
}

export interface RequestPersonalDataMongoI extends Document {
  status: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  birthdate: string;
  taxId: string;
  nationalId: string;
  postalCode: string;
  city: string;
  state: string;
  neighborhood: string;
  street: string;
  streetNumber: string;
  department: string;
  country: keyof typeof Country;
  vehicleSelected: string;
  ssn: string;
  rideShareTotalRides: number;
  avgEarningPerWeek: number;
  termsAndConditions: boolean;
  dataPrivacyConsentForm: boolean;
  nationality: string;
  age: number;
  occupation: string;
  homePhone: string;
  timeInResidency: string;
  municipality: string;
  maritalStatus: string;
  dependents: string;
  spouseOrPartnerIncome: string;
  partnerSourceOfIncome: string;
  partnerName: string;
  partnerPhone: string;
  noOfDependents: number;
  dependendsInfo: Array<{
    dependendName: string;
    dependendPhone: string | null;
    dependentRelationship: string;
  }>;
  ownACar: string;
  carLeasingtime: string;
  carMake: string;
  carModel: string;
  ownDebt: string;
  outStandingDebt: string;
  doesDebtAffectPersonalFinance: string;
  references: {
    reference1Name: string;
    reference1Phone: string;
    reference1Relationship: string;
    reference1Address: string;
    reference2Name: string;
    reference2Phone: string;
    reference2Relationship: string;
    reference2Address: string;
    reference3Name: string;
    reference3Phone: string;
    reference3Relationship: string;
    reference3Address: string;
  };
  learnAboutOcn: string;
  referrer: Referrer;
  doesItApplyToElectricCars: string;
  ficoScore: string;
  criminalBackgroundCheck: string;
  motorVehicleRecordCheck: string;
  privacyPolicy: boolean;
  ocnBackgroundAndCreditCheckForApplication: boolean;
}

export interface RequestDocumentsMongoI extends Document {
  mediaId: Types.ObjectId | null;
  status: string;
  type: string;
  analysisResult: DocumentAnalysisResultMongoI | null;
}

export interface DocumentAnalysisResultMongoI extends Document {
  status: string;
  extractedData: any;
  validationErrors: string[];
  validationWarnings: string[];
  confidence: number;
}

export interface RequestDocumentsAnalysisMongoI extends Document {
  status: string;
  documents: RequestDocumentsMongoI[];
}

export interface PalencaAccountRetrievalMongoI extends Document {
  status: string;
}

export interface PalencaAccountMongoI extends Document {
  accountId: string;
  platform: string;
  earnings: PalencaAccountRetrievalMongoI;
  metrics: PalencaAccountRetrievalMongoI;
  status: string;
  createdAt: Date;
}
export interface RequestPalencaMongoI extends Document {
  widgetId: string;
  externalId: string;
  accounts: PalencaAccountMongoI[];
}

export interface RequestHomeVisitMongoI extends Document {
  status: string;
  residentOwnershipStatus: string;
  comments: string;
  images: string[];
  visitDate: Date;
  visitTime: string;
  houseInformation: {
    ownProperty: string;
    nameOfOwner: string;
    ownerRelative: string;
    ownerRelativeRelation: string;
    ownerPhone: string;
    typeOfHousing: string;
    noOfBedrooms: number;
    livingRoom: string;
    dinningRoom: string;
    kitchen: string;
    television: string;
    audioSystem: string;
    stove: string;
    refrigerator: string;
    washingMachine: string;
  };
  proofOfPropertyOwnership: string[];
  visitorEmailAddress: string;
  doesProofOfAddressMatchLocation: string;
  characteristicsOfGarage: string;
  behaviourOfCustomerDuringCall: string;
  homeVisitStepsStatus: {
    personal?: string;
    contact?: string;
    address?: string;
    family?: string;
    property?: string;
    automobile?: string;
    debt?: string;
    references?: string;
    outcome?: string;
  };
  reasonOfRejection: string;
  suggestedStatus?: string;
  statusReason?: string;
  homeImages?: {
    homeImagesFront?: string[];
    homeImagesGarage?: string[];
    homeImagesSurroundings?: string[];
  };
}

const palencaAccountRetrievalSchema = new Schema<PalencaAccountRetrievalMongoI>({
  status: {
    type: String,
    required: true,
  },
});

const palencaAccountSchema = new Schema<PalencaAccountMongoI>({
  accountId: {
    type: String,
    required: true,
  },
  platform: {
    type: String,
    required: true,
  },
  earnings: palencaAccountRetrievalSchema,
  metrics: palencaAccountRetrievalSchema,
  status: {
    type: String,
    required: true,
  },
});

const requestPalencaSchema = new Schema<RequestPalencaMongoI>({
  widgetId: {
    type: String,
    required: true,
  },
  externalId: {
    type: String,
    required: true,
  },
  accounts: [palencaAccountSchema],
});

const avalDataSchema = new Schema<RequestAvalDataMongoI>({
  name: {
    type: String,
  },
  phone: {
    type: String,
  },
  email: {
    type: String,
  },
  location: {
    type: String,
  },
});

const locationDataSchema = new Schema<RequestLocationDataMongoI>({
  latitude: {
    type: Number,
    required: true,
  },
  longitude: {
    type: Number,
    required: true,
  },
  location: {
    type: String,
    required: true,
  },
  ipAddress: {
    type: String,
    required: true,
  },
  isFromIP: {
    type: Boolean,
    required: true,
  },
});

const requestPersonalDataSchema = new Schema<RequestPersonalDataMongoI>({
  firstName: {
    type: String,
  },
  lastName: {
    type: String,
  },
  phone: {
    type: String,
  },
  email: {
    type: String,
  },
  status: {
    type: String,
    required: true,
  },
  birthdate: {
    type: String,
  },
  taxId: {
    type: String,
  },
  nationalId: {
    type: String,
  },
  postalCode: {
    type: String,
  },
  country: {
    type: String,
    enum: Object.values(Country),
    default: Country.mx,
  },
  city: {
    type: String,
  },
  state: {
    type: String,
  },
  neighborhood: {
    type: String,
  },
  street: {
    type: String,
  },
  streetNumber: {
    type: String,
  },
  department: {
    type: String,
  },
  vehicleSelected: {
    type: String,
  },

  ssn: {
    type: String,
    required: false,
  },

  rideShareTotalRides: {
    type: Number,
    required: false,
  },

  avgEarningPerWeek: {
    type: Number,
    required: false,
  },

  termsAndConditions: {
    type: Boolean,
    required: false,
  },

  dataPrivacyConsentForm: {
    type: Boolean,
    required: false,
  },

  nationality: {
    type: String,
    required: false,
  },

  age: {
    type: Number,
    required: false,
  },

  occupation: {
    type: String,
    required: false,
  },

  homePhone: {
    type: String,
    required: false,
  },

  timeInResidency: {
    type: String,
    required: false,
  },

  municipality: {
    type: String,
    required: false,
  },

  maritalStatus: {
    type: String,
    required: false,
    enum: Object.values(MaritalStatus),
    default: MaritalStatus.Single,
  },

  dependents: {
    type: String,
    required: false,
  },

  spouseOrPartnerIncome: {
    type: String,
    required: false,
  },

  partnerSourceOfIncome: {
    type: String,
    required: false,
  },

  partnerName: {
    type: String,
    required: false,
  },

  partnerPhone: {
    type: String,
    required: false,
  },

  noOfDependents: {
    type: Number,
    required: false,
  },

  dependendsInfo: {
    type: [
      {
        dependendName: {
          type: String,
          required: false,
        },
        dependendPhone: {
          type: String,
          required: false,
        },
        dependentRelationship: {
          type: String,
          required: false,
        },
      },
    ],
    required: false,
  },

  ownACar: {
    type: String,
    required: false,
  },

  carLeasingtime: {
    type: String,
    required: false,
  },

  carMake: {
    type: String,
    required: false,
  },

  carModel: {
    type: String,
    required: false,
  },

  ownDebt: {
    type: String,
    required: false,
  },

  outStandingDebt: {
    type: String,
    required: false,
  },

  doesDebtAffectPersonalFinance: {
    type: String,
    required: false,
  },

  references: {
    type: {
      reference1Name: {
        type: String,
        required: false,
      },
      reference1Phone: {
        type: String,
        required: false,
      },
      reference1Relationship: {
        type: String,
        required: false,
      },
      reference1Address: {
        type: String,
        required: false,
      },
      reference2Name: {
        type: String,
        required: false,
      },
      reference2Phone: {
        type: String,
        required: false,
      },
      reference2Relationship: {
        type: String,
        required: false,
      },
      reference2Address: {
        type: String,
        required: false,
      },
      reference3Name: {
        type: String,
        required: false,
      },
      reference3Phone: {
        type: String,
        required: false,
      },
      reference3Relationship: {
        type: String,
        required: false,
      },
      reference3Address: {
        type: String,
        required: false,
      },
    },
    required: false,
  },
  learnAboutOcn: {
    type: String,
    required: false,
  },

  referrer: {
    type: {
      name: {
        type: String,
        required: false,
      },
      email: {
        type: String,
        required: false,
      },
      phone: {
        type: String,
        required: false,
      },
      _id: {
        type: Schema.Types.ObjectId,
        required: false,
        select: false,
      },
    },
    required: false,
    default: undefined,
  },

  doesItApplyToElectricCars: {
    type: String,
    required: false,
  },

  ficoScore: {
    type: String,
    required: false,
  },

  criminalBackgroundCheck: {
    type: String,
    required: false,
  },

  motorVehicleRecordCheck: {
    type: String,
    required: false,
  },

  privacyPolicy: {
    type: Boolean,
    required: false,
    default: false,
  },

  ocnBackgroundAndCreditCheckForApplication: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const requestDocumentsDetailSchema = new Schema<RequestDocumentsMongoI>({
  mediaId: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
    required: false,
    default: null,
  },
  status: {
    type: String,
    required: false,
  },
  type: {
    type: String,
    required: false,
  },
  analysisResult: {
    status: {
      type: String,
      required: true,
      default: AnalysisStatus.pending,
    },
    extractedData: {
      type: Schema.Types.Mixed,
      required: false,
    },
    validationErrors: {
      type: [String],
      default: [],
    },
    validationWarnings: {
      type: [String],
      default: [],
    },
    confidence: {
      type: Number,
      required: false,
    },
  },
});

const requestDocumentAnalysisSchema = new Schema<RequestDocumentsAnalysisMongoI>({
  status: {
    type: String,
    required: true,
  },
  documents: [requestDocumentsDetailSchema],
});

const dailyEarningSchema = new Schema<DailyEarningMongoI>({
  amount: {
    type: Number,
    required: true,
  },
  countTrips: {
    type: Number,
    required: true,
  },
  earningDate: {
    type: Date,
    required: true,
  },
  currency: {
    type: String,
    required: true,
  },
});

const weeklyEarningsSchema = new Schema<WeeklyEarningMongoI>({
  totalAmount: {
    type: Number,
    required: true,
  },
  totalTrips: {
    type: Number,
    required: true,
  },
  fromDate: {
    type: Date,
    required: true,
  },
  toDate: {
    type: Date,
    required: true,
  },
  week: {
    type: Number,
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    required: true,
  },
  dailyEarnings: [dailyEarningSchema],
});

const requestEarningsAnalysisSchema = new Schema<EarningsAnalysisMongoI>({
  status: {
    type: String,
    required: true,
  },
  totalEarnings: {
    type: Number,
  },
  earnings: [weeklyEarningsSchema],
  platforms: {
    type: Number,
  },
  earningsNotificationSent: {
    type: Boolean,
    default: false,
  },
});

const requestHomeVisitSchema = new Schema<RequestHomeVisitMongoI>({
  status: {
    type: String,
    required: false,
    default: 'pending',
  },
  residentOwnershipStatus: {
    type: String,
    required: false,
    default: '',
  },
  comments: {
    type: String,
    required: false,
    default: '',
  },
  images: {
    type: [String],
    required: false,
    default: [],
  },
  visitDate: {
    type: Date,
    required: false,
    default: null,
  },
  visitTime: {
    type: String,
    required: false,
    default: '',
  },
  houseInformation: {
    ownProperty: {
      type: String,
      required: false,
      default: '',
    },
    nameOfOwner: {
      type: String,
      required: false,
      default: '',
    },
    ownerRelative: {
      type: String,
      required: false,
      default: '',
    },
    ownerRelativeRelation: {
      type: String,
      required: false,
      default: '',
    },
    ownerPhone: {
      type: String,
      required: false,
      default: '',
    },
    typeOfHousing: {
      type: String,
      required: false,
      default: '',
    },
    noOfBedrooms: {
      type: Number,
      required: false,
      default: '',
    },
    livingRoom: {
      type: String,
      required: false,
      default: '',
    },
    dinningRoom: {
      type: String,
      required: false,
      default: '',
    },
    kitchen: {
      type: String,
      required: false,
      default: '',
    },
    television: {
      type: String,
      required: false,
      default: '',
    },
    audioSystem: {
      type: String,
      required: false,
      default: '',
    },
    stove: {
      type: String,
      required: false,
      default: '',
    },
    refrigerator: {
      type: String,
      required: false,
      default: '',
    },
    washingMachine: {
      type: String,
      required: false,
      default: '',
    },
  },
  proofOfPropertyOwnership: {
    type: [String],
    required: false,
    default: [],
  },

  visitorEmailAddress: {
    type: String,
    required: false,
    default: '',
  },

  doesProofOfAddressMatchLocation: {
    type: String,
    required: false,
    default: '',
  },

  characteristicsOfGarage: {
    type: String,
    required: false,
    default: '',
  },

  behaviourOfCustomerDuringCall: {
    type: String,
    required: false,
    default: '',
  },

  homeVisitStepsStatus: {
    personal: {
      type: String,
      required: false,
      default: '',
    },
    contact: {
      type: String,
      required: false,
      default: '',
    },
    address: {
      type: String,
      required: false,
      default: '',
    },
    family: {
      type: String,
      required: false,
      default: '',
    },
    property: {
      type: String,
      required: false,
      default: '',
    },
    automobile: {
      type: String,
      required: false,
      default: '',
    },
    debt: {
      type: String,
      required: false,
      default: '',
    },
    references: {
      type: String,
      required: false,
      default: '',
    },
    outcome: {
      type: String,
      required: false,
      default: '',
    },
  },

  reasonOfRejection: {
    type: String,
    required: false,
    default: '',
  },

  suggestedStatus: {
    type: String,
    required: false,
    default: '',
  },

  statusReason: {
    type: String,
    required: false,
    default: '',
  },

  homeImages: {
    homeImagesFront: {
      type: [String],
      required: false,
      default: [],
    },
    homeImagesGarage: {
      type: [String],
      required: false,
      default: [],
    },
    homeImagesSurroundings: {
      type: [String],
      required: false,
      default: [],
    },
  },
});

const modelResultSchema = new Schema<ModelResultMongoI>({
  modelName: {
    type: String,
    enum: Object.values(MLModels),
    required: false,
  },
  status: {
    type: String,
    default: AnalysisStatus.pending,
    required: true,
  },
  modelScore: {
    type: Number,
    default: 0,
    required: true,
  },
  modelFeatureScores: {
    type: Object,
    default: {},
    required: true,
  },
  modelWeights: {
    type: Object,
    default: {},
    required: true,
  },
  modelResult: {
    type: Object,
    default: {},
    required: true,
  },
});

const modelScoresSchema = new Schema<ModelScoresMongoI>({
  [MLModels.RIDESHARE_PERFORMANCE]: modelResultSchema,
  [MLModels.PERSONAL_INFORMATION]: modelResultSchema,
  [MLModels.HOMEVISIT_INFORMATION]: modelResultSchema,
  [MLModels.FINANCIAL_ASSESSMENT]: modelResultSchema,
  // [MLModels.DRIVING_AND_LEGAL_HISTORY]: modelResultSchema,
});

const scorecardCategorySchema = new Schema<ScorecardCategoryMongoI>({
  threshold: {
    type: Schema.Types.Mixed,
    required: true,
  },
  riskCategory: {
    type: String,
    required: true,
  },
  riskScore: {
    type: Number,
    required: true,
  },
  weight: {
    type: Number,
    required: true,
  },
  result: {
    type: Number,
    required: true,
  },
});

const scorecardDetailSchema = new Schema<ScorecardDetailMongoI>({
  variable: {
    type: String,
    required: true,
  },
  category: scorecardCategorySchema,
});

const scorecardSchema = new Schema<ScorecardMongoI>({
  totalScore: {
    type: Number,
    required: true,
  },
  scaledScore: {
    type: Number,
    required: true,
  },
  minScore: {
    type: Number,
    required: true,
  },
  maxScore: {
    type: Number,
    required: true,
  },
  minScaledScore: {
    type: Number,
    required: true,
  },
  maxScaledScore: {
    type: Number,
    required: true,
  },
  details: [scorecardDetailSchema],
});

const riskAnalysisSchema = new Schema<RiskAnalysisMongoI>({
  status: {
    type: String,
    required: true,
  },
  scorecardVersion: {
    type: String,
    required: true,
  },
  scorecard: scorecardSchema,
});

export interface RequestAvalDataMongoI extends Document {
  name: string;
  phone: string;
  email: string;
  location: string;
}

export interface RequestLocationDataMongoI extends Document {
  latitude: number;
  longitude: number;
  location: string;
  ipAddress: string;
  isFromIP: boolean;
}

const admissionRequestSchema = new Schema<AdmissionRequestMongoI>(
  {
    status: {
      type: String,
      enum: Object.values(AdmissionRequestStatus),
      required: true,
    },
    convertedToAssociate: {
      type: Boolean,
      default: false,
    },
    rejectionReason: {
      type: String,
      required: false,
    },
    documentsAnalysis: requestDocumentAnalysisSchema,
    firstBlockVerifier: {
      type: Boolean,
      default: false,
    },
    personalData: requestPersonalDataSchema,
    palenca: requestPalencaSchema,
    homeVisit: requestHomeVisitSchema,
    earningsAnalysis: requestEarningsAnalysisSchema,
    modelScores: modelScoresSchema,
    riskAnalysis: riskAnalysisSchema,
    typeOfPreapproval: {
      type: String,
      default: null,
    },
    suggestedTypeOfPreapproval: {
      type: String,
      default: null,
    },
    preApprovalReason: {
      type: String,
      required: false,
    },
    hubspot: {
      type: Object,
      id: {
        type: String,
        required: false,
      },
    },
    hubspotDeal: {
      type: Object,
      id: {
        type: String,
        required: false,
      },
    },
    hilos: {
      id: {
        type: String,
        required: false,
      },
      conversation: {
        type: String,
        required: false,
      },
      contact: {
        type: String,
        required: false,
      },
    },
    video: {
      type: Object,
    },
    screenshots: {
      type: Object,
    },
    isRequestLinkOpened: {
      type: Boolean,
      default: false,
    },

    isImported: {
      type: Boolean,
      default: false,
    },

    associateId: {
      type: Schema.Types.ObjectId,
      required: false,
    },

    openLinkDates: {
      type: Array,
    },
    source: {
      type: String,
      default: null,
    },
    clientIpAddress: {
      type: String,
      default: null,
    },
    homeVisitScheduleLinkSendDate: {
      type: Date,
      default: null,
    },
    avalData: avalDataSchema,
    locationData: locationDataSchema,
    agentId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  { timestamps: true }
);

export const AdmissionRequestMongo = model<AdmissionRequestMongoI>(
  'AdmissionRequest',
  admissionRequestSchema,
  'admissionRequests'
);
