import { VendorAuthService } from '../services/auth.service';
import { SignInDto } from '../dtos/sign-in.dto';
import { AsyncController } from '@/types&interfaces/types';
import { logger } from '@/clean/lib/logger';
import { vendorPlatformAccessTokenSecret } from '@/vendor-platform/constants';
import CompanyUserPermissions from '../../company/models/company-user-permissions.model';

const authService = new VendorAuthService();

export const signInVendor: AsyncController = async (req, res) => {
  try {
    const { email, password } = SignInDto.parse(req.body);

    const user = await authService.findUserByEmail(email);
    if (!user) return res.status(404).send({ message: 'Email or password is incorrect' });

    const isValidPassword = await authService.validatePassword(password, user.password!);
    if (!isValidPassword) return res.status(404).send({ message: 'Email or password is incorrect' });

    const { token, expires } = authService.generateAccessToken(
      {
        email,
        userId: user._id.toString(),
        role: user.roles[0],
        organizationId: user.organizationId?.toString() || '',
      },
      vendorPlatformAccessTokenSecret
    );
    res.setHeader('expires', expires);

    delete user.password;
    let permissions;
    let companyId;
    if (user.userType === 'company' || user.userType === 'company-gestor' || user.userType === 'superAdmin') {
      permissions = await CompanyUserPermissions.findOne({ userId: user._id })
        // .populate('company')
        .populate('cities')
        .populate('crews');

      companyId = permissions?.companyId?.toString();
    }

    return res.status(200).send({
      message: 'Vendor logged in successfully',
      accessToken: token,
      user: {
        ...user.toObject(),
        password: undefined,
        permissions, // Agregar los permisos al objeto de usuario
        companyId,
      },
    });
  } catch (error: any) {
    logger.info({
      message: '[signInVendor] An unexpected error occurred',
      stack: error.stack,
    });
    console.log('error', error);
    return res.status(500).send({ message: 'Internal server error' });
  }
};

export const verifyInvitationToken: AsyncController = async (req, res) => {
  try {
    const { token } = req.body as { token: string };
    if (!token) return res.status(400).send({ message: 'Not allowed' });

    const decoded = authService.verifyToken(token);
    if (!decoded) return res.status(400).send({ message: 'Not allowed' });

    return res.status(200).send({ message: 'Token verified' });
  } catch (error) {
    return res.status(500).send({ message: 'Internal server error' });
  }
};

export const completeRegister: AsyncController = async (req, res) => {
  try {
    const { email, password } = req.body as { email: string; password: string };

    const isPasswordUpdated = await authService.updatePassword(email, password);
    if (!isPasswordUpdated) return res.status(404).send({ message: 'User not found' });

    return res.status(200).send({ message: 'User password registered successfully' });
  } catch (error: any) {
    logger.info({
      message: '[completeRegister] An unexpected error occurred',
      stack: error.stack,
    });

    return res.status(500).send({ message: 'Internal server error' });
  }
};
