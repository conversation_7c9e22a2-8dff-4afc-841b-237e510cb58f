import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const wire4Webhook = new Schema({
  body: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
wire4Webhook.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const Wire4Webhook = model('Wire4Webhook', wire4Webhook);

export default Wire4Webhook;
