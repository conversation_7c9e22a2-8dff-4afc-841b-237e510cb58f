import mongoose, { Schema, Document } from 'mongoose';
import { PhysicalVehicleStatus } from './StockVehicleSchema';

export enum QRScanHistoryActionType {
  SCAN = 'SCAN',
  STATUS_CHANGE = 'STATUS_CHANGE',
}

export interface IQRScanHistory extends Document {
  vehicleId: mongoose.Schema.Types.ObjectId;
  userId: mongoose.Schema.Types.ObjectId;
  scanTime: Date;
  statusChangedFrom: string;
  statusChangedTo: string;
  location?: string;
  deviceInfo?: string;
  actionToken?: string;
  actionType: QRScanHistoryActionType;
  notes?: string;
  photo?: mongoose.Schema.Types.ObjectId;
  createdAt: Date;
  vendorRegion?: string;
  vendorWorkshopName?: string;
  isAdminCorrection?: boolean;
  vendorUserName?: string;
}

const qrScanHistorySchema = new Schema<IQRScanHistory>(
  {
    vehicleId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'StockVehicle',
      required: true,
      index: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    scanTime: {
      type: Date,
      default: Date.now,
      required: true,
    },
    statusChangedFrom: {
      type: String,
      enum: Object.values(PhysicalVehicleStatus),
      required: true,
    },
    statusChangedTo: {
      type: String,
      enum: Object.values(PhysicalVehicleStatus),
      required: true,
    },
    location: {
      type: String,
      required: false,
    },
    deviceInfo: {
      type: String,
      required: false,
    },
    actionToken: {
      type: String,
      required: false,
    },
    actionType: {
      type: String,
      enum: Object.values(QRScanHistoryActionType),
      required: true,
    },
    notes: {
      type: String,
      required: false,
    },
    photo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Document',
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    vendorRegion: {
      type: String,
      required: false,
    },
    vendorWorkshopName: {
      type: String,
      required: false,
    },
    isAdminCorrection: {
      type: Boolean,
      required: false,
      default: false,
    },
    vendorUserName: {
      // This is the user name of the vendor who is correcting the status because the user is in a different db, we can't use populate
      type: String,
      required: false,
    },
  },
  { timestamps: true }
);

// Create indexes for better query performance
qrScanHistorySchema.index({ vehicleId: 1, scanTime: -1 });
qrScanHistorySchema.index({ userId: 1 });

const QRScanHistory = mongoose.model<IQRScanHistory>('QRScanHistory', qrScanHistorySchema);

export default QRScanHistory;
