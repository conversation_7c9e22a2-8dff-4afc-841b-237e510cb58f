import { AllowedMimeType, parseTextFromSource, Source } from './llmClients/geminiClient';
import { logger } from '@/clean/lib/logger';

const prompt = `Extract text from the provided Didi profile screenshot and output a structured JSON object using the following JSON schema:

{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "fullName": {
      "type": "string",
      "description": "The name of the driver."
    },
    "acceptanceRate": {
      "type": "integer",
      "description": "The acceptance rate of the driver in percentage.",
      "minimum": 0,
      "maximum": 100
    },
    "completedTripsRate": {
      "type": "integer",
      "description": "The rate of completed trips by the driver in percentage.",
      "minimum": 0,
      "maximum": 100
    },
    "rating": {
      "type": "number",
      "description": "The average star rating of the driver.",
      "minimum": 0,
      "maximum": 5
    },
    "registrationCity": {
      "type": "string",
      "description": "The city where the driver is registered."
    },
    "completedTripsThisWeek": {
      "type": "integer",
      "description": "The number of trips completed by the driver this week.",
      "minimum": 0
    },
    "yearsActive": {
      "type": "integer",
      "description": "The number of years the driver has been active.",
      "minimum": 0
    },
    "passengersServed": {
      "type": "integer",
      "description": "The total number of passengers served by the driver.",
      "minimum": 0
    }
  },
  "required": [
    "fullName",
    "acceptanceRate",
    "completedTripsRate",
    "rating",
    "registrationCity",
    "completedTripsThisWeek",
    "yearsActive",
    "passengersServed"
  ],
  "additionalProperties": false
}
Additional requirements:
- Handle any language input and translate to english
- Return null for any fields not present in the image
- Preserve any special characters in names
- Format dates in ISO 8601 format (YYYY-MM-DD) when possible
- If time using app is a range output the higher range value along with month or year example "9 months" or "2 years" 
- Do not include any thing else in the response other than that above JSON structure
- The response should be a valid JSON Object
- Do not use Markdown code block syntax in response`;

const getDidiProfileData = async (image: Express.Multer.File) => {
  try {
    // Validate the image object
    if (!image || !image.buffer || !image.mimetype) {
      throw new Error('Invalid image file provided');
    }

    // Validate MIME type
    const allowedMimeTypes: AllowedMimeType[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedMimeTypes.includes(image.mimetype as AllowedMimeType)) {
      throw new Error(`Unsupported MIME type: ${image.mimetype}`);
    }

    // Prepare the source object
    const source: Source = {
      data: image.buffer.toString('base64'),
      media_type: image.mimetype as AllowedMimeType,
      prompt: prompt,
    };

    logger.info(`[getDidiProfileData] - Processing image with MIME type: ${source.media_type}`);

    // Call the Anthropic API to parse the image
    const result = await parseTextFromSource(source);
    return result;
  } catch (error) {
    logger.error(`[getDidiProfileData] - Error: ${JSON.stringify(error)}`);
    throw new Error(`Failed to extract profile data: ${JSON.stringify(error)}`);
  }
};

export { getDidiProfileData };
