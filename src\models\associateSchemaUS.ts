import { Schema, model } from 'mongoose';
import mt from 'moment-timezone';

const timeZone = 'America/Mexico_City';

function getCurrentDateTime() {
  return mt().tz(timeZone).toDate();
}

const associateSchemaUS = new Schema({
  associate: {
    type: Schema.Types.ObjectId,
    ref: 'Associate',
    required: true,
    unique: true,
  },
  city: {
    type: String,
    required: [true, 'City is required'],
  },
  state: {
    type: String,
    required: [true, 'State is required'],
  },
  documents: {
    addressVerification: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    driverLicenseFront: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    driverLicenseBack: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
      default: null,
    },
    garage: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    proofOfCompletionOfAnyRequiredSafetyCourses: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    drivingRecord: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    rideShareDates: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    rideShareRideHistory: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    avgWeeklyIncomeOfLastTwelveWeeks: {
      type: [
        {
          type: Schema.Types.ObjectId,
          ref: 'Documents',
          default: null,
        },
      ],
    },
    signature: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
  },
  bankStatement: {
    bankStatementOne: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementTwo: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementThree: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementFour: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementFive: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
    bankStatementSix: {
      type: Schema.Types.ObjectId,
      ref: 'Documents',
      default: null,
    },
  },
  contacts: {
    type: [
      {
        emergencyContactName: {
          type: String,
          required: true,
        },
        emergencyContactPhone: {
          type: String,
          required: true,
        },
        emergencyContactRelation: {
          type: String,
          required: true,
        },
      },
    ],
    default: [],
    required: false,
  },

  ssn: {
    type: String,
    default: null,
  },

  rideShareTotalRides: {
    type: Number,
    default: null,
  },

  avgEarningPerWeek: {
    type: Number,
    default: null,
  },

  mobilityPlatforms: {
    type: Array,
    default: [],
  },

  termsAndConditions: {
    type: Boolean,
    default: false,
  },

  dataPrivacyConsentForm: {
    type: Boolean,
    default: false,
  },

  createdAt: { type: String, default: getCurrentDateTime },
  updatedAt: { type: String, default: getCurrentDateTime },
});

const AssociateUS = model('AssociateUS', associateSchemaUS);

export default AssociateUS;
