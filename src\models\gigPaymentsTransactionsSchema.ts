import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const gigPaymentsTransaction = new Schema({
  monexData: {
    type: Object,
  },
  gigstackData: {
    type: Object,
  },
  gigstackApiResponse: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
gigPaymentsTransaction.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const GigPaymentsTransaction = model('GigPaymentsTransaction', gigPaymentsTransaction);

export default GigPaymentsTransaction;
