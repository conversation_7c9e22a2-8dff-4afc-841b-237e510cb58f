import { Schema, Document, Types } from 'mongoose';
import vendorDB from '@/vendor-platform/db';

export enum CorrectiveServiceStatus {
  NOT_STARTED = 'not-started',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  WAITING_FOR_PARTS = 'waiting-for-parts',
  CANCELLED = 'cancelled',
}

export enum CorrectiveServiceType {
  BRAKES = 'brakes',
  TIRES = 'tires',
  SUSPENSION = 'suspension',
  ENGINE = 'engine',
  TRANSMISSION = 'transmission',
  ELECTRICAL = 'electrical',
  BODYWORK = 'bodywork',
  CLUTCH = 'clutch',
  COOLING_SYSTEM = 'cooling-system',
  EXHAUST = 'exhaust',
  FUEL_SYSTEM = 'fuel-system',
  STEERING = 'steering',
  OTHER = 'other',
}

export interface IPart {
  name: string;
  partNumber?: string;
  quantity: number;
  // unitCost: number; // Removed - not needed for vendor platform
  totalCost: number;
  supplier: string;
  estimatedArrival?: Date;
  actualArrival?: Date;
  isAvailable: boolean;

  // Enhanced inventory tracking
  availableQuantity?: number;
  needsOrdering?: boolean;
  inventoryStatus?: 'available' | 'low-stock' | 'out-of-stock' | 'pending-order';
  alternativePartNumbers?: string[];
  reservationId?: string; // For tracking reserved inventory
}

export interface ICorrectiveService extends Document {
  _id: Types.ObjectId;
  orderId: Types.ObjectId;

  // Service details
  serviceType: CorrectiveServiceType;
  serviceName: string;
  description: string;
  status: CorrectiveServiceStatus;

  // Cost and time estimates
  estimatedCost: number;
  actualCost?: number;
  estimatedDuration: number; // in hours
  actualDuration?: number;

  // Parts required
  parts: IPart[];
  totalPartsCost: number;
  // laborCost: number; // Removed - not needed for vendor platform

  // Scheduling and timing
  scheduledStartTime?: Date;
  actualStartTime?: Date;
  scheduledEndTime?: Date;
  actualEndTime?: Date;

  // Real-time tracking
  currentPhase?: string; // e.g., 'diagnosis', 'parts-preparation', 'work-in-progress', 'quality-check'
  phaseStartTime?: Date;
  estimatedCompletionTime?: Date;
  timeSpentMinutes?: number;
  pausedTime?: number; // Total time paused in minutes
  isPaused?: boolean;
  pauseReason?: string;
  lastActivityTime?: Date;

  // Parts availability
  allPartsAvailable: boolean;
  missingParts: string[];
  partsETA?: Date;

  // Approval
  isApproved: boolean;
  approvedAt?: Date;
  rejectedAt?: Date;
  rejectionReason?: string;

  // Evidence and documentation
  beforePhotos: string[];
  progressPhotos: string[];
  afterPhotos: string[];
  videos: string[];

  // Technical details
  technicalNotes: string[];
  qualityCheckPassed: boolean;

  // Enhanced evidence tracking
  evidenceByPhase?: {
    phase: string;
    photos: string[];
    videos: string[];
    notes: string;
    timestamp: Date;
    technician?: string;
  }[];

  // Service completion details
  completionEvidence?: {
    photos: string[];
    videos: string[];
    notes: string;
    qualityCheckPhotos?: string[];
    customerSignature?: string;
  };
  warrantyPeriod?: number; // in days

  // SLA tracking
  slaTarget: Date;
  slaActual?: Date;
  slaCompliance: boolean;

  createdAt: Date;
  updatedAt: Date;
}

const PartSchema = new Schema<IPart>(
  {
    name: { type: String, required: true },
    partNumber: { type: String },
    quantity: { type: Number, required: true, min: 1 },
    // unitCost: { type: Number, required: true, min: 0 }, // Removed
    totalCost: { type: Number, required: true, min: 0 },
    supplier: { type: String, required: true },
    estimatedArrival: { type: Date },
    actualArrival: { type: Date },
    isAvailable: { type: Boolean, default: false },

    // Enhanced inventory tracking
    availableQuantity: { type: Number, min: 0 },
    needsOrdering: { type: Boolean, default: false },
    inventoryStatus: {
      type: String,
      enum: ['available', 'low-stock', 'out-of-stock', 'pending-order'],
      default: 'available',
    },
    alternativePartNumbers: [{ type: String }],
    reservationId: { type: String },
  },
  { _id: false }
);

const CorrectiveServiceSchema = new Schema<ICorrectiveService>(
  {
    orderId: {
      type: Schema.Types.ObjectId,
      ref: 'CorrectiveMaintenanceOrder',
      required: true,
      index: true,
    },

    serviceType: {
      type: String,
      enum: Object.values(CorrectiveServiceType),
      required: true,
    },
    serviceName: { type: String, required: true },
    description: { type: String, required: true },
    status: {
      type: String,
      enum: Object.values(CorrectiveServiceStatus),
      default: CorrectiveServiceStatus.NOT_STARTED,
      index: true,
    },

    estimatedCost: { type: Number, required: true, min: 0 },
    actualCost: { type: Number, min: 0 },
    estimatedDuration: { type: Number, required: true, min: 0 },
    actualDuration: { type: Number, min: 0 },

    parts: [PartSchema],
    totalPartsCost: { type: Number, default: 0, min: 0 },
    // laborCost: { type: Number, required: true, min: 0 }, // Removed

    scheduledStartTime: { type: Date },
    actualStartTime: { type: Date },
    scheduledEndTime: { type: Date },
    actualEndTime: { type: Date },

    // Real-time tracking
    currentPhase: { type: String },
    phaseStartTime: { type: Date },
    estimatedCompletionTime: { type: Date },
    timeSpentMinutes: { type: Number, default: 0 },
    pausedTime: { type: Number, default: 0 },
    isPaused: { type: Boolean, default: false },
    pauseReason: { type: String },
    lastActivityTime: { type: Date },

    allPartsAvailable: { type: Boolean, default: false },
    missingParts: [{ type: String }],
    partsETA: { type: Date },

    isApproved: { type: Boolean, default: false },
    approvedAt: { type: Date },
    rejectedAt: { type: Date },
    rejectionReason: { type: String },

    beforePhotos: [{ type: String }],
    progressPhotos: [{ type: String }],
    afterPhotos: [{ type: String }],
    videos: [{ type: String }],

    technicalNotes: [{ type: String }],
    qualityCheckPassed: { type: Boolean, default: false },

    // Enhanced evidence tracking
    evidenceByPhase: [
      {
        phase: { type: String, required: true },
        photos: [{ type: String }],
        videos: [{ type: String }],
        notes: { type: String },
        timestamp: { type: Date, default: Date.now },
        technician: { type: String },
      },
    ],

    // Service completion details
    completionEvidence: {
      photos: [{ type: String }],
      videos: [{ type: String }],
      notes: { type: String },
      qualityCheckPhotos: [{ type: String }],
      customerSignature: { type: String },
    },
    warrantyPeriod: { type: Number }, // days

    slaTarget: { type: Date, required: true },
    slaActual: { type: Date },
    slaCompliance: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
CorrectiveServiceSchema.index({ orderId: 1, status: 1 });
CorrectiveServiceSchema.index({ serviceType: 1 });
CorrectiveServiceSchema.index({ isApproved: 1, status: 1 });
CorrectiveServiceSchema.index({ allPartsAvailable: 1 });

// Pre-save middleware to calculate total parts cost
CorrectiveServiceSchema.pre('save', function (next) {
  if (this.parts && this.parts.length > 0) {
    this.totalPartsCost = this.parts.reduce((total, part) => total + part.totalCost, 0);
    this.allPartsAvailable = this.parts.every((part) => part.isAvailable);
    this.missingParts = this.parts.filter((part) => !part.isAvailable).map((part) => part.name);
  }
  next();
});

export const CorrectiveService = vendorDB.model<ICorrectiveService>(
  'CorrectiveService',
  CorrectiveServiceSchema
);
