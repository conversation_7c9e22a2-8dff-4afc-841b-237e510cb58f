import { Request, Response, NextFunction } from 'express';
import { Types } from 'mongoose';
import { FleetOrdersService } from '../services/fleet-orders.service';
import { FleetOrderStatus } from '../models/fleet-order.model';

const fleetOrdersService = new FleetOrdersService();

/**
 * Middleware para verificar si el usuario puede acceder a una orden específica
 */
export const checkFleetOrderAccess = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id, orderId } = req.params;
    const orderIdToCheck = id || orderId;
    const userId = req.userVendor?.userId;
    const userType = req.userVendor?.userType;

    if (!userId) {
      return res.status(401).json({
        message: 'Usuario no autenticado',
        code: 'UNAUTHORIZED'
      });
    }

    if (!orderIdToCheck || !Types.ObjectId.isValid(orderIdToCheck)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    // Los usuarios OCN pueden acceder a cualquier orden
    if (userType === 'ocn') {
      return next();
    }

    // Verificar que la orden existe
    const order = await fleetOrdersService.getFleetOrderById(new Types.ObjectId(orderIdToCheck));
    
    if (!order) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Para otros tipos de usuario, verificar permisos específicos
    // Por ahora, solo usuarios OCN pueden gestionar Fleet Orders
    return res.status(403).json({
      message: 'Solo usuarios OCN pueden acceder a las órdenes de flota',
      code: 'INSUFFICIENT_PERMISSIONS'
    });

  } catch (error) {
    console.error('Error checking fleet order access:', error);
    return res.status(500).json({
      message: 'Error interno del servidor al verificar acceso',
      code: 'ACCESS_CHECK_ERROR'
    });
  }
};

/**
 * Middleware para verificar si se puede modificar una orden basado en su estado
 */
export const checkFleetOrderModifiable = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    if (!id || !Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    const order = await fleetOrdersService.getFleetOrderById(new Types.ObjectId(id));
    
    if (!order) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Las órdenes entregadas no se pueden modificar
    if (order.status === FleetOrderStatus.DELIVERED) {
      return res.status(400).json({
        message: 'No se puede modificar una orden que ya fue entregada',
        code: 'ORDER_ALREADY_DELIVERED'
      });
    }

    // Agregar la orden al request para uso posterior
    (req as any).fleetOrder = order;
    
    next();
  } catch (error) {
    console.error('Error checking if fleet order is modifiable:', error);
    return res.status(500).json({
      message: 'Error interno del servidor',
      code: 'MODIFIABLE_CHECK_ERROR'
    });
  }
};

/**
 * Middleware para verificar si se puede eliminar una orden
 */
export const checkFleetOrderDeletable = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    if (!id || !Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    const order = await fleetOrdersService.getFleetOrderById(new Types.ObjectId(id));
    
    if (!order) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Solo se pueden eliminar órdenes en estado CREATED
    if (order.status !== FleetOrderStatus.CREATED) {
      return res.status(400).json({
        message: 'Solo se pueden eliminar órdenes en estado "created"',
        code: 'ORDER_NOT_DELETABLE',
        currentStatus: order.status
      });
    }

    // Agregar la orden al request para uso posterior
    (req as any).fleetOrder = order;
    
    next();
  } catch (error) {
    console.error('Error checking if fleet order is deletable:', error);
    return res.status(500).json({
      message: 'Error interno del servidor',
      code: 'DELETABLE_CHECK_ERROR'
    });
  }
};

/**
 * Middleware para verificar si se puede actualizar la dispersión
 */
export const checkDispersionUpdateAllowed = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    if (!id || !Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        message: 'ID de orden inválido',
        code: 'INVALID_ORDER_ID'
      });
    }

    const order = await fleetOrdersService.getFleetOrderById(new Types.ObjectId(id));
    
    if (!order) {
      return res.status(404).json({
        message: 'Orden no encontrada',
        code: 'ORDER_NOT_FOUND'
      });
    }

    // Solo se puede actualizar la dispersión cuando la orden está en estado SENT
    if (order.status !== FleetOrderStatus.SENT) {
      return res.status(400).json({
        message: 'Solo se puede actualizar la dispersión cuando la orden está en estado "sent"',
        code: 'INVALID_STATUS_FOR_DISPERSION',
        currentStatus: order.status,
        requiredStatus: FleetOrderStatus.SENT
      });
    }

    // Agregar la orden al request para uso posterior
    (req as any).fleetOrder = order;
    
    next();
  } catch (error) {
    console.error('Error checking dispersion update permission:', error);
    return res.status(500).json({
      message: 'Error interno del servidor',
      code: 'DISPERSION_UPDATE_CHECK_ERROR'
    });
  }
};

/**
 * Middleware para validar que existe una orden para el mes/año antes de crear una nueva
 */
export const checkOrderExistsForPeriod = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { month, year } = req.body;

    if (!month || !year) {
      return res.status(400).json({
        message: 'Mes y año son requeridos',
        code: 'MISSING_PERIOD_DATA'
      });
    }

    // Buscar si ya existe una orden para este período
    const existingOrders = await fleetOrdersService.listFleetOrders({
      month: Number(month),
      year: Number(year),
      limit: 1
    });

    if (existingOrders.orders.length > 0) {
      return res.status(409).json({
        message: `Ya existe una orden para el período ${month}/${year}`,
        code: 'ORDER_ALREADY_EXISTS_FOR_PERIOD',
        existingOrder: existingOrders.orders[0]
      });
    }

    next();
  } catch (error) {
    console.error('Error checking order existence for period:', error);
    return res.status(500).json({
      message: 'Error interno del servidor',
      code: 'PERIOD_CHECK_ERROR'
    });
  }
};

/**
 * Middleware para validar fechas de creación de órdenes
 */
export const validateOrderCreationTiming = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { month, year } = req.body;

    if (!month || !year) {
      return res.status(400).json({
        message: 'Mes y año son requeridos',
        code: 'MISSING_PERIOD_DATA'
      });
    }

    const now = new Date();
    const currentMonth = now.getMonth() + 1; // getMonth() returns 0-11
    const currentYear = now.getFullYear();
    const currentDay = now.getDate();

    // Validar que no se esté creando una orden para un mes pasado
    if (year < currentYear || (year === currentYear && month < currentMonth)) {
      return res.status(400).json({
        message: 'No se pueden crear órdenes para períodos pasados',
        code: 'PAST_PERIOD_NOT_ALLOWED',
        requestedPeriod: `${month}/${year}`,
        currentPeriod: `${currentMonth}/${currentYear}`
      });
    }

    // Advertir si se está creando muy tarde en el mes
    if (year === currentYear && month === currentMonth && currentDay > 5) {
      console.warn(`⚠️ Orden creada tarde: día ${currentDay} del mes ${month}/${year}`);
      // No bloquear, solo advertir
    }

    next();
  } catch (error) {
    console.error('Error validating order creation timing:', error);
    return res.status(500).json({
      message: 'Error interno del servidor',
      code: 'TIMING_VALIDATION_ERROR'
    });
  }
};
