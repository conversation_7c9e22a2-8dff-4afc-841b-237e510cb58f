import jwt from 'jsonwebtoken';
import ExpiredToken from '../models/expiredTokens';

type BlackList = {
  [key: string]: boolean;
};

// Esto es un objeto donde se guardaran tokens invalidados manualmente
// solo para casos donde se cierre sesión antes de tiempo y el token siga siendo valido
export const blacklist: BlackList = {};

export const addAndInvalidateToken = (token: string) => {
  const decodedToken = jwt.decode(token) as jwt.JwtPayload;
  const expiration = decodedToken.exp as number;
  const timeRemainingInSeconds = Math.floor((expiration * 1000 - Date.now()) / 1000);
  if (timeRemainingInSeconds > 0) {
    // verifica si el token aún no ha expirado
    blacklist[token] = true;
    setTimeout(() => {
      delete blacklist[token];
    }, timeRemainingInSeconds * 1000);
  }
};

export const addAndInvalidateTokenDB = async (token: string) => {
  const decodedToken = jwt.decode(token) as jwt.JwtPayload;
  const expiration = decodedToken.exp as number;
  const timeRemainingInSeconds = Math.floor((expiration * 1000 - Date.now()) / 1000);
  if (timeRemainingInSeconds > 0) {
    // verifica si el token aún no ha expirado
    const tokenDB = new ExpiredToken({
      token,
    });

    await tokenDB.save();

    await new Promise((resolve) => {
      setTimeout(async () => {
        const res = await tokenDB.remove();
        resolve(res); // Resuelve la promesa después de ejecutar el tokenDB.remove()
        console.log('eliminado', res);
      }, timeRemainingInSeconds * 1000);
    });
  }
};
