import { AsyncController } from '@/types&interfaces/types';
import { InstallationScheduleService } from '../services/installation-schedule.service';
import { z } from 'zod';
import { updateInstallationAppointmentDto } from '../dtos/update-installation-appointment.dto';

export const getAvailableInstallationSlots: AsyncController = async (req, res) => {
  try {
    const { neighborhoodId, date } = req.params;

    const slots = await InstallationScheduleService.getAvailableSlots({
      neighborhoodId,
      date,
    });
    return res.status(200).send({ message: 'Available slots found', data: slots });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

// validate are correct mongo ids with regex
const validateObjectId = z.string().regex(/^[0-9a-fA-F]{24}$/);

const createInstallationAppointmentDto = z.object({
  neighborhoodId: validateObjectId,
  startTime: z.string(),
  associateId: validateObjectId,
  stockId: validateObjectId,
});

export const createInstallationAppointment: AsyncController = async (req, res) => {
  try {
    const { neighborhoodId, startTime, associateId, stockId } = createInstallationAppointmentDto.parse(
      req.body
    );
    const address = req.body.address;

    const appointment = await InstallationScheduleService.createAppointment({
      stockId,
      neighborhoodId,
      startTime,
      associateId,
      address,
    });

    return res.status(201).send({ message: 'Appointment created', data: appointment });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const getInstallationAppointments: AsyncController = async (req, res) => {
  try {
    const { startDate, endDate, cityId, crewId, neighborhoodId } = req.query as {
      startDate?: string;
      endDate?: string;
      cityId?: string;
      crewId?: string;
      neighborhoodId?: string;
    };
    const appointments = await InstallationScheduleService.getInstallationAppointments(
      // req.query.startDate as string,
      // req.query.endDate as string
      {
        startDate,
        endDate,
        cityId,
        crewId,
        neighborhoodId,
        userId: req.userVendor.userId,
      }
    );
    return res.status(200).send({ message: 'Appointments found', data: appointments });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const updateInstallationAppointmentProof: AsyncController = async (req, res) => {
  try {
    const { appointmentId } = req.params;
    const { source, comments } = updateInstallationAppointmentDto.parse(req.body);
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.status(400).send({
        success: false,
        message: 'No files were uploaded',
      });
    }

    const appointment = await InstallationScheduleService.updateAppointmentWithProofImages({
      appointmentId,
      files: req.files,
      source,
      comments,
    });

    return res.status(200).send({
      success: true,
      message: `Proof images uploaded successfully by ${source}`,
      data: appointment,
    });
  } catch (error: any) {
    return res.status(400).send({
      success: false,
      message: error.message,
    });
  }
};

export const rescheduleInstallationAppointment: AsyncController = async (req, res) => {
  try {
    const { appointmentId } = req.params;
    const { startTime, neighborhoodId } = req.body;
    const vendorUser = req.userVendor?.userId || req.userReq?.userId;

    // neighborhoodId is optional, but should only receive it if the user is a vendor or from admin
    if (neighborhoodId && !vendorUser) {
      return res.status(401).send({
        message: 'Unauthorized',
      });
    }

    const appointment = await InstallationScheduleService.rescheduleAppointment({
      appointmentId,
      newStartTime: startTime,
      neighborhoodId,
      vendorUserId: req.userVendor?.userId,
      adminUserId: req.userReq?.userId,
    });

    return res.status(200).send({
      message: 'Installation appointment rescheduled successfully',
      data: appointment,
    });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const sendOneHourReminder: AsyncController = async (req, res) => {
  try {
    const { appointmentId } = req.params;
    await InstallationScheduleService.sendOneHourReminder(appointmentId);
    return res.status(200).send({ message: 'One hour reminder sent successfully' });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const sendOneNightReminder: AsyncController = async (req, res) => {
  try {
    const { appointmentId } = req.params;
    await InstallationScheduleService.sendOneNightReminder(appointmentId);
    return res.status(200).send({ message: 'One night reminder sent successfully' });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const updateInstallationArrival: AsyncController = async (req, res) => {
  try {
    const { appointmentId } = req.params;

    const appointment = await InstallationScheduleService.updateArrivalTime(appointmentId);

    return res.status(200).send({
      success: true,
      message: 'Arrival time updated successfully',
      data: appointment,
    });
  } catch (error: any) {
    return res.status(400).send({
      success: false,
      message: error.message,
    });
  }
};

export const filterInstallationAppointments: AsyncController = async (req, res) => {
  try {
    const { startDate, endDate, status, cityId, crewId, neighborhoodId, companyId } = req.query as {
      startDate?: string;
      endDate?: string;
      status?: string;
      cityId?: string;
      crewId?: string;
      neighborhoodId?: string;
      companyId?: string;
    };

    const appointments = await InstallationScheduleService.filterInstallationAppointments({
      startDate,
      endDate,
      status,
      cityId,
      crewId,
      neighborhoodId,
      companyId,
      userId: req.userVendor.userId,
    });

    return res.status(200).send({
      message: 'Filtered appointments found',
      data: appointments,
    });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const updateNotAttendedReason: AsyncController = async (req, res) => {
  try {
    const { id } = req.params;
    const { notAttendedReason } = req.body;

    const appointment = await InstallationScheduleService.updateNotAttendedReason({
      appointmentId: id,
      notAttendedReason,
    });

    return res.status(200).send({
      message: 'Not attended reason updated successfully',
      data: appointment,
    });
  } catch (error: any) {
    return res.status(error.statusCode || 400).send({ error: error.message });
  }
};

export const searchInstallationAppointments: AsyncController = async (req, res) => {
  try {
    const { page, limit, query, startDate, endDate, status, cityId, crewId, neighborhoodId, companyId } =
      req.query as {
        page?: string;
        limit?: string;
        query?: string;
        startDate?: string;
        endDate?: string;
        status?: string;
        cityId?: string;
        crewId?: string;
        neighborhoodId?: string;
        companyId?: string;
      };

    const result = await InstallationScheduleService.searchInstallationAppointments({
      page: page ? parseInt(page, 10) : 1,
      limit: limit ? parseInt(limit, 10) : 10,
      query,
      startDate,
      endDate,
      status,
      cityId,
      crewId,
      neighborhoodId,
      companyId,
      userId: req.userVendor.userId,
    });

    return res.status(200).send({
      message: 'Citas de instalación encontradas',
      data: result.appointments,
      pagination: result.pagination,
    });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};
