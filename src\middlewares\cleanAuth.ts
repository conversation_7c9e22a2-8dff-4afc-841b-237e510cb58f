import jwt from 'jsonwebtoken';
import { NextFunction, Request, Response } from 'express';
import { UnauthorizedException } from '../clean/errors/exceptions';
import { blacklist } from '../conf/blackList';
import { accessTokenSecret, Roles } from '../constants';
import { AuthUser } from '../types&interfaces/types';

export const cleanAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const { authorization } = req.headers;

  if (!authorization) {
    throw new UnauthorizedException();
  }

  const token = authorization.split(' ')[1];

  if (!token) {
    throw new UnauthorizedException();
  }

  if (blacklist[token]) {
    throw new UnauthorizedException();
  }

  try {
    const result = jwt.verify(token, accessTokenSecret);

    req.authUser = result as AuthUser;
  } catch (error: any) {
    throw new UnauthorizedException();
  }

  next();
};

export const isAdministratorOrSuperAdmin = (req: Request, res: Response, next: NextFunction) => {
  if (req.authUser?.role === Roles.administrador || req.authUser?.role === Roles.superadmin) {
    next();
  }
  throw new UnauthorizedException();
};
