import { Request, Response, NextFunction } from 'express';
import { Types } from 'mongoose';
import { UserRolesPermissionsService } from '../services/user-roles-permissions.service';
import { GlobalUserRole } from '../models/user-roles-permissions.model';

const userRolesPermissionsService = new UserRolesPermissionsService();

/**
 * Middleware para verificar permisos específicos de módulo
 */
export const checkPermission = (module: string, permission: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.userVendor?.userId;
      
      if (!userId) {
        return res.status(401).json({
          message: 'Usuario no autenticado',
          code: 'UNAUTHORIZED'
        });
      }
      
      const hasPermission = await userRolesPermissionsService.hasPermission(
        new Types.ObjectId(userId),
        module,
        permission
      );
      
      if (!hasPermission) {
        return res.status(403).json({
          message: `No tienes permisos para ${permission} en el módulo ${module}`,
          code: 'INSUFFICIENT_PERMISSIONS',
          required: { module, permission }
        });
      }
      
      next();
    } catch (error) {
      console.error('Error checking permissions:', error);
      return res.status(500).json({
        message: 'Error interno del servidor al verificar permisos',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
};

/**
 * Middleware para verificar roles específicos
 */
export const checkRole = (roles: GlobalUserRole | GlobalUserRole[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.userVendor?.userId;
      
      if (!userId) {
        return res.status(401).json({
          message: 'Usuario no autenticado',
          code: 'UNAUTHORIZED'
        });
      }
      
      const rolesToCheck = Array.isArray(roles) ? roles : [roles];
      
      const hasRole = await userRolesPermissionsService.hasAnyRole(
        new Types.ObjectId(userId),
        rolesToCheck
      );
      
      if (!hasRole) {
        return res.status(403).json({
          message: `No tienes uno de los roles requeridos: ${rolesToCheck.join(', ')}`,
          code: 'INSUFFICIENT_ROLE',
          required: rolesToCheck
        });
      }
      
      next();
    } catch (error) {
      console.error('Error checking roles:', error);
      return res.status(500).json({
        message: 'Error interno del servidor al verificar roles',
        code: 'ROLE_CHECK_ERROR'
      });
    }
  };
};

/**
 * Middleware para verificar si el usuario es de tipo OCN
 */
export const checkOCNUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.userVendor?.userId;
    
    if (!userId) {
      return res.status(401).json({
        message: 'Usuario no autenticado',
        code: 'UNAUTHORIZED'
      });
    }
    
    const userPermissions = await userRolesPermissionsService.getUserPermissions(
      new Types.ObjectId(userId)
    );
    
    if (!userPermissions || userPermissions.userType !== 'ocn') {
      return res.status(403).json({
        message: 'Solo usuarios OCN pueden acceder a este recurso',
        code: 'OCN_ONLY_ACCESS'
      });
    }
    
    next();
  } catch (error) {
    console.error('Error checking OCN user:', error);
    return res.status(500).json({
      message: 'Error interno del servidor al verificar usuario OCN',
      code: 'OCN_CHECK_ERROR'
    });
  }
};

/**
 * Middleware para verificar si el usuario pertenece a una organización específica
 */
export const checkOrganizationAccess = (organizationIdParam: string = 'organizationId') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.userVendor?.userId;
      const organizationId = req.params[organizationIdParam];
      
      if (!userId) {
        return res.status(401).json({
          message: 'Usuario no autenticado',
          code: 'UNAUTHORIZED'
        });
      }
      
      if (!organizationId) {
        return res.status(400).json({
          message: 'ID de organización requerido',
          code: 'ORGANIZATION_ID_REQUIRED'
        });
      }
      
      const userPermissions = await userRolesPermissionsService.getUserPermissions(
        new Types.ObjectId(userId)
      );
      
      if (!userPermissions) {
        return res.status(403).json({
          message: 'Usuario sin permisos configurados',
          code: 'NO_PERMISSIONS_CONFIGURED'
        });
      }
      
      // Los usuarios OCN pueden acceder a cualquier organización
      if (userPermissions.userType === 'ocn') {
        return next();
      }
      
      // Verificar que el usuario pertenece a la organización
      if (!userPermissions.organizationId || 
          userPermissions.organizationId.toString() !== organizationId) {
        return res.status(403).json({
          message: 'No tienes acceso a esta organización',
          code: 'ORGANIZATION_ACCESS_DENIED'
        });
      }
      
      next();
    } catch (error) {
      console.error('Error checking organization access:', error);
      return res.status(500).json({
        message: 'Error interno del servidor al verificar acceso a organización',
        code: 'ORGANIZATION_ACCESS_CHECK_ERROR'
      });
    }
  };
};

/**
 * Middleware para verificar múltiples permisos (AND logic)
 */
export const checkMultiplePermissions = (permissions: Array<{module: string, permission: string}>) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.userVendor?.userId;
      
      if (!userId) {
        return res.status(401).json({
          message: 'Usuario no autenticado',
          code: 'UNAUTHORIZED'
        });
      }
      
      const userObjectId = new Types.ObjectId(userId);
      
      // Verificar todos los permisos
      for (const perm of permissions) {
        const hasPermission = await userRolesPermissionsService.hasPermission(
          userObjectId,
          perm.module,
          perm.permission
        );
        
        if (!hasPermission) {
          return res.status(403).json({
            message: `No tienes permisos para ${perm.permission} en el módulo ${perm.module}`,
            code: 'INSUFFICIENT_PERMISSIONS',
            required: perm
          });
        }
      }
      
      next();
    } catch (error) {
      console.error('Error checking multiple permissions:', error);
      return res.status(500).json({
        message: 'Error interno del servidor al verificar permisos múltiples',
        code: 'MULTIPLE_PERMISSIONS_CHECK_ERROR'
      });
    }
  };
};

/**
 * Middleware para verificar si el usuario puede gestionar todos los recursos (manageAll)
 */
export const checkManageAllPermission = (module: string) => {
  return checkPermission(module, 'manageAll');
};

/**
 * Helper function para verificar permisos en controladores
 */
export const hasPermissionHelper = async (
  userId: string,
  module: string,
  permission: string
): Promise<boolean> => {
  try {
    return await userRolesPermissionsService.hasPermission(
      new Types.ObjectId(userId),
      module,
      permission
    );
  } catch (error) {
    console.error('Error in hasPermissionHelper:', error);
    return false;
  }
};

/**
 * Helper function para verificar roles en controladores
 */
export const hasRoleHelper = async (
  userId: string,
  roles: GlobalUserRole | GlobalUserRole[]
): Promise<boolean> => {
  try {
    const rolesToCheck = Array.isArray(roles) ? roles : [roles];
    return await userRolesPermissionsService.hasAnyRole(
      new Types.ObjectId(userId),
      rolesToCheck
    );
  } catch (error) {
    console.error('Error in hasRoleHelper:', error);
    return false;
  }
};
