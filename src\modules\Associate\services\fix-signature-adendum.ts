import axios from 'axios';
import { WEETRUST_ADD_ID, WEETRUST_URL, WEETRUST_USER_ID } from '../../../constants';

export const EMAIL_OCN = '<EMAIL>';
// export const EMAIL_OCN = '<EMAIL>'; // For testing

function getAdendumCoordinates({ associateEmail, hasFee }: { associateEmail: string; hasFee: boolean }) {
  const adendumCoordinatesWithoudFee = [
    {
      user: {
        email: EMAIL_OCN,
      },
      coordinates: {
        x: 121.9500223114681,
        y: 129.3671262198797,
      },
      page: 2,
      pageY: 129.3671262198797,
      pageYv2: 129.3671262198797,
      color: '#FFD247',
      imageSize: {
        width: 107.94134211339689,
        height: 54.505034136467735,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 362.8424810352521,
        y: 129.36712621987974,
      },
      page: 2,
      pageY: 129.36712621987974,
      pageYv2: 129.36712621987974,
      color: '#FFD247',
      imageSize: {
        width: 107.94134211339689,
        height: 54.505034136467735,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
  ];

  const adendumCoordinatesWithFee = [
    {
      user: {
        email: EMAIL_OCN,
      },
      coordinates: {
        x: 127.94734493529668,
        y: 576.9014988999094,
      },
      page: 2,
      pageY: 576.9014988999094,
      pageYv2: 576.9014988999094,
      color: '#FFD247',
      imageSize: {
        width: 107.94134211339689,
        height: 54.505034136467735,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
    {
      user: {
        email: associateEmail,
      },
      coordinates: {
        x: 361.84292726461393,
        y: 575.9025382466057,
      },
      page: 2,
      pageY: 575.9025382466057,
      pageYv2: 575.9025382466057,
      color: '#FFD247',
      imageSize: {
        width: 107.94134211339689,
        height: 54.505034136467735,
      },
      parentImageSize: {
        width: 595.280029,
        height: 841.890015,
      },
      viewport: {
        width: 595.280029,
        height: 841.890015,
      },
    },
  ];

  return hasFee ? adendumCoordinatesWithFee : adendumCoordinatesWithoudFee;
}

export async function fixAddendumSignature({
  associateEmail,
  hasFee,
  documentID,
  token,
}: {
  associateEmail: string;
  hasFee: boolean;
  documentID: string;
  token: string;
}) {
  const coordinates = getAdendumCoordinates({ associateEmail, hasFee });

  // return coordinates;
  const body = {
    documentID,
    staticSignPositions: coordinates,
  };
  const res = await axios.put(`${WEETRUST_URL}/documents/fixed-signatory`, body, {
    headers: {
      'user-id': WEETRUST_USER_ID,
      token,
    },
  });
  return res.data;
}

export const sendAddendumSignatory = async (
  documentID: string,
  token: string,
  { associate }: { associate: { email: string; name: string /* phone: string  */ } }
) => {
  const participants: any = [
    {
      emailID: associate.email,
      name: associate.name,
      identification: WEETRUST_ADD_ID === 'true' ? 'id' : undefined,
      // phone: associate.phone,
    },
  ];

  // console.log('NODE ENV -------------', process.env.NODE_ENV);
  if (WEETRUST_ADD_ID === 'true' || process.env.NODE_ENV !== 'production') {
    participants.push({
      emailID: EMAIL_OCN,
      name: 'Mairon Esteban Sandoval Gómez',
      identification: undefined,
    });
  }
  // console.log('participants', participants);
  const body = {
    documentID,
    title: 'Firma de adendum',
    message: 'Por favor firme el adendum',
    signatory: participants,
  };

  // console.log('body singatory', body.signatory);

  try {
    const response = await axios.put(`${WEETRUST_URL}/documents/signatory`, body, {
      headers: {
        'user-id': process.env.WEETRUST_USER_ID,
        token,
      },
    });

    return {
      success: true,
      response,
      error: null,
    };
  } catch (error: any) {
    const errorMsg = error.response?.data?.message || error.message;
    console.log('error', errorMsg);
    return {
      success: false,
      error: errorMsg,
      response: null,
    };
  }
};
