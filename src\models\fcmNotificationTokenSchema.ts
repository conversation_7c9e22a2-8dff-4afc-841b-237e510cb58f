import { FCMNotificationUserType } from '@/modules/FirebaseCloudMessaging/common/enums';
import { model, Schema, Types } from 'mongoose';

// Sub-schema for fcmTokens with timestamps
// This schema defines the structure of each FCM token entry
// It includes the old token, new token because tokens can change, so we keep both,
// for tracking purposes if token refresh.
const fcmNotificationTokenSchema = new Schema(
  {
    oldToken: { type: String, required: true, unique: true },
    token: { type: String, required: true, unique: true },
    isActive: { type: Boolean, required: true },
    deviceDetails: { type: Object, required: true },
  },
  { _id: false, timestamps: true }
);

export interface FCMNotificationTokenMongoI {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  fcmTokens: Array<{
    oldToken: string;
    token: string;
    isActive: boolean;
    deviceDetails: Record<string, any>;
    createdAt?: Date;
    updatedAt?: Date;
  }>;
  userType: FCMNotificationUserType;
}

const FCM = model(
  'FCMNotificationToken',
  new Schema<FCMNotificationTokenMongoI>(
    {
      userId: {
        type: Schema.Types.ObjectId,
        required: [true, 'user ID is required'],
      },
      fcmTokens: [fcmNotificationTokenSchema],
      userType: {
        type: String,
        enum: Object.values(FCMNotificationUserType),
        required: [true, 'user type is required'],
      },
    },
    { timestamps: true }
  )
);

const instance = new FCM();
export type FCMType = typeof instance;
export default FCM as FCMType & typeof FCM;
