// models/scheduleOverride.ts
import mongoose, { Schema, Document } from 'mongoose';
import { TimeRange, WorkshopCapacity } from '@/constants/vendor-platform';
import vendorDB from '@/vendor-platform/db';

export type OverrideType = 'BLOCKED' | 'MODIFIED';
export type OverrideScope = 'SINGLE' | 'YEARLY';

export interface ModifiedSchedule {
  workingHours?: TimeRange;
  breakTime?: TimeRange;
  capacity?: WorkshopCapacity;
}

export interface IScheduleOverride extends Document {
  startDate: Date;
  endDate: Date;
  organization?: mongoose.Types.ObjectId;
  workshop?: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  type: OverrideType;
  scope: OverrideScope; // SINGLE para una vez, YEARLY para repetición anual
  // modifiedSchedule?: {
  //   workingHours?: TimeRange;
  //   maxSimultaneousAppointments?: number;
  //   breakTime?: TimeRange;
  // };
  modifiedSchedule?: ModifiedSchedule;
  active: boolean;
  createdBy: mongoose.Types.ObjectId;
  createdAt: Date;
}

const ScheduleOverrideSchema = new Schema({
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  organization: { type: Schema.Types.ObjectId, ref: 'Organization' },
  workshop: { type: Schema.Types.ObjectId, ref: 'Workshop' },
  name: { type: String, required: true },
  description: String,
  type: {
    type: String,
    enum: ['BLOCKED', 'MODIFIED'],
    required: true,
  },
  scope: {
    type: String,
    enum: ['SINGLE', 'YEARLY'],
    required: true,
  },
  modifiedSchedule: {
    workingHours: {
      start: String,
      end: String,
    },
    breakTime: {
      start: String,
      end: String,
    },
    capacity: {
      totalBays: Number,
      techniciansPerBay: Number,
    },
  },
  active: { type: Boolean, default: true },
  createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
});

ScheduleOverrideSchema.pre('save', function (next) {
  if ((this.organization && this.workshop) || (!this.organization && !this.workshop)) {
    next(new Error('Must specify either organization or workshop, not both'));
  }
  if (this.type === 'MODIFIED' && !this.modifiedSchedule) {
    next(new Error('Modified schedule is required for MODIFIED type'));
  }
  next();
});

export const ScheduleOverride = vendorDB.model<IScheduleOverride>('ScheduleOverride', ScheduleOverrideSchema);
