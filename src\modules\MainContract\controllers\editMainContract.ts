import axios from 'axios';
import { genericMessages, mainContractResponse } from '../../../constants';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import StockVehicle from '../../../models/StockVehicleSchema';
import AssociatePayments from '../../../models/associatePayments';
import Associate from '../../../models/associateSchema';
import MainContractSchema from '../../../models/mainContractSchema';
// import { ValidRegion, gigstackRequests } from '../../../services/tokenAssignGigstack';
import { AsyncController } from '../../../types&interfaces/types';
import { parse } from 'date-fns';

export const updateMainContractById: AsyncController = async (req, res) => {
  const { mainContractId } = req.params;

  console.log('mainContractId:', mainContractId, mainContractId === '679a90415031b6e002e202d1');
  console.log('req.body:', req.body);
  try {
    const mainContract = await MainContractSchema.findById(mainContractId);
    if (!mainContract)
      return res.status(404).send({ message: mainContractResponse.errors.mainContractNotFound });

    if (req.body.allPayments) {
      const lastDate = mainContract.allPayments[mainContract.allPayments.length - 1].day;

      const newLastDate = req.body.allPayments[req.body.allPayments.length - 1]?.day as string;

      const isDifferent = lastDate !== newLastDate || lastDate < newLastDate;

      if (!isDifferent) {
        const associatePayment = await AssociatePayments.findOne({ contractId: mainContract._id });

        const endDate = parse(newLastDate, 'dd-MM-yyyy', new Date()).toISOString();

        if (associatePayment) {
          const associate = await Associate.findById(associatePayment.associateId);

          if (associate && associate.clientId) {
            await axios.patch(
              `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}`,
              {
                endDate,
              },
              {
                headers: {
                  Authorization: `Bearer ${PAYMENTS_API_KEY}`,
                },
              }
            );
          }
        }
      }
    }

    const entries = Object.keys(req.body);

    if (mainContract) {
      for (let i = 0; i < entries.length; i++) {
        (mainContract as any)[entries[i]] = Object.values(req.body)[i];
      }
    }

    const vehicle = await StockVehicle.findById(mainContract.stockId);

    if (vehicle) {
      vehicle.canFinishProcess = true;
      await vehicle.save();
    }

    if (req.body.deliveryDateContractTimezone) {
      mainContract.deliveryData = {
        timezone: req.body.deliveryDateContractTimezone,
      };
    }

    await mainContract.save();
    return res.status(200).send({ message: 'Main contract updated successfully' });
  } catch (error: any) {
    const message =
      error?.response?.data?.message || error.message || genericMessages.errors.somethingWentWrong;
    console.log('[PATCH MAIN CONTRACT BY ID]', message);
    console.error(error);
    return res.status(500).send({ message });
  }
};
