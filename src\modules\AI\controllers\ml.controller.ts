import { AsyncController } from '@/types&interfaces/types';
import { logger } from '@/clean/lib/logger';
import {
  getRiskScoreService,
  getPreApprovalService,
  getHomevisitScoreService,
  getHomevisitStackingclfScoringService,
  getHomevisitVotingclfScoringService,
} from '../services/ml.service';

/**
 * Controller for risk score calculation
 */
export const getRiskScore: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const data = await getRiskScoreService(requestId);

    return res.status(200).send(data);
  } catch (error) {
    logger.error(`[getRiskScore] Error in fetching riskscoring for rideshare performance: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing rideshare performance risk score',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Controller for pre-approval processing
 */
export const getPreApproval: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const data = await getPreApprovalService(requestId);

    return res.status(200).send(data);
  } catch (error) {
    logger.error(`[getPreApproval] Error in getPreApproval: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing pre-approval',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Controller for home visit scoring
 */
export const getHomevisitScore: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const data = await getHomevisitScoreService(requestId);

    return res.status(200).send(data);
  } catch (error) {
    if (error instanceof Error && error.message === 'Request not found') {
      return res.status(404).send('Request not found');
    }

    logger.error(`[getHomevisitScore] Error in getSocialScoring: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing social scoring',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Controller for home visit stacking classifier scoring
 */
export const getHomevisitStackingclfScoring: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const data = await getHomevisitStackingclfScoringService(requestId);

    return res.status(200).send(data);
  } catch (error) {
    if (error instanceof Error && error.message === 'Request not found') {
      return res.status(404).send('Request not found');
    }

    logger.error(`[getHomevisitStackingclfScoring] Error in getSocialScoring: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing social scoring',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Controller for home visit voting classifier scoring
 */
export const getHomevisitVotingclfScoring: AsyncController = async (req, res) => {
  try {
    const requestId = req.params.requestId;
    const data = await getHomevisitVotingclfScoringService(requestId);

    return res.status(200).send(data);
  } catch (error) {
    if (error instanceof Error && error.message === 'Request not found') {
      return res.status(404).send('Request not found');
    }

    logger.error(`[getHomevisitVotingclfScoring] Error in getSocialScoring: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while processing social scoring',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
