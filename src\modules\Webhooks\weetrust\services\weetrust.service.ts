import axios from 'axios';
import Associate from '../../../../models/associateSchema';
import { weetrustWebhookInstance } from '../model';
import StockVehicle from '../../../../models/StockVehicleSchema';
import { logger } from '../../../../clean/lib/logger';
import { getDocumentById } from '@/modules/Associate/services/weetrust';
import { runPromise } from '@/utils/run-promise';
import { getCurrentDateTime } from '@/services/timestamps';
import { uploadFileReadable } from '@/aws/s3';
import { Readable } from 'stream';
import { Types } from 'mongoose';
import Document from '@/models/documentSchema';
import { removeEmptySpacesNameFileV2 } from '@/services/removeEmptySpaces';

export async function documentSignEventForAdendum(Data: any, weetrust: typeof weetrustWebhookInstance) {
  const documentID = Data._id || Data?.documentID;

  const signatory = Data?.signatory;

  logger.info(
    `[documentSignEventForAdendum]: STARTING Document Sign event webkhook for Adendum document for documentID: ${documentID}`
  );

  if (!signatory) return;
  const associate = await Associate.findOne({ 'adendumDigitalSignature.documentID': documentID })
    .select({
      email: 1,
    })
    .select('+adendumDigitalSignature')
    .lean();

  if (!associate || !associate.adendumDigitalSignature?.length) return;
  try {
    const adendumDigitalSignature = associate.adendumDigitalSignature;

    const itemFound = adendumDigitalSignature.find((item) => item.documentID === documentID);

    if (!itemFound) return;

    signatory.forEach((s: any) => {
      console.log('s', s);
    });

    const alreadySigned = signatory.filter((s: any) => s.isSigned);

    for (const signatoryParticipant of alreadySigned) {
      const participant = itemFound.participants.find((p: any) => p.email === signatoryParticipant.emailID);

      if (participant) {
        participant.signed = true;
      }
    }

    await Associate.updateOne(
      { _id: associate._id },
      {
        $set: {
          adendumDigitalSignature: adendumDigitalSignature,
        },
      }
    );

    weetrust.associateId = associate._id;
    weetrust.success = true;
    await weetrust.save();

    logger.info(
      `[documentSignEventForAdendum]: COMPLETED Document Sign event webkhook for Adendum document for documentID: ${documentID} completed`
    );
  } catch (error) {
    console.log('Error', error);
    logger.error(
      `[documentSignEventForAdendum]: Error in Document Sign event webkhook for Adendum document for documentID: ${associate._id}`
    );
  }
}

export async function documentCompletedForAdendum(
  Data: any,
  weetrust: typeof weetrustWebhookInstance,
  isRetry: boolean = false
) {
  // const documentID = Data._id || Data?.documentID;
  const documentID =
    Data._id || Data?.documentID || weetrust?.body?.Document._id || weetrust?.body?.Document?.documentID;

  const associate = await Associate.findOne({ 'adendumDigitalSignature.documentID': documentID })
    .select({
      email: 1,
      adendumDocs: 1,
    })
    .select('+adendumDigitalSignature')
    .lean();

  if (!associate) return;
  try {
    // console.log('associate', associate.adendumDigitalSignature);

    logger.info(
      `[documentCompletedForAdendum]: Starting Document completed webkhook for Adendum document for associateId: ${associate._id}`
    );

    const adendumDigitalSignature = associate.adendumDigitalSignature;
    if (!adendumDigitalSignature?.length) return;

    const itemFound = adendumDigitalSignature.find((item) => item.documentID === documentID);

    // console.log('itemFound', itemFound);
    if (!itemFound) return;

    itemFound.signed = true;

    const stockVehicle = await StockVehicle.findOne({ 'drivers._id': associate._id });

    // console.log('stockVehicle', stockVehicle);
    if (!stockVehicle) return;

    const documentURL = Data.documentURL || Data?.documentURL || weetrust?.body?.Document?.documentURL;

    // console.log('documentURL', documentURL);
    if (!documentURL) return;

    const { error: errorFile, data: file } = await runPromise(async () => {
      if (isRetry) {
        const document = await getDocumentById(documentID);
        if (document) {
          const documentUrl = document.responseData.documentFileObj.url;
          if (documentUrl) {
            return axios.get(documentUrl, { responseType: 'arraybuffer' });
          }
        }
      }
      return axios.get(documentURL, { responseType: 'arraybuffer' });
    }, '[GetDocumentURL]');
    // const { data: fileUrl } = await axios.get(documentURL, { responseType: 'arraybuffer' });
    // let fileUrl: typeof data = null;
    // remove null of typeof data
    let fileUrl: any = file;
    // console.log('fileUrl first attempt', fileUrl);

    if (errorFile) {
      // console.log('errorFile', errorFile?.response?.data || errorFile.message);
      const statusError = errorFile?.response?.status || 500;
      if (statusError && statusError === 403) {
        const response = await getDocumentById(documentID);
        if (response) {
          // file
          // console.log('response', response);
          const documentUrl = response.responseData.documentFileObj.url;
          // console.log('Document url on second attempt', documentUrl);
          if (documentUrl) {
            const { error: errorFileUrl, data } = await runPromise(
              () => axios.get(documentUrl, { responseType: 'arraybuffer' }),
              '[GetDocumentURL - 2]'
            );
            if (errorFileUrl) {
              // throw new Error(errorFileUrl.message);
              // console.log('errorFileUrl', errorFileUrl);
              throw new Error('Error getting file');
            }
            fileUrl = data;
            // console.log('fileUrl second attempt', fileUrl);
          }
        }
      }
    }

    itemFound.url = fileUrl.data;

    const createdCount = stockVehicle.adendumServiceCount?.toString() || '1';

    const contractNumber = stockVehicle.extensionCarNumber
      ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
      : stockVehicle.carNumber;
    // save this document in the database
    const path = `adendums/${contractNumber}/${associate.email}/${stockVehicle.status}-${createdCount}/adendumDoc`;

    stockVehicle.adendumServiceCount = stockVehicle.adendumServiceCount
      ? stockVehicle.adendumServiceCount + 1
      : 1;

    const originalName = removeEmptySpacesNameFileV2(Data.documentFileObj.originalKey);

    const document = new Document({
      associateId: associate._id,
      path,
      originalName,
    });

    await document.save();

    // convert the file to readable stream
    const buffer = Buffer.from(fileUrl.data);

    const pdfStream = Readable.from(buffer);

    // upload the file to s3
    await uploadFileReadable(pdfStream, path);

    weetrust.associateId = associate._id;
    associate.adendumDocs.push({ _id: document._id });

    stockVehicle.updateHistory.push({
      step: 'Documentos firmados agregados automaticamente',
      time: getCurrentDateTime(),
      // userId: new Types.ObjectId('647f69776a98b82801ddcc45'), // ocn user prod
      // userId: new Types.ObjectId('64f9fca5d0a417208d2b8e78'), // dev
      userId: process.env.PAYMENTS_API_URL?.includes('develop')
        ? new Types.ObjectId('64f9fca5d0a417208d2b8e78')
        : new Types.ObjectId('647f69776a98b82801ddcc45'),
    });

    weetrust.success = true;
    await weetrust.save();
    await stockVehicle.save();

    await Associate.updateOne(
      { _id: associate._id },
      {
        $set: {
          adendumDigitalSignature: adendumDigitalSignature,
          adendumDocs: associate.adendumDocs,
        },
      }
    );

    logger.info(
      `[documentCompletedForAdendum]: Completed Document completed webkhook for Adendum document for associateId: ${associate._id}`
    );
  } catch (error) {
    console.log('Error', error);

    logger.error(
      `[documentCompletedForAdendum]: Error in Document completed webkhook for Adendum document for associateId: ${associate._id}`
    );
  }
}
