import { Schema, model } from 'mongoose';

const contractSchema = new Schema({
  region: {
    type: Number,
    required: [true, 'Region is required'],
  },
  contractNumber: {
    type: Number,
    required: [true, 'Number is required'],
  },
  alias: Number,
  stockVehicleId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicle',
    required: [true, 'StockVehicle is required'],
  },
  documentId: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updateAt: {
    type: Date,
  },
});

const Contract = model('Contract', contractSchema);

export default Contract;
