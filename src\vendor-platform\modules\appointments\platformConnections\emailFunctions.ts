import { logger } from '@/clean/lib/logger';
import { transporter } from '@/modules/platform_connections/emailFunc';
import {
  vehicleMaintenanceAppointmentCancelledEmailTemplate,
  vehicleMaintenanceAppointmentCompletedEmailTemplate,
  vehicleMaintenanceAppointmentMissedEmailTemplate,
  vehicleMaintenanceAppointmentRescheduledEmailTemplate,
  vehicleMaintenanceAppointmentScheduledEmailTemplate,
} from './emailTemplates';
import { emailSender } from '@/constants';

interface IVehicleMaintenanceEmail {
  name: string;
  email: string;
  associateId: string;
}

interface IVehicleMaintenanceScheduled extends IVehicleMaintenanceEmail {
  date: string;
  time: string;
  rescheduleAppointmentLink: string;
  workshopName: string;
  workshopLocation: string;
}

interface IVehicleMaintenanceRescheduled extends IVehicleMaintenanceEmail {
  date: string;
  time: string;
  link: string;
}

interface IVehicleMaintenanceCancelled extends IVehicleMaintenanceEmail {
  link: string;
}

interface IVehicleMaintenanceCompleted extends IVehicleMaintenanceEmail {}

interface IVehicleMaintenanceMissed extends IVehicleMaintenanceEmail {
  link: string;
}

export const sendEmailAboutVehicleMaintenanceAppointmentScheduled = async ({
  name,
  date,
  time,
  rescheduleAppointmentLink,
  email,
  associateId,
  workshopName,
  workshopLocation,
}: IVehicleMaintenanceScheduled) => {
  try {
    const emailPayload = {
      subject: `¡Mantenimiento de vehículos! 👨‍🔧`,
      html: vehicleMaintenanceAppointmentScheduledEmailTemplate({
        name,
        date,
        time,
        rescheduleAppointmentLink,
        workshopName,
        workshopLocation,
      }),
    };
    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPayload.subject,
      text: '',
      html: emailPayload.html,
    });

    logger.info(
      `[sendEmailAboutVehicleMaintenanceAppointmentScheduled] - Vehicle maintenance appointment scheduled email sent with id ${emailResponse.messageId} to clientId ${associateId}`
    );
  } catch (error) {
    logger.error(
      `[sendEmailAboutVehicleMaintenanceAppointmentScheduled] - Error sending vehicle maintenance appointment email to clientId ${associateId}`,
      error
    );
  }
};

export const sendEmailAboutVehicleMaintenanceAppointmentRescheduled = async ({
  name,
  date,
  time,
  link,
  email,
  associateId,
}: IVehicleMaintenanceRescheduled) => {
  try {
    const emailPayload = {
      subject: `¡Mantenimiento de vehículos! 👨‍🔧`,
      html: vehicleMaintenanceAppointmentRescheduledEmailTemplate({
        name,
        date,
        time,
        link,
      }),
    };
    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPayload.subject,
      text: '',
      html: emailPayload.html,
    });
    logger.info(
      `[sendEmailAboutVehicleMaintenanceAppointmentRescheduled] - Vehicle maintenance appointment rescheduled email sent with id ${emailResponse.messageId} to clientId ${associateId}`
    );
  } catch (error) {
    logger.error(
      `[sendEmailAboutVehicleMaintenanceAppointmentRescheduled] - Error sending vehicle maintenance appointment email to clientId ${associateId}`,
      error
    );
  }
};

export const sendEmailAboutVehicleMaintenanceAppointmentCancelled = async ({
  name,
  link,
  email,
  associateId,
}: IVehicleMaintenanceCancelled) => {
  try {
    const emailPayload = {
      subject: `¡Mantenimiento de vehículos! 👨‍🔧`,
      html: vehicleMaintenanceAppointmentCancelledEmailTemplate({
        name,
        link,
      }),
    };
    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPayload.subject,
      text: '',
      html: emailPayload.html,
    });
    logger.info(
      `[sendEmailAboutVehicleMaintenanceAppointmentCancelled] - Vehicle maintenance appointment cancelled email sent with id ${emailResponse.messageId} to clientId ${associateId}`
    );
  } catch (error) {
    logger.error(
      `[sendEmailAboutVehicleMaintenanceAppointmentCancelled] - Error sending vehicle maintenance appointment email to clientId ${associateId}`,
      error
    );
  }
};

export const sendEmailAboutVehicleMaintenanceAppointmentCompleted = async ({
  name,
  email,
  associateId,
}: IVehicleMaintenanceCompleted) => {
  try {
    const emailPayload = {
      subject: `Mantenimiento exitoso`,
      html: vehicleMaintenanceAppointmentCompletedEmailTemplate({
        name,
      }),
    };
    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPayload.subject,
      text: '',
      html: emailPayload.html,
    });
    logger.info(
      `[sendEmailAboutVehicleMaintenanceAppointmentCompleted] - Vehicle maintenance appointment completed email sent with id ${emailResponse.messageId} to clientId ${associateId}`
    );
  } catch (error) {
    logger.error(
      `[sendEmailAboutVehicleMaintenanceAppointmentCompleted] - Error sending vehicle maintenance appointment email to clientId ${associateId}`,
      error
    );
  }
};

export const sendEmailAboutVehicleMaintenanceAppointmentMissed = async ({
  name,
  link,
  email,
  associateId,
}: IVehicleMaintenanceMissed) => {
  try {
    const emailPayload = {
      subject: `¡Mantenimiento de vehículos! 👨‍🔧`,
      html: vehicleMaintenanceAppointmentMissedEmailTemplate({
        name,
        link,
      }),
    };
    const emailResponse = await transporter.sendMail({
      from: emailSender,
      to: email,
      subject: emailPayload.subject,
      text: '',
      html: emailPayload.html,
    });
    logger.info(
      `[sendEmailAboutVehicleMaintenanceAppointmentMissed] - Vehicle maintenance appointment missed email sent with id ${emailResponse.messageId} to clientId ${associateId}`
    );
  } catch (error) {
    logger.error(
      `[sendEmailAboutVehicleMaintenanceAppointmentMissed] - Error sending vehicle maintenance appointment email to clientId ${associateId}`,
      error
    );
  }
};
