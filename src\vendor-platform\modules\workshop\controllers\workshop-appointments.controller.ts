import { AsyncController } from '@/types&interfaces/types';
import { ServiceTypeVendorModel } from '../../serviceType/models/serviceType.model';
import { ScheduleService } from '../services/schedule.service';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';

export const createAppointmentToWorkshop: AsyncController = async (req, res) => {
  try {
    const { workshopId } = req.params;
    const {
      startTime,
      serviceTypeId, // Nuevo campo
      associateId,
      stockId,
      km,
      isCorrectiveMaintenance = false, // Nueva bandera
      // Campos adicionales para mantenimiento correctivo
      failureDescription,
      urgencyLevel,
      customerDescription,
    } = req.body;

    // Obtener el tipo de servicio para validar duración
    const serviceType = await ServiceTypeVendorModel.findById(serviceTypeId);
    if (!serviceType) {
      return res.status(404).send({ error: 'Service type not found' });
    }

    // Validar que si es mantenimiento correctivo, el service type sea correcto
    if (isCorrectiveMaintenance && serviceType.maintenanceType !== 'corrective') {
      return res.status(400).send({
        error: 'Service type must be of corrective maintenance type when isCorrectiveMaintenance is true',
      });
    }

    const appointment = await ScheduleService.createAppointment(
      workshopId,
      startTime,
      serviceTypeId, // Pasar el ID del tipo de servicio
      {
        associateId,
        stockId,
        registeredKm: km,
        // Campos adicionales para mantenimiento correctivo
        failureDescription,
        urgencyLevel,
        customerDescription,
      },
      req.userVendor?.userId, // We can create an appointment without being logged in in agendas.onecarnow.com website
      req.userVendor?.isAdminPlatform,
      // email
      req.userReq?.email,
      isCorrectiveMaintenance // Pasar la bandera al servicio
    );

    return res.status(201).send({
      message: isCorrectiveMaintenance
        ? 'Corrective maintenance appointment created with complete flow'
        : 'Appointment created',
      data: appointment,
    });
  } catch (error: any) {
    console.log('[createAppointmentToWorkshop] Error', error);
    return res.status(400).send({ error: error.message });
  }
};

export const getWorkshopAppointments: AsyncController = async (req, res) => {
  try {
    const { maintenanceType } = req.query;

    let appointments = await ScheduleService.getWorkshopAppointments(
      req.params.workshopId,
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Filter by maintenance type if specified
    if (maintenanceType && (maintenanceType === 'preventive' || maintenanceType === 'corrective')) {
      appointments = appointments.filter(
        (appointment) => (appointment as any).data?.type === maintenanceType
      );
    }

    return res.status(200).send({ message: 'Appointments found', data: appointments });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const getAvailableSlotsByWorkshop: AsyncController = async (req, res) => {
  try {
    const { workshopId, date, serviceTypeId } = req.params;
    const slots = await ScheduleService.getAvailableSlots(workshopId, date, serviceTypeId);
    return res.status(200).send({ message: 'Available slots found', data: slots });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

export const getOrganizationAppointments: AsyncController = async (req, res) => {
  try {
    console.log('query', req.query);
    const { maintenanceType } = req.query;

    let appointments = await ScheduleService.getOrganizationAppointments(
      req.params.organizationId,
      req.query.startDate as string,
      req.query.endDate as string
    );

    // Filter by maintenance type if specified
    if (maintenanceType && (maintenanceType === 'preventive' || maintenanceType === 'corrective')) {
      appointments = appointments.filter(
        (appointment) => (appointment as any).data?.type === maintenanceType
      );
    }

    console.log('----------------------------------------------');
    console.log('----------------------------------------------');
    return res.status(200).send({ message: 'Appointments found', data: appointments });
  } catch (error: any) {
    return res.status(400).send({ error: error.message });
  }
};

/**
 * Obtiene la información necesaria para agendar una cita basada en las placas y kilometraje
 * @param req - Request con placas y kilometraje
 * @param res - Response con la información para agendar
 */
export const getAppointmentInfoByPlatesAndKm: AsyncController = async (req, res) => {
  const { plates, km, isCorrectiveMaintenance = false } = req.query;
  const isReschedule = req.query.isReschedule === 'true';

  // check that if isReschedule, the km is not required
  if (!isReschedule && (!plates || !km)) {
    throw HttpException.BadRequest('Se requieren las placas del vehículo y el kilometraje actual');
  }

  const currentKm = parseInt((km as string) || '0', 10);

  // check that if isReschedule, the km is not required
  if (!isReschedule && (isNaN(currentKm) || currentKm <= 0)) {
    throw HttpException.BadRequest('El kilometraje debe ser un número positivo');
  }

  const appointmentInfo = await ScheduleService.getAppointmentInfoByPlatesAndKm({
    plates: plates as string,
    currentKm,
    adminCreatorEmail: req.userReq?.email,
    isCorrectiveMaintenance: isCorrectiveMaintenance as boolean,
    isReschedule,
  });

  return res.status(200).send({
    message: 'Información para agendar cita obtenida correctamente',
    data: appointmentInfo,
  });
};
