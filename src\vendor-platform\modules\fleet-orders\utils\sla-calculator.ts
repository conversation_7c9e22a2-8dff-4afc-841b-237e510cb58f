/**
 * Utilidades para calcular fechas límite de SLA para Fleet Orders
 */

export interface SLADates {
  sentDeadline: Date; // Día 6 del mes
  dispersionDeadline: Date; // Día 22 del mes
  invoiceLetterRequestDeadline: Date; // Día 1 del siguiente mes
  invoiceLetterArrivalDeadline: Date; // Día 4 del siguiente mes
}

/**
 * Calcula todas las fechas límite de SLA para una orden de flota
 */
export function calculateSLADates(year: number, month: number): SLADates {
  // Día 6 del mes para envío
  const sentDeadline = new Date(year, month - 1, 6, 23, 59, 59, 999);
  
  // Día 22 del mes para dispersión
  const dispersionDeadline = new Date(year, month - 1, 22, 23, 59, 59, 999);
  
  // Día 1 del siguiente mes para solicitud de cartas factura
  const nextMonth = month === 12 ? 1 : month + 1;
  const nextYear = month === 12 ? year + 1 : year;
  const invoiceLetterRequestDeadline = new Date(nextYear, nextMonth - 1, 1, 23, 59, 59, 999);
  
  // Día 4 del siguiente mes para llegada de cartas factura
  const invoiceLetterArrivalDeadline = new Date(nextYear, nextMonth - 1, 4, 23, 59, 59, 999);

  return {
    sentDeadline,
    dispersionDeadline,
    invoiceLetterRequestDeadline,
    invoiceLetterArrivalDeadline,
  };
}

/**
 * Calcula los días restantes hasta una fecha límite
 */
export function calculateDaysRemaining(deadline: Date, fromDate: Date = new Date()): number {
  const timeDiff = deadline.getTime() - fromDate.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
  return daysDiff;
}

/**
 * Verifica si una fecha límite ha sido excedida
 */
export function isDeadlineExceeded(deadline: Date, fromDate: Date = new Date()): boolean {
  return fromDate > deadline;
}

/**
 * Verifica si una fecha límite está cerca (2 días o menos)
 */
export function isDeadlineNear(deadline: Date, fromDate: Date = new Date(), warningDays: number = 2): boolean {
  const daysRemaining = calculateDaysRemaining(deadline, fromDate);
  return daysRemaining <= warningDays && daysRemaining > 0;
}

/**
 * Obtiene la fecha límite correspondiente a un estado específico
 */
export function getDeadlineForStatus(status: string, slaDate: SLADates): Date | null {
  switch (status) {
    case 'created':
      return slaDate.sentDeadline;
    case 'sent':
      return slaDate.dispersionDeadline;
    case 'dispersion':
      return slaDate.invoiceLetterRequestDeadline;
    case 'invoice_letter_request':
      return slaDate.invoiceLetterArrivalDeadline;
    default:
      return null;
  }
}

/**
 * Calcula el estado de SLA para una orden
 */
export function calculateSLAStatus(
  currentStatus: string,
  slaDate: SLADates,
  fromDate: Date = new Date()
): {
  deadline: Date | null;
  daysRemaining: number;
  isExceeded: boolean;
  isNear: boolean;
  status: 'on_time' | 'warning' | 'exceeded';
} {
  const deadline = getDeadlineForStatus(currentStatus, slaDate);
  
  if (!deadline) {
    return {
      deadline: null,
      daysRemaining: 0,
      isExceeded: false,
      isNear: false,
      status: 'on_time',
    };
  }

  const daysRemaining = calculateDaysRemaining(deadline, fromDate);
  const isExceeded = isDeadlineExceeded(deadline, fromDate);
  const isNear = isDeadlineNear(deadline, fromDate);

  let status: 'on_time' | 'warning' | 'exceeded' = 'on_time';
  if (isExceeded) {
    status = 'exceeded';
  } else if (isNear) {
    status = 'warning';
  }

  return {
    deadline,
    daysRemaining,
    isExceeded,
    isNear,
    status,
  };
}

/**
 * Formatea una fecha para mostrar en mensajes
 */
export function formatDeadlineDate(date: Date): string {
  return date.toLocaleDateString('es-MX', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Obtiene el nombre legible de un estado
 */
export function getStatusDisplayName(status: string): string {
  const statusNames: { [key: string]: string } = {
    created: 'Creada',
    sent: 'Enviada',
    dispersion: 'Dispersión',
    invoice_letter_request: 'Solicitud de Cartas Factura',
    invoice_letter_arrival: 'Llegada de Cartas Factura',
    supplier_notification: 'Notificación a Proveedores',
    waiting_for_cars: 'Esperando Vehículos',
    delivered: 'Entregada',
  };
  
  return statusNames[status] || status;
}
