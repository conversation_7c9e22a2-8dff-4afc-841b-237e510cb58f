import { logger } from '@/clean/lib/logger';
// Import the new text generation function instead of the client
import { generateTextContent } from '@/services/ocr/llmClients/geminiClient';
import { GEMINI_MAX_OUTPUT_TOKENS } from '@/constants';
import { AdmissionRequest, HomeVisitHouseInformation } from '@/clean/domain/entities';
import { calculateWeightedScore } from '../../lib/helpers';
import { PERSONAL_INFORMATION_WEIGHTS } from '../../lib/weights';

/**
 * Extract personal information features from admission request for scoring
 */
export function parsePersonalInfo(admissionRequest: AdmissionRequest): Record<string, any> {
  const personalData = admissionRequest.personalData || {};
  const homeVisit = admissionRequest.homeVisit || {};
  const houseInfo = homeVisit.houseInformation || ({} as HomeVisitHouseInformation);

  // Demographics
  const age = personalData.age || 0;
  const maritalStatus = personalData.maritalStatus || '';
  const dependentsStr = personalData.dependents || 'No';
  const nationality = personalData.nationality || '';
  const occupation = personalData.occupation || '';

  // Handle dependents count
  let numberOfDependents = 0;
  if (typeof personalData.noOfDependents === 'number') {
    numberOfDependents = personalData.noOfDependents;
  } else if (dependentsStr && !isNaN(Number(dependentsStr))) {
    numberOfDependents = Number(dependentsStr);
  } else {
    const lowerDependents = dependentsStr.toLowerCase();
    numberOfDependents = lowerDependents === 'no' ? 0 : 1;
  }

  // Residence stability
  let timeInResidency = 0;
  if (personalData.timeInResidency) {
    const parsedTime = parseInt(personalData.timeInResidency.toString());
    timeInResidency = isNaN(parsedTime) ? 0 : parsedTime;
  }

  // Location quality
  const neighborhood = personalData.neighborhood || '';
  const municipality = personalData.municipality || '';
  const city = personalData.city || '';
  const state = personalData.state || '';
  const country = personalData.country || '';

  // Financial indicators
  const avgEarningPerWeek = personalData.avgEarningPerWeek || 0;
  const ownDebt = personalData.ownDebt?.toString().toLowerCase() === 'yes';

  // Asset ownership
  const ownProperty = houseInfo.ownProperty?.toString().toLowerCase() === 'yes';
  const ownsCar = personalData.ownACar?.toString().toLowerCase() === 'yes';
  const carMake = personalData.carMake || '';
  const carModel = personalData.carModel || '';

  // Family financial situation
  const spouseIncomeStatus = personalData.spouseOrPartnerIncome || '';

  // References quality
  const references = (personalData.references || {}) as Record<string, any>;
  let referenceCount = 0;

  for (let i = 1; i <= 3; i++) {
    const nameKey = `reference${i}Name`;
    const phoneKey = `reference${i}Phone`;

    if (references[nameKey] && references[phoneKey]) {
      referenceCount++;
    }
  }

  const hasValidReferences = referenceCount >= 2;

  return {
    age,
    maritalStatus,
    dependentsCount: numberOfDependents,
    nationality,
    occupation,
    timeInResidencyMonths: timeInResidency,
    neighborhood,
    municipality,
    city,
    state,
    country,
    weeklyEarnings: avgEarningPerWeek,
    hasDebt: ownDebt,
    spouseIncomeStatus,
    ownsProperty: ownProperty,
    ownsCar,
    carMake,
    carModel,
    referenceCount,
    hasValidReferences,
  };
}

/**
 * Create prompt for personal information analysis
 */
function createPersonalInfoPrompt(features: Record<string, any>): string {
  return `You are a personal information evaluator for loan underwriting.
Each feature receives a numeric score between 0 and 100 based on risk assessment.

Use the following applicant information:

DEMOGRAPHIC PROFILE:
- Age: ${features.age}
- Marital Status: ${features.maritalStatus}
- Number of Dependents: ${features.dependentsCount}
- Occupation: ${features.occupation}

RESIDENCE STABILITY:
- Time in Current Residence (months): ${features.timeInResidencyMonths}

LOCATION QUALITY:
- Neighborhood: ${features.neighborhood}
- Municipality: ${features.municipality}
- City: ${features.city}
- State: ${features.state}
- Country: ${features.country}

FINANCIAL INDICATORS:
- Weekly Earnings: ${features.weeklyEarnings}
- Has Debt: ${features.hasDebt}
- Spouse/Partner Income Status: ${features.spouseIncomeStatus}

ASSET OWNERSHIP:
- Owns Property: ${features.ownsProperty}
- Owns Car: ${features.ownsCar}
- Car Details: Make - ${features.carMake}, Model - ${features.carModel}

REFERENCE QUALITY:
- Number of References: ${features.referenceCount}
- Has Valid References: ${features.hasValidReferences}

Score each feature on a scale of 0-100 where higher scores indicate lower risk:

SCORING GUIDELINES:
- Age: Higher scores for ages 28-55, lower for very young or elderly applicants
- Marital Status: Married typically scores higher for stability
- Dependents: Fewer dependents generally scores higher (less financial burden)
- Occupation: Stable jobs score higher
- Time in Residence: Longer periods score higher (indicates stability)
- Location: Affluent areas score higher
- Weekly Earnings: Higher consistent earnings score higher
- Debt: No debt scores higher
- Asset Ownership: More assets score higher
- References: Valid references score higher

Return your assessment as a structured JSON with individual scores and explanations.
RETURN ONLY THE JSON STRING Of below shape AND NO EXTRA WORDS BEFORE OR AFTER THE JSON AS THE STRING WILL BE PARSED AS JSON.
PRESENCE OF EXTRA WORDS WILL FAIL THE PARSING OF JSON STRING:
{
  "individualScores": {
    "age": score,
    "maritalStatus": score,
    "dependentsCount": score,
    "occupation": score,
    "timeInResidencyMonths": score,
    "neighborhood": score,
    "municipality": score,
    "city": score,
    "weeklyEarnings": score,
    "hasDebt": score,
    "ownsProperty": score,
    "ownsCar": score,
    "hasValidReferences": score
  },
  "explanation": {
    "age": "explanation",
    "maritalStatus": "explanation",
    "dependentsCount": "explanation",
    "occupation": "explanation",
    "timeInResidencyMonths": "explanation",
    "neighborhood": "explanation",
    "municipality": "explanation",
    "city": "explanation",
    "weeklyEarnings": "explanation",
    "hasDebt": "explanation",
    "ownsProperty": "explanation",
    "ownsCar": "explanation",
    "hasValidReferences": "explanation",
    "overall_assessment": "brief overall assessment of the applicant's personal profile"
  }
}`;
}

/**
 * Perform personal information analysis using Gemini API via helper function
 */
export async function performPersonalInfoAnalysis(admissionRequest: AdmissionRequest): Promise<{
  finalScore: number;
  individualScores: Record<string, number>;
  responseData: any;
}> {
  try {
    logger.info(
      `[performPersonalInfoAnalysis] Starting personal info analysis for request ${admissionRequest.id}`
    );

    // Extract features from admission request
    const features = parsePersonalInfo(admissionRequest);

    // Create prompt for analysis
    const prompt = createPersonalInfoPrompt(features);

    // Configure generation parameters
    const generationConfig = {
      temperature: 0,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
    };

    // Call Gemini API using the helper function with retry logic
    // It handles API call, retries, and JSON extraction
    const responseJson = await generateTextContent(prompt, generationConfig);

    // Validate response (basic check)
    if (!responseJson) {
      throw new Error('Empty or invalid JSON response from Gemini API helper');
    }

    // Get individual scores
    const individualScores = responseJson.individualScores || {};

    // Calculate final score using weighted average
    const finalScore = calculateWeightedScore(PERSONAL_INFORMATION_WEIGHTS, individualScores);

    // Add final score to the response
    responseJson.finalScore = finalScore;

    logger.info(
      `[performPersonalInfoAnalysis] Completed personal info analysis for request ${admissionRequest.id}`
    );

    return {
      finalScore,
      individualScores,
      responseData: responseJson,
    };
  } catch (error) {
    logger.error(`[performPersonalInfoAnalysis] Error: ${error}`);
    throw error;
  }
}
