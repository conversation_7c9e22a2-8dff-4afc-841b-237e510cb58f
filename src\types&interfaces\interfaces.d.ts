import { Request } from 'express';

interface MyRequest extends Request {
  [key: string]: any;
}
export interface CarBodyProps {
  aditionalData: string;
  description: string;
  exterior: string;
  interior: string;
  liters: string;
  motorCilinders: string;
  name: string;
  payment: number;
  plan: string;
  seatsNumber: string;
  securityDetails: string;
  transmission: string;
  specialDetails: string;
}

export interface StockVehicleProps {
  carNumber?: string;
  vehiclePhoto?: string;
  model?: string;
  brand?: string;
  year?: string;
  color?: string;
  vin?: string;
  vehicleState?: string;
  owner?: string;
  billAmount?: string;
  bill?: string;
  carPlates?: string;
  circulationCard?: string;
  gpsNumber?: string;
  gpsSerie?: string;
}

export interface BlockPayment {
  status: string;
  cost: number;
  date: string;
  paymentId: string;
  url: string;
}
