import { Event } from '@/clean/domain/entities';
import { EntityType, ActionType } from '@/clean/domain/enums';
import { logger } from '@/clean/lib/logger';
import { repoSaveEvents } from '@/clean/data/mongoRepositories';

interface AuditContext {
  userId: string;
  entityId: string;
  entityType: EntityType;
  metadata?: FieldChange;
  actionType: ActionType;
  message?: string;
}

interface FieldChange<T = any> {
  field: string;
  oldValue: T;
  newValue: T;
  displayName?: string;
}

class AuditTrail {
  private events: Event[] = [];

  private batchSize = 10;

  private flushTimeout: NodeJS.Timeout | null = null;

  async logFieldChange(context: AuditContext): Promise<void> {
    const { userId, entityId, entityType, actionType, metadata, message: contextMessage } = context;
    const { field, oldValue, newValue, displayName } = metadata as FieldChange;

    const fieldDisplayName = displayName || field;
    let message: string;

    if (oldValue === undefined || oldValue === null) {
      message = contextMessage
        ? contextMessage
        : `${this.formatMetadata(metadata)} ${fieldDisplayName} agregado ${newValue}`;
    } else {
      message = contextMessage
        ? contextMessage
        : `${this.formatMetadata(metadata)} ${fieldDisplayName} actualizado de ${oldValue} a ${newValue}`;
    }

    await this.addEvent({
      userId,
      entityId,
      entityType,
      actionType,
      message,
      metadata,
    });
  }

  async logCustomEvent({
    userId,
    entityId,
    entityType,
    actionType,
    message,
    metadata,
  }: {
    userId: string;
    entityId: string;
    entityType: EntityType;
    actionType: ActionType;
    message: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.addEvent({
      userId,
      entityId,
      entityType,
      actionType,
      message,
      metadata,
    });
  }

  private formatMetadata(metadata?: FieldChange): string {
    if (!metadata) return '';
    const message = metadata.field ? `${metadata.field} ` : '';
    return message;
  }

  private async addEvent({
    userId,
    entityId,
    entityType,
    actionType,
    message,
    metadata,
  }: {
    userId: string;
    entityId: string;
    entityType: EntityType;
    actionType: ActionType;
    message: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const event = new Event({
      userId,
      entityId,
      entityType,
      actionType,
      message,
      metadata: metadata,
    });

    this.events.push(event);

    if (this.events.length >= this.batchSize) {
      await this.flush();
    } else if (!this.flushTimeout) {
      this.flushTimeout = setTimeout(() => this.flush(), 1000);
    }
  }

  private async flush(): Promise<void> {
    if (this.events.length === 0) return;

    if (this.flushTimeout) {
      clearTimeout(this.flushTimeout);
      this.flushTimeout = null;
    }

    const eventsToSave = [...this.events];
    this.events = [];

    try {
      await repoSaveEvents(eventsToSave);
      logger.info('[AuditTrail][flush] Successfully saved audit events ');
    } catch (error) {
      logger.error(`[AuditTrail][flush] Failed to save audit events,  count: ${eventsToSave.length}`, {
        stack: error instanceof Error ? error.stack : undefined,
      });
    }
  }
}

export const auditTrail = new AuditTrail();
