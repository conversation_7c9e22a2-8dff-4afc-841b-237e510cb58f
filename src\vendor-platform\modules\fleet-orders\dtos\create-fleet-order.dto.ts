export interface CreateFleetOrderDTO {
  month: number;
  year: number;
  vehicles: VehicleOrderDTO[];
  notificationEmails: string[];
}

export interface VehicleOrderDTO {
  brand: string;
  dealer: string;
  model: string;
  version: string;
  quantity: number;
  unitPrice: number;
}

/**
 * Valida los datos para crear una nueva orden de flota
 */
export function validateCreateFleetOrderDTO(data: any): {
  isValid: boolean;
  errors: string[];
  validatedData?: CreateFleetOrderDTO;
} {
  const errors: string[] = [];

  // Validar month
  if (!data.month || typeof data.month !== 'number') {
    errors.push('El mes es requerido y debe ser un número');
  } else if (data.month < 1 || data.month > 12) {
    errors.push('El mes debe estar entre 1 y 12');
  }

  // Validar year
  if (!data.year || typeof data.year !== 'number') {
    errors.push('El año es requerido y debe ser un número');
  } else if (data.year < 2024) {
    errors.push('El año debe ser 2024 o posterior');
  }

  // Validar vehicles
  if (!data.vehicles || !Array.isArray(data.vehicles)) {
    errors.push('Los vehículos son requeridos y deben ser un array');
  } else if (data.vehicles.length === 0) {
    errors.push('Debe incluir al menos un vehículo');
  } else {
    data.vehicles.forEach((vehicle: any, index: number) => {
      const vehicleErrors = validateVehicleOrderDTO(vehicle, index);
      errors.push(...vehicleErrors);
    });
  }

  // Validar notificationEmails
  if (!data.notificationEmails || !Array.isArray(data.notificationEmails)) {
    errors.push('Los emails de notificación son requeridos y deben ser un array');
  } else if (data.notificationEmails.length === 0) {
    errors.push('Debe incluir al menos un email de notificación');
  } else {
    data.notificationEmails.forEach((email: any, index: number) => {
      if (typeof email !== 'string') {
        errors.push(`Email ${index + 1}: debe ser una cadena de texto`);
      } else if (!isValidEmail(email)) {
        errors.push(`Email ${index + 1}: formato de email inválido`);
      }
    });
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    validatedData: {
      month: data.month,
      year: data.year,
      vehicles: data.vehicles,
      notificationEmails: data.notificationEmails,
    },
  };
}

/**
 * Valida los datos de un vehículo
 */
function validateVehicleOrderDTO(vehicle: any, index: number): string[] {
  const errors: string[] = [];
  const prefix = `Vehículo ${index + 1}:`;

  if (!vehicle.brand || typeof vehicle.brand !== 'string') {
    errors.push(`${prefix} la marca es requerida`);
  }

  if (!vehicle.dealer || typeof vehicle.dealer !== 'string') {
    errors.push(`${prefix} el dealer es requerido`);
  }

  if (!vehicle.model || typeof vehicle.model !== 'string') {
    errors.push(`${prefix} el modelo es requerido`);
  }

  if (!vehicle.version || typeof vehicle.version !== 'string') {
    errors.push(`${prefix} la versión es requerida`);
  }

  if (!vehicle.quantity || typeof vehicle.quantity !== 'number') {
    errors.push(`${prefix} la cantidad es requerida y debe ser un número`);
  } else if (vehicle.quantity < 1) {
    errors.push(`${prefix} la cantidad debe ser mayor a 0`);
  }

  if (!vehicle.unitPrice || typeof vehicle.unitPrice !== 'number') {
    errors.push(`${prefix} el precio unitario es requerido y debe ser un número`);
  } else if (vehicle.unitPrice < 0) {
    errors.push(`${prefix} el precio unitario debe ser mayor o igual a 0`);
  }

  return errors;
}

/**
 * Valida el formato de un email
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
