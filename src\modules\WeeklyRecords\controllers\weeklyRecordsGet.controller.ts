import { genericMessages } from '../../../constants';
import { AsyncController } from '../../../types&interfaces/types';
import WeeklyRecordsModel from '../model/weeklyRecords.model';
import { parseISO } from 'date-fns'; // Importa parseISO de date-fns

export const getWeeklyRecords: AsyncController = async (req, res) => {
  const { startDate, weekNumber, year } = req.query;

  try {
    let query: any = {};

    // Construir la consulta en función de los parámetros proporcionados
    if (year) {
      query.year = year;
    }

    if (startDate) {
      // Convertir la cadena de fecha a un objeto Date
      const startDateISO = parseISO(startDate.toString());
      query.startDate = startDateISO;
    }

    if (weekNumber) {
      query.weekNumber = Number(weekNumber);
    }

    const weeklyRecords = await WeeklyRecordsModel.findOne(query).sort({ startDate: -1 });

    return res.status(200).json({ message: 'getWeeklyRecords', weeklyRecords });
  } catch (error) {
    return res.status(500).json({ message: genericMessages.errors.somethingWentWrong, error });
  }
};
