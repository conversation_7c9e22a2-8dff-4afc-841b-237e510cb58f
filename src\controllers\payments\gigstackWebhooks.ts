import { associatePaymentsConsts, stockVehiclesText } from '../../constants';
import StockVehicle from '../../models/StockVehicleSchema';
import AssociatePayments from '../../models/associatePayments';
// import gigHistory from '../../models/gigHistory';
import RegionsPayments from '../../models/regionPaymentsSchema';
import { BlockPayment } from '../../types&interfaces/interfaces';
import { AsyncController } from '../../types&interfaces/types';
import { saveGigErrors } from '../../services/saveGigError';
import GigPayments from '../../models/gigPaymentsSchema';

export const gigstackPaymentCreated: AsyncController = async (req, res) => {
  const { data } = req.body;
  const { regionCode } = req.params;
  console.log('aqui va la data');
  console.log({ data });
  await GigPayments.create({ body: data, region: regionCode });
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    const date = new Date(data.timestamp);

    if (!associatePayments) {
      saveGigErrors('gigstackPaymentCreated', data, associatePaymentsConsts.errors.payment404);
      return res.status(200).send({ message: associatePaymentsConsts.errors.payment404 });
    }

    if (!associatePayments.model) {
      saveGigErrors('gigstackPaymentCreated', data, associatePaymentsConsts.errors.modelNotFound);
      return res.status(200).send({ message: associatePaymentsConsts.errors.modelNotFound });
    }

    if (!associatePayments.monexClabe) {
      saveGigErrors('gigstackPaymentCreated', data, associatePaymentsConsts.errors.userNotInFlow);
      return res.status(200).send({ message: associatePaymentsConsts.errors.userNotInFlow });
    }

    const region = await RegionsPayments.findOne({ region: regionCode });
    if (!region) {
      saveGigErrors('gigstackPaymentCreated', data, associatePaymentsConsts.errors.notValidRegion);
      return res.status(200).json({ error: associatePaymentsConsts.errors.notValidRegion });
    }
    // console.log({ data });
    const lastPayment = associatePayments?.newPaymentsArr[associatePayments?.newPaymentsArr.length - 1];
    const totalItems = data.items.reduce((acc: number, curr: any) => acc + curr.total, 0);

    const isBlockPayment = data.items.some((item: any) => item.id === region.reactivationFee);
    const isRentPayment = data.items.some(
      (item: any) => item.id === region.models[associatePayments.model as string].rentID
    );
    if (!isRentPayment && !isBlockPayment) {
      const otherPaymentData = {
        transactionId: data.id,
        transactionAmount: totalItems,
        date: date.toString(),
        url: data.shortURL,
        status: data.status,
      };
      associatePayments.otherPayment.push(otherPaymentData);
      associatePayments.balance -= totalItems;

      await associatePayments?.save();
      return res.status(200).send({
        message: associatePaymentsConsts.success.otherPayments,
        otherPaymentData,
      });
    }

    if (lastPayment && isRentPayment) {
      const newPayment = {
        status: data.status,
        weeklyCost: totalItems,
        url: data.shortURL,
        paymentId: data.id,
        date: date.toString(),
        blockPayment: null,
      };
      associatePayments.newPaymentsArr.push(newPayment);
    } else if (isBlockPayment && associatePayments.newPaymentsArr) {
      const currentPayment = associatePayments.newPaymentsArr[associatePayments.newPaymentsArr.length - 1];
      if (!currentPayment.blockPayment) {
        const blockPayment: BlockPayment = {
          status: data.status,
          cost: totalItems,
          date: date.toString(),
          paymentId: data.id,
          url: data.shortURL,
        };

        currentPayment.blockPayment = blockPayment;
      }
    }
    //To do: agregar logica para los pagos duplicados tanto de block como de renta

    //Calculo del balance
    associatePayments.balance -= totalItems;

    await associatePayments?.save();
    return res.status(200).send({
      message: associatePaymentsConsts.success.paymentCreated,
      lastPayment,
      associatePayments,
    });
  } catch (error) {
    await saveGigErrors('gigstackPaymentCreated', data, error);
    console.error(error);
    return res.status(200).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const updateStatusGigstack: AsyncController = async (req, res) => {
  const { data } = req.body;
  const { regionCode } = req.params;

  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      saveGigErrors('updateStatusGigstack', data, associatePaymentsConsts.errors.payment404);
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }
    if (!associatePayments.monexClabe) {
      saveGigErrors('updateStatusGigstack', data, associatePaymentsConsts.errors.userNotInFlow);
      return res.status(404).send({ message: associatePaymentsConsts.errors.userNotInFlow });
    }

    const region = await RegionsPayments.findOne({ region: regionCode });

    if (!region) {
      saveGigErrors('updateStatusGigstack', data, associatePaymentsConsts.errors.notValidRegion);
      return res.status(404).send({ message: associatePaymentsConsts.errors.notValidRegion });
    }

    const { newPaymentsArr } = associatePayments;
    let { block } = associatePayments;
    const isBlockPayment = data.items.some((item: any) => item.id === region.reactivationFee);
    const isRentPayment = data.items.some(
      (item: any) => item.id === region.models[associatePayments.model as string].rentID
    );

    const paymentToUpdate = newPaymentsArr[newPaymentsArr.length - 1];
    const otherPayment = associatePayments.otherPayment;
    let updatedPayment;

    if (
      associatePayments.newPaymentsArr.length > 0 &&
      isRentPayment &&
      (paymentToUpdate?.paymentId === data.id || paymentToUpdate?.paymentId === data.fid)
    ) {
      paymentToUpdate.status = data.status;
      updatedPayment = paymentToUpdate;
    } else if (
      isBlockPayment &&
      associatePayments.newPaymentsArr.length > 0 &&
      (paymentToUpdate.blockPayment.paymentId === data.fid ||
        paymentToUpdate.blockPayment.paymentId === data.id)
    ) {
      paymentToUpdate.blockPayment.status = data.status;
      updatedPayment = paymentToUpdate.blockPayment;
      associatePayments.markModified('newPaymentsArr');
    } else {
      otherPayment.map((payment) => {
        if (payment.transactionId === data.id || payment.transactionId === data.fid) {
          payment.status = data.status;
        }
      });
    }
    if (block === false && paymentToUpdate.status === associatePaymentsConsts.gigstackValues.successStatus) {
      associatePayments.paymentNumber++;

      await associatePayments.save();

      return res.status(200).send({
        message: associatePaymentsConsts.success.paymentUpdated,
        paymentToUpdate,
      });
    } else if (
      paymentToUpdate.blockPayment.status === associatePaymentsConsts.gigstackValues.successStatus &&
      paymentToUpdate.status === associatePaymentsConsts.gigstackValues.successStatus &&
      block === true
    ) {
      block = false;
      associatePayments.paymentNumber++;

      return res.status(200).send({
        message: associatePaymentsConsts.success.paymentUpdated,
        updatedPayment,
      });
    }

    await associatePayments?.save();

    return res.status(200).send({ message: associatePaymentsConsts.success.paymentUpdated, updatedPayment });
  } catch (error) {
    await saveGigErrors('updateStatusGigstack', data, error);
    console.error(error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const paymentHistoryGigstackSuccess: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }

    if (!associatePayments.monexClabe) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.userNotInFlow });
    }

    const stockVehicle = await StockVehicle.findById(associatePayments.vehiclesId);
    if (!stockVehicle) {
      return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
    }
    const date = new Date(data.timestamp);
    const totalItems = data.items.reduce((acc: number, curr: any) => acc + curr.total, 0);
    const paymentAdded = {
      email: data.client.email,
      transactionId: data.id,
      transactionAmount: totalItems,
      source: '1',
      date: date.toString(),
    };
    associatePayments.paymentsHistory.push({
      email: data.client.email,
      transactionId: data.id,
      transactionAmount: totalItems,
      source: '1',
      date: date.toString(),
    });
    stockVehicle.paymentCount++;
    await stockVehicle.save();
    await associatePayments.save();

    return res.status(200).send({
      message: associatePaymentsConsts.success.historyPaymentAdded,
      paymentAdded,
    });
  } catch (error) {
    await saveGigErrors('paymentHistoryGigstackSuccess', data, error);
    console.error(error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};

export const cancelGigstack: AsyncController = async (req, res) => {
  const { data } = req.body;
  try {
    const associatePayments = await AssociatePayments.findOne({ associateEmail: data.client.email });
    if (!associatePayments) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.payment404 });
    }

    if (!associatePayments.monexClabe) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.userNotInFlow });
    }
    //Todo: Logica para manejar casos de que cancelemos el pago de la semana, tiene que haber movimientos dentro del array si es semanmal

    //Calculo del balance
    const totalItems = data.items.reduce((acc: number, curr: any) => acc + curr.total, 0);
    associatePayments.balance += totalItems;
    await associatePayments.save();

    return res.status(200).send({ message: associatePaymentsConsts.success.paymentCancelled });
  } catch (error) {
    console.error(error);
    await saveGigErrors('cancelGigstack', data, error);
    return res.status(400).send({ message: associatePaymentsConsts.errors.error, error });
  }
};
