import {
  CorrectiveMaintenanceType,
  FailureType,
  VehicleArrivalMethod,
} from '../models/corrective-maintenance-order.model';

export interface CreateCorrectiveMaintenanceOrderDto {
  stockId: string;
  associateId: string;
  workshopId: string;
  type: CorrectiveMaintenanceType;
  failureType: FailureType;
  arrivalMethod: VehicleArrivalMethod;
  customerDescription?: string;
  canVehicleDrive: boolean;
  needsTowTruck: boolean;
  approvalType: 'fleet' | 'customer';
}

export interface CompleteDiagnosisDto {
  diagnosisNotes: string;
  services: {
    serviceType: string;
    serviceName: string;
    description: string;
    estimatedCost: number;
    estimatedDuration: number;
    laborCost: number;
    parts: {
      name: string;
      quantity: number;
      unitCost: number;
      supplier: string;
      estimatedArrival?: Date;
    }[];
  }[];
}

export interface CreateQuotationDto {
  approvalType: 'fleet' | 'customer';
  approverEmail?: string;
  validityDays?: number;
  customerNotes?: string;
  internalNotes?: string;
  paymentTerms?: string;
  warrantyTerms?: string;
}

export interface ApprovalDecisionDto {
  serviceId: string;
  isApproved: boolean;
  rejectionReason?: string;
}

export interface ProcessApprovalDecisionsDto {
  decisions: ApprovalDecisionDto[];
}

export interface CreateCorrectiveMaintenanceAppointmentDto {
  startTime: string;
  endTime: string;
  serviceTypeId: string; // Required - must be a corrective maintenance service type
  failureDescription?: string;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
}
