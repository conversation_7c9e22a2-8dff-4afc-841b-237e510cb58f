import FleetOrder from '../models/fleet-order.model';

/**
 * Genera un número de orden único para Fleet Orders
 * Formato: FO-YYYY-MM-XXX
 * Ejemplo: FO-2024-03-001
 */
export async function generateOrderNumber(year: number, month: number): Promise<string> {
  // Buscar el último número de orden para el mes y año especificados
  const lastOrder = await FleetOrder.findOne({
    year,
    month,
  })
    .sort({ orderNumber: -1 })
    .select('orderNumber')
    .lean();

  let sequenceNumber = 1;

  if (lastOrder && lastOrder.orderNumber) {
    // Extraer el número de secuencia del último número de orden
    const parts = lastOrder.orderNumber.split('-');
    if (parts.length === 4) {
      const lastSequence = parseInt(parts[3], 10);
      if (!isNaN(lastSequence)) {
        sequenceNumber = lastSequence + 1;
      }
    }
  }

  // Formatear el número de secuencia con ceros a la izquierda (3 dígitos)
  const formattedSequence = sequenceNumber.toString().padStart(3, '0');
  
  // Formatear el mes con ceros a la izquierda (2 dígitos)
  const formattedMonth = month.toString().padStart(2, '0');

  return `FO-${year}-${formattedMonth}-${formattedSequence}`;
}

/**
 * Valida el formato de un número de orden
 */
export function validateOrderNumber(orderNumber: string): boolean {
  const pattern = /^FO-\d{4}-\d{2}-\d{3}$/;
  return pattern.test(orderNumber);
}

/**
 * Extrae información del número de orden
 */
export function parseOrderNumber(orderNumber: string): {
  year: number;
  month: number;
  sequence: number;
} | null {
  if (!validateOrderNumber(orderNumber)) {
    return null;
  }

  const parts = orderNumber.split('-');
  return {
    year: parseInt(parts[1], 10),
    month: parseInt(parts[2], 10),
    sequence: parseInt(parts[3], 10),
  };
}
