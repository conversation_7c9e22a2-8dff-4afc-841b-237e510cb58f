export type GestorNotificationTemplateProps = {
  gestorName: string;
  tramiteName: string;
  vehicleInfo?: string;
  procedimientoId: string;
  cost?: number;
  duration?: number;
  status?: string;
};

export default function gestorNotificationTemplate({
  gestorName,
  tramiteName,
  vehicleInfo,
  procedimientoId,
}: GestorNotificationTemplateProps) {
  const year = new Date().getFullYear();
  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Nuevo Procedimiento Asignado</title>
  <!--[if mso]>
    <style type="text/css">
      body, div, p, a { font-family: Arial, Helvetica, sans-serif !important; }
    </style>
  <![endif]-->
  <style>
    body {
      font-family: Helvetica, Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #ffffff;
    }

    .email-container {
      max-width: 600px;
      margin: 0 auto;
      padding: 1rem 2rem;
      background-color: #ffffff;
    }

    .logo {
      text-align: left;
      margin-bottom: 24px;
    }

    .logo img {
      max-width: 150px;
      height: 32px;
    }

    .card {
      background-color: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }

    .title {
      margin: 1rem 0;
      font-size: 24px;
      color: #344054;
    }

    .text {
      margin: 1rem 0;
      font-size: 16px;
      color: #4b5563;
      line-height: 1.5;
    }

    .procedure-details {
      background-color: #f9fafb;
      border-radius: 8px;
      padding: 16px;
      margin: 16px 0;
    }

    .procedure-details p {
      margin: 8px 0;
      font-size: 14px;
    }

    .footer {
      text-align: center;
      margin-top: 32px;
      padding-top: 16px;
      border-top: 1px solid #e5e7eb;
      color: #6b7280;
      font-size: 12px;
    }
  </style>
</head>

<body>
  <div class="email-container">
    <div class="logo">
      <img src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp" alt="OneCarNow Logo">
    </div>
    <div class="card">
      <div style="color: #000000; text-align: left;">
        <h1 class="title">🚗 Nuevo Procedimiento Asignado</h1>
        <p class="text">Hola <strong>${gestorName}</strong>,</p>
        <p class="text">Se te ha asignado un nuevo procedimiento para gestionar. A continuación, encontrarás los detalles:</p>

        <div class="procedure-details">
          <p><strong>Tipo de Trámite:</strong> ${tramiteName}</p>
          ${vehicleInfo ? `<p><strong>Vehículo:</strong> ${vehicleInfo}</p>` : ''}
          <p><strong>ID del Procedimiento:</strong> ${procedimientoId}</p>
        </div>

        <p class="text">Por favor, revisa este procedimiento en el sistema y comienza a gestionarlo lo antes posible.</p>
        <p class="text">Si tienes alguna pregunta, no dudes en contactar al equipo de soporte.</p>
        <p class="text">¡Gracias por tu trabajo!</p>
        <p>Saludos,<br>Equipo OCN</p>
      </div>
    </div>
    <!-- Footer Section -->
    <div class="footer">
      © Copyright ${year} OCN
    </div>
  </div>
</body>

</html>`;
}
