/* eslint-disable prettier/prettier */
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import { accessTokenSecret, citiesByRegion, CountriesEnum, HandoverType, slackTexts } from '../constants';
import { AsyncController } from '../types&interfaces/types';
import StockVehicle, { UpdatedVehicleStatus, VehicleCategory, VehicleStatus, VehicleSubCategory, VehicleSubCategoryType, PhysicalVehicleStatus } from '../models/StockVehicleSchema';
import Document from '../models/documentSchema';
import { Types } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';
import { mapReplaceArrayOfDocsId, mapReplaceObjectWithDocUrl, replaceDocWithUrl, replaceSingleObjectWithDocUrl, replaceSingleObjectWithDocUrlSureste } from '../services/getPropertyWithUrls';
import { removeEmptySpacesNameFile } from '../services/removeEmptySpaces';
import { uploadFile } from '../aws/s3';
import { stockVehiclesText, steps } from '../constants';
import { getUpdateHistoryWithUserInfo } from '../services/intex';
import { getLastContractNumber } from '../services/getTheLastContractNumber';
import Contract from '../models/contractSchema';
import Associate from '../models/associateSchema';
import User from '../models/userSchema';
import Readmission from '../models/readmissionSchema';
import { updateAccountProject } from '../services/updateAccountPaymentProject';
import { getStockQuerySchema } from '../express-validator/zod/get-all-stock.validator';
import { associateService } from '../modules/Associate/services/associate.service';
import { logger } from '@/clean/lib/logger';
import MainContractSchema from '@/models/mainContractSchema';
import { checkAndUpdateVehicleStatus, deleteDocument, deleteMostRecentPolicy, deleteMostRecentTenancy } from '@/services/stockVehicles/vehicleFunctions'
import { slackChannelNotifier, createSlackErrorNotification } from '@/services/slackBotNotifier/slackBot';
import { SLACK_NOTIFIER_BOT_TOKEN, SLACK_BULK_UPLOAD_CHANNEL_ID } from '@/constants';
import { sendSlackFallbackEmail } from '@/modules/platform_connections/emailFunc';
import { processBulkXMLFiles } from '@/services/stockVehicles/xmlUploadServices';
import { generateAndUploadQrCode } from '../services/qrCodeService'; // Import the QR service
import QRActionToken, { QRActionTokenType } from '../models/qrActionTokenSchema'; // Import our new token model
import crypto from 'crypto'; // For generating a random token
import { createQRScanHistoryEntry, getQRScanHistoryForVehicle } from '../services/qrScanHistoryService';
import { QRScanHistoryActionType } from '../models/qrScanHistorySchema';
import { FRONTEND_ADMIN_URL, VENDOR_PANEL_URL } from '@/constants';
import { getQrScanStepResult } from '../services/stockVehicles/vehiclePhysicalStatus';
import { createQrActionToken } from '../services/qrCodeService';
import StartPayFlow from '@/models/start-pay-flow';
import axios from 'axios';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '@/constants/payments-api';

export const checkIfVehicleExist: AsyncController = async (req, res) => {
  const { carNumber } = req.body;
  try {
    const vehicleExist = await StockVehicle.findOne({ carNumber });

    if (vehicleExist) return res.status(200).send({ message: 'Vehiculo existente' })
    else return res.status(201).send({ message: 'No existe, puede crearse' })

  } catch (error) {
    return res.status(500).send({ message: 'Hubo un error', error })
  }
}

const cities: { [key: string]: string } = {
  1: 'cdmx',
  2: 'gdl',
  3: 'mty',
  4: 'qro',
  5: 'tij',
  6: 'moka',
  7: 'pbe',
  8: 'tol',
  9: 'ptv',
  10: 'tep',
  11: 'col',
  12: 'sal',
  13: 'torr',
  14: 'dur',
  15: 'mxli',
  16: 'her',
  17: 'chi',
  18: 'leo',
  19: 'ags',
  20: 'slp',
  21: 'mer',
  // 22: 'mia', // country -> US, State -> Florida, city -> Miami
  22: 'dal', // country -> US, State -> Texas, city -> Dallas
};

export const addStockVehicle: AsyncController = async (req, res) => {
  const { /* historyData, */ region, carNumber } = req.body

  const allowedProperties = [
    'region',
    'carNumber',
    'model',
    'brand',
    'status',
    'vehicleStatus',
    'category',
    'version',
    'year',
    'color',
    'vin',
    'owner',
    'billAmount',
    'billNumber',
    'billDate',
    'receptionDate',
    'km',
    'country',
    'state',
    'mi',
    'isElectric',
  ]

  const files = req.files as Express.Multer.File[]
    & { bill: Express.Multer.File[] };

  const { bill } = files;

  const updateProps: { [key: string]: string | undefined | boolean } = {}
  // Itera a través de las propiedades permitidas y agrega las que existen en req.body
  allowedProperties.forEach(prop => {
    if (prop in req.body) {
      updateProps[prop] = req.body[prop.trim()];
      if (prop === 'isElectric') {
        updateProps[prop] = req.body[prop.trim()] === 'true'
      }
    }
  });
  const newStockVehicleId = new Types.ObjectId();

  const contractNumber = (await getLastContractNumber(region)) || 1;
  // const ceros = '0'.repeat(3 - contractNumber.toString().length);
  // const carNumber = region + ceros + contractNumber;
  const contract = new Contract({
    region,
    contractNumber,
    alias: Number(carNumber),
    stockVehicleId: newStockVehicleId,
  });


  try {

    const newVehicleValidator = await StockVehicle.findOne({ carNumber });
    if (newVehicleValidator) {
      return res.status(409).json({
        message: stockVehiclesText.errors.vehicleAlreadyExists,
      });
    }

    // const newStockVehiclePhoto = new Document({
    //   originalName: vehiclePhoto[0].originalname,
    //   path: `stock/${newStockVehicleId}/photo/${vehiclePhoto[0].originalname}`,
    //   vehicleId: newStockVehicleId,
    // });

    const newStockVehicleBill = new Document({
      originalName: bill[0].originalname,
      path: `stock/${carNumber}/bill/${bill[0].originalname}`,
      vehicleId: newStockVehicleId,
    });

    // await uploadFile(vehiclePhoto[0], newStockVehiclePhoto.originalName, `stock/${newStockVehicleId}/photo/`);
    await uploadFile(bill[0], newStockVehicleBill.originalName, `stock/${carNumber}/bill/`);
    await newStockVehicleBill.save(); // Save the bill doc

    const newStockVehicle = new StockVehicle({
      _id: newStockVehicleId,
      carNumber,
      vehicleState: cities[region.toString()],
      // vehiclePhoto: new Types.ObjectId(newStockVehiclePhoto._id.toString()),
      bill: new Types.ObjectId(newStockVehicleBill._id.toString()), // Assign bill ID
      status: 'invoiced',
      vehicleStatus: UpdatedVehicleStatus.inactive,
      category: VehicleCategory['in-preparation'],
      subCategory: VehicleSubCategory.default,
    });

    Object.assign(newStockVehicle, updateProps);
    const { userId } = req.userId
    const historyUpdate = {
      // ...historyData,
      step: 'VEHICULO CREADO',
      description: '',
      userId: new Types.ObjectId(userId),
    }

    newStockVehicle.updateHistory.push(historyUpdate);
    newStockVehicle.gpsNumber = carNumber
    newStockVehicle.gpsSerie = carNumber
    newStockVehicle.status = 'invoiced'
    newStockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
    newStockVehicle.category = VehicleCategory['in-preparation']
    newStockVehicle.subCategory = VehicleSubCategory.default;

    // ** Initial Save to get ID **
    await newStockVehicle.save();


    try {
      const qrCodeId = await generateAndUploadQrCode(newStockVehicle._id, newStockVehicle.carNumber);
      if (qrCodeId) {
        newStockVehicle.qrCode = qrCodeId; // Assign the generated QR code document ID
      } else {
        logger.warn(`[addStockVehicle] QR Code generation/upload failed for vehicle ${newStockVehicle.carNumber}, continuing without QR code.`);

      }
    } catch (qrError: any) {
      logger.error(`[addStockVehicle] Unexpected error during QR Code generation for vehicle ${newStockVehicle.carNumber}: ${qrError.message}`, qrError);

    }


    await newStockVehicle.save();
    await contract.save();

    return res.status(201).json({
      message: stockVehiclesText.success.vehicleAddedToStock,
      stock: newStockVehicle,
      contract,
    });
  } catch (error) {
    logger.error(`[addStockVehicle] Error creating stock vehicle: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error })
  }
};

export const createContract: AsyncController = async (req, res) => {
  const { vehicleId } = req.params

  const vehicleStock = await StockVehicle.findById(vehicleId)
  if (!vehicleStock) {
    return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound })
  }
  const contract: Express.Multer.File | undefined = req.file;

  const removeSpacesNamefile = removeEmptySpacesNameFile(contract);

  try {
    if (contract && vehicleStock) {
      const newStockVehicleContract = new Document({
        originalName: removeSpacesNamefile,
        path: `stock/${vehicleStock.carNumber}/contract/${removeSpacesNamefile}`,
        vehicleId: vehicleStock?._id,
      });
      await uploadFile(contract, removeSpacesNamefile, `stock/${vehicleStock?.carNumber}/contract/`)
      await newStockVehicleContract.save();

      // vehicleStock.contract = newStockVehicleContract._id
      vehicleStock.step.stepName = steps.contractCreated.name
      vehicleStock.step.stepNumber = steps.contractCreated.number

      vehicleStock.updateHistory.push({
        userId: req.userId.userId,
        step: 'CONTRATO GENERADO',
        description: '',
        time: getCurrentDateTime(),
      })

      const lastAssociate =
        await Associate.findById(vehicleStock.drivers[vehicleStock.drivers.length - 1]?._id)

      if (lastAssociate) {
        lastAssociate.unSignedContractDoc = newStockVehicleContract._id
        await lastAssociate.save();
      }

      // vehicleStock.contract = newStockVehicleContract._id
      await vehicleStock.save()
    }
    const currentAssociate =
      await Associate.findById(vehicleStock.drivers[vehicleStock.drivers.length - 1]?._id)
    return res.status(200)
      .send({ message: stockVehiclesText.success.contractCreated, vehicleStock, currentAssociate })
  } catch (error) {
    console.error('[CREATE CONTRACT]', error);
    return res.status(400).send({ message: stockVehiclesText.errors.error, error })
  }
}

/* EDIT DESCRIPTION 🖍️ */

export const editDescription: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  // const { bill } = files;

  const { historyData } = req.body

  const updateProps: { [key: string]: string | undefined | boolean } = {}

  // Enumera todas las propiedades que pueden actualizarse
  const allowedProperties = [
    'carNumber',
    'model',
    'brand',
    'version',
    'year',
    'color',
    'vin',
    'owner',
    'billAmount',
    'billNumber',
    'km',
    "transferredTo",
    'billDate',
    'receptionDate',
    'isElectric',
    'platform',
  ];

  const bill: Express.Multer.File | undefined = req.file;

  try {

    const vehicleExist = await StockVehicle.findById(vehicleId);
    if (!vehicleExist) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound })

    // Itera a través de las propiedades permitidas y agrega las que existen en req.body
    allowedProperties.forEach(prop => {
      if (prop in req.body) {

        if (typeof req.body[prop] === 'string') {
          updateProps[prop] = req.body[prop].trim();
          if (prop === 'transferredTo') {
            // console.log(req.body[prop])
            updateAccountProject(
              vehicleExist.carNumber,
              vehicleExist.drivers.length === 1 ? 0 : vehicleExist.drivers.length,
              req.body[prop]
            );
          }
        } else updateProps[prop] = req.body[prop];
        if (prop === 'transferredTo') {
          // console.log(req.body[prop])
          updateAccountProject(
            vehicleExist.carNumber,
            vehicleExist.drivers.length===1 ? 0: vehicleExist.drivers.length,
            req.body[prop]
          );
        }
      }
    });


    if (bill) {
      const removeSpacesNamefile = removeEmptySpacesNameFile(bill);

      const oldBill = await Document.findById(vehicleExist.bill?._id)

      if (oldBill) {
        // vehicleExist.oldDocuments.push(oldBill)
        // oldBill.originalName = removeSpacesNamefile
        // oldBill.path = `stock/${vehicleExist.carNumber}/bill/${removeSpacesNamefile}`
        // await oldBill.save()
        vehicleExist.oldDocuments.push(oldBill)

      }


      await uploadFile(bill, removeSpacesNamefile, `stock/${vehicleExist.carNumber}/bill/`)

      const newStockVehicleBill = new Document({
        originalName: removeSpacesNamefile,
        path: `stock/${vehicleExist.carNumber}/bill/${removeSpacesNamefile}`,
        vehicleId: vehicleExist._id,
      });

      await newStockVehicleBill.save();

      vehicleExist.bill = newStockVehicleBill._id

    }

    const historyUpdate = {
      ...historyData,
      userId: new Types.ObjectId(historyData.userId),
    }

    vehicleExist.updateHistory.push(historyUpdate);

    if (req.body.receptionDate) vehicleExist.receptionDate = req.body.receptionDate;

    checkAndUpdateVehicleStatus(vehicleExist, req.userId.userId)

    if (
      vehicleExist.step?.stepName === steps.vehicleReady.name &&
      !req.body.receptionDate &&
      vehicleExist.category === VehicleCategory.stock
    ) {
      vehicleExist.updateHistory.push({
        userId: new Types.ObjectId(historyData.userId),
        step: 'Vehículo enviado para preparación.',
        description: '',
        time: getCurrentDateTime(),
      });

      vehicleExist.step.stepName = steps.stock.name;
      vehicleExist.step.stepNumber = steps.stock.number
      vehicleExist.status = VehicleStatus.invoiced;
      vehicleExist.vehicleStatus = UpdatedVehicleStatus.inactive;
      vehicleExist.category = VehicleCategory['in-preparation'];
      vehicleExist.subCategory = VehicleSubCategory.default;
    }

    type Cities = 'cdmx' | 'gdl' | 'tij' | 'qro' | 'mty' | 'moka' | 'pbe' | 'tol' | 'ptv' | 'tep' | 'col' | 'sal' | 'torr' | 'dur' | 'mxli' | 'her' | 'chi' | 'leo' | 'ags' | 'slp' | 'mer';

    const existingVehicleContract = await Contract.findOne({ stockVehicleId: vehicleExist._id });
    if (existingVehicleContract) {
      if (!vehicleExist.vehicleState || 
      vehicleExist.vehicleState !== cities[existingVehicleContract.region]) {
        logger.info(`[editDescription] - Region mismatch found. Changing vehicle ${vehicleExist._id} region to ${cities[existingVehicleContract.region]}`);
        vehicleExist.vehicleState = cities[existingVehicleContract.region] as Cities; // Update vehicle state based on contract region
      }
    }

    await vehicleExist.save();

    return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })

  } catch (error: any) {

    if (error.code === 11000 && error.keyValue.vin) {

      const vehicleExist = await StockVehicle.findOne({ vin: error.keyValue.vin });
      if (vehicleExist) {
        return res.status(409).send({ message: `VIN o Serie registrado en el vehiculo : ${vehicleExist.carNumber}` });
      }
    }
    console.error('[UPDATE DESCRIPTION]', error.message);

    return res.status(500).send({ message: stockVehiclesText.errors.error, error })
  }
}


/* QUERY STOCK */

export const queryGetStock: AsyncController = async (req, res) => {
  const { model, brand, carNumber } = req.query;
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(403).json({ message: ' Authorization required! ' });
  }
  jwt.verify(token, accessTokenSecret);
  try {
    const aggModelBrand = [
      {
        $search: {
          compound: {
            should: [
              {
                autocomplete: {
                  query: brand ? brand : ' ',
                  path: 'brand',
                  fuzzy: {
                    maxEdits: 1,
                  },
                },
              },
              {
                autocomplete: {
                  query: model ? model : ' ',
                  path: 'model',
                  fuzzy: {
                    maxEdits: 1,
                  },
                },
              },
            ],
            minimumShouldMatch: 1,
          },
        },
      },
      {
        $project: {
          _id: 1,
          model: 1,
          brand: 1,
          carNumber: 1,
        },
      },
    ];

    const aggCarNumber = [
      {
        $search: {
          autocomplete: {
            query: carNumber,
            path: 'carNumber',
          },
        },
      },
      {
        $project: {
          _id: 1,
          model: 1,
          brand: 1,
          carNumber: 1,
        },
      },
    ];

    let query;

    if (carNumber) {
      query = await StockVehicle.aggregate(aggCarNumber);
      return res.status(200).send(query);
    } else {
      query = await StockVehicle.aggregate(aggModelBrand);
      return res.status(200).send(query);
    }
  } catch (error) {
    return res.status(400).send(error);
  }
};


// type Query = z.infer<typeof querySchema>;

export const getAllStock: AsyncController = async (req, res) => {
  try {
    logger.info(`[getAllStock] Starting to fetch all stock for userId: ${req.userId.userId}`);
    const { userId } = req.userId;

    logger.info('[getAllStock] Validating query parameters');
    const validatedQuery = getStockQuerySchema.parse(req.query);
    const {
      page = 0,
      limit,
      status,
      listStatus,
      city,
      stepNumber,
      isNew,
      excludeStatus,
      country,
      isElectric,
      platform,
      reason,
      vehicleStatus,
      category,
      subCategory,
    } = validatedQuery;

    const currentUser = await User.findById(userId);
    if (!currentUser) {
      return res.status(400).send({ message: 'Usuario no encontrado' });
    }

    logger.info(`[getAllStock] User authenticated with role: ${currentUser.role}`);
    const isSuperAdminOrAdmin = currentUser.role === 'superadmin' || currentUser.role === 'administrador';

    const allowedRegions = currentUser.settings.allowedRegions;

    const statusFilter = listStatus ? { $in: (listStatus as string).split(',') } : status ? { $in: [status] } : { $in: [vehicleStatus] };

    let queryFilter: Record<string, any> =  {}

    if (country) {
      queryFilter = { ...queryFilter, country: country }
      if(country === CountriesEnum.Mexico){
        // doing this to get the vehicles from mexico and the ones that are not setted because we added the country field
        // during integration of US Flows.
        queryFilter = { ...queryFilter, country: { $in: [CountriesEnum.Mexico, undefined] }   }
      }
    }

    if (isElectric) {
      const bool = isElectric === 'true'
      queryFilter = { ...queryFilter, $or: [{ isElectric: bool }, { isElectric: { $exists: false } }] }

    }

    if (platform) {
      queryFilter = { ...queryFilter, platform }
    }

    if (reason) {
      queryFilter = { ...queryFilter, 'dischargedData.reason': reason }
    }

    if (city) {
      queryFilter = { ...queryFilter, vehicleState: city }
    }

    if (stepNumber) {
      queryFilter = { ...queryFilter, 'step.stepNumber': stepNumber }
    }

    if (isNew) {
      const bool = isNew === 'true'
      queryFilter = { ...queryFilter, newCar: bool }
    }


    if (statusFilter && !status && !vehicleStatus) {
      queryFilter = { ...queryFilter, status: statusFilter }
    }

    if (status){
      queryFilter = { ...queryFilter, status: { $in: [status] } }
    }

    if (vehicleStatus){
      queryFilter = { ...queryFilter, vehicleStatus: { $in: [vehicleStatus] } }
    }

    if (category){
      queryFilter = { ...queryFilter, category: { $in: [category] } }
    }

    logger.info(`[getAllStock] Fetching initial stock with base filters: ${JSON.stringify(queryFilter)}`);
    const stock = await StockVehicle.find(queryFilter, { subCategory: 1 }).lean();

    if (subCategory){
      queryFilter = { ...queryFilter, subCategory: { $in: [subCategory] } }
    }

    if (excludeStatus){

      // filter the previous status setted, excluding this status,
      // so if is coming a list of status: invoiced, active, discharged, stock
      // if excludeStatus comes with active, invoiced, the query will exclude active and invoiced
      // and will return only discharged and stock to the query

      const excludeStatusArray = excludeStatus.split(',')

      const currentStatus = queryFilter.status.$in

      const exists = excludeStatusArray.some((s: string) => currentStatus.includes(s))

      if (exists){
        queryFilter = { ...queryFilter, status: undefined }
      }

    }

    logger.info('[getAllStock] Calculating subcategory counts');
    const subcategoryCounts: Record<VehicleSubCategoryType, number> = Object.values(VehicleSubCategory)
    .reduce((acc, subcat) => {
      acc[subcat] = stock.filter(item => item.subCategory === subcat).length;
      return acc;
    }, {} as Record<VehicleSubCategoryType, number>);

    if (country || page || limit || status || listStatus || city || stepNumber || isNew || excludeStatus
       || vehicleStatus || category || subCategory) {
      if (!isSuperAdminOrAdmin) {
        queryFilter.vehicleState = { $in: allowedRegions };
      }

      logger.info('[getAllStock] Fetching filtered stock with pagination');
      const stockAdmin = await StockVehicle.find(queryFilter, {
          carNumber: 1,
          brand: 1,
          model: 1,
          status: 1,
          vehicleStatus: 1,
          category: 1,
          vin:1,
          subCategory: 1,
          newCar: 1,
          step: 1,
          vehicleDocsComplete: 1,
          extensionCarNumber: 1,
          dischargedData: 1,
          color: 1,
          createdAt:1,
          vehicleState: 1,
          carPlates: 1,
          isElectric: 1,
          contract: 1,
        }).populate({ path: 'contract', select:'contractNumber' })
        .skip(Number(page || '0') * Number(limit))
        .limit(Number(limit))
        .sort({ carNumber: 1, createdAt: -1 })
        .collation({ locale: "en_US", numericOrdering: true })
        .exec();

      const totalCount = await StockVehicle.countDocuments(queryFilter);
      logger.info(`[getAllStock] Found ${totalCount} total vehicles matching criteria`);
      if (stockAdmin.length === 0) {
        let response = [
        {
          subcategoryCounts,
        },
        ]
        return res.status(200).send({
          stock: response,
          totalCount,
          message: "Vehiculos encontrados",
        });
      } else {
        return res.status(200).send({
          //stock: stockAdmin,
          stock: stockAdmin.map(item => ({
            ...item.toObject(), // Convert Mongoose document to plain object
            subcategoryCounts,
          })),
          totalCount,
          message: "Vehiculos encontrados",
        });
      }
    }

    logger.info('[getAllStock] Fetching all stock without filters');
    const stockAdmin = await StockVehicle.find(
      isSuperAdminOrAdmin ? {} : { vehicleState: { $in: allowedRegions } }, {
      carNumber: 1,
      brand: 1,
      model: 1,
      status: 1,
      newCar: 1,
      step: 1,
      vin:1,
      vehicleDocsComplete: 1,
      extensionCarNumber: 1,
      dischargedData: 1,
      color: 1,
      createdAt:1,
      vehicleState: 1,
        isElectric: 1,
    }).sort({ carNumber: 1 }).collation({ locale: "en_US", numericOrdering: true })

    logger.info(`[getAllStock] Successfully fetched ${stockAdmin.length} vehicles`);
    return res.status(200).send(
      stockAdmin.map(item => ({
      ...item.toObject(),
      subcategoryCounts,
      }))
    );
  } catch (error) {
      if (error instanceof z.ZodError) {
        logger.error(`[getAllStock] Validation error: ${JSON.stringify(error.errors)}`);
        return res.status(400).json({ error: error.errors });
      }
      logger.error(`[getAllStock] Server error: ${error}`);
      return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const filterStock: AsyncController = async (req, res) => {
  try {
    logger.info(`[filterStock] Starting to filter stock with search: ${req.query.search}, status: ${req.query.vehicleStatus}, category: ${req.query.category}, country: ${req.query.country}`);

    const { search: s, vehicleStatus, category, subCategory, country } = req.query || '';
    const { userId } = req.userId;

    const search = s?.toString().trim() || '';

    const currentUser = await User.findById(userId);
    if (!currentUser) {
      return res.status(400).send({ message: 'Usuario no encontrado' });
    }

    logger.info(`[filterStock] User found with role: ${currentUser.role}`);
    const isAdminOrSuper = currentUser.role === 'superadmin' || currentUser.role === 'administrador';
    const allowedRegions = currentUser.settings.allowedRegions;

    let findOptionsAdmin: any = {
      $or: [
        { brand: { $regex: search, $options: 'i' } },
        { model: { $regex: search, $options: 'i' } },
        { carNumber: { $regex: search, $options: 'i' } },
        { vin: { $regex: search, $options: 'i' } },
        { year: { $regex: search, $options: 'i' } },
        { 'carPlates.plates': { $regex: search, $options: 'i' } },
        { 'circulationCard.number': { $regex: search, $options: 'i' } },
      ],
    };

    let findOptions: any = {
      $or: [
        { brand: { $regex: search, $options: 'i' } },
        { model: { $regex: search, $options: 'i' } },
        { carNumber: { $regex: search, $options: 'i' } },
        { vin: { $eq: search } },
        { 'carPlates.plates': { $eq: search } },
      ],
      vehicleState: { $in: allowedRegions },
    };

    const findDriversOptions: any = {
      $or: [
        { firstName: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' }, lastName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { curp: { $regex: search, $options: 'i' } },
        { rfc: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: /^\d+$/.test(search) ? { $eq: Number(search) } : null },
      ],
    };

    logger.info('[filterStock] Searching for associates matching criteria');
    const latestAssociate = await Associate.find(findDriversOptions)
    const driversIds = latestAssociate.map((associate) => associate._id)
    if (latestAssociate) {
      findOptionsAdmin = {
        $or: [
          { brand: { $regex: search, $options: 'i' } },
          { model: { $regex: search, $options: 'i' } },
          { carNumber: { $regex: search, $options: 'i' } },
          { vin: { $eq: search } },
          { 'carPlates.plates': { $eq: search } },
          { drivers: { $elemMatch: { _id: { $in: driversIds } } } },
        ],
      }
      findOptions = {
        $or: [
          { brand: { $regex: search, $options: 'i' } },
          { model: { $regex: search, $options: 'i' } },
          { carNumber: { $regex: search, $options: 'i' } },
          { vin: { $eq: search } },
          { 'carPlates.plates': { $eq: search } },
          { drivers: { $elemMatch: { _id: { $in: driversIds } } } },
        ],
        vehicleState: { $in: allowedRegions },
      }
    }

    if (vehicleStatus) {
      findOptionsAdmin.vehicleStatus = vehicleStatus;
      findOptions.vehicleStatus = vehicleStatus;
    }

    if (category) {
      findOptionsAdmin.category = category;
      findOptions.category = category;
    }
    if (country && (country === CountriesEnum.Mexico || country === CountriesEnum.Mexico.toLowerCase())) {
      findOptionsAdmin.country = { $in: [CountriesEnum[country as keyof typeof CountriesEnum], undefined] };
      findOptions.country = { $in: [CountriesEnum[country as keyof typeof CountriesEnum], undefined] };
    } else if (country) {
      findOptionsAdmin.country = country;
      findOptions.country = country;
    }

    // if (subCategory) {
    //   findOptionsAdmin.subCategory = subCategory;
    //   findOptions.subCategory = subCategory;
    // }

    const options = isAdminOrSuper ? findOptionsAdmin : findOptions;

    logger.info('[filterStock] Fetching stock vehicles with filters');
    const stock = await StockVehicle.find(options).lean();

    const categoryCount = stock.filter((item) => item.category === category).length;

    const subcategoryCounts: Record<VehicleSubCategoryType, number> = Object.values(VehicleSubCategory)
    .reduce((acc, subcat) => {
      acc[subcat] = stock.filter(item => item.subCategory === subcat).length;
      return acc;
    }, {} as Record<VehicleSubCategoryType, number>);

    const filteredStock = subCategory
    ? stock.filter(item => item.subCategory === subCategory)
    : stock;

    logger.info(`[filterStock] Successfully filtered stock. Found ${filteredStock.length} vehicles matching criteria`);

    if (filteredStock.length === 0) {
      let response = [
{
        subcategoryCounts,
        categoryCount,
      },
      ]
      return res.status(200).send(
        response
      );
    } else {
      return res.status(200).send(
        filteredStock?.map(item => ({
            ...item,
            subcategoryCounts: subcategoryCounts,
            categoryCount,
          }))
      );
    }
  } catch (error) {
    logger.error(`[filterStock] Error filtering stock vehicles: ${error}`);
    return res.status(404).send({ message: stockVehiclesText.errors.stockVehiclesNotFound });
  }
};

export const dashboardStats: AsyncController = async (req, res) => {
  try {
    logger.info(`[dashboardStats] Starting to fetch dashboard static data`);
    const chartData = {
      label: 'Suma total: 2547 ',
      labels: [
        "CDMX",
        "GDL",
        "MTY",
        "QRO",
        "TIJ",
        "PBE",
        "PTV",
        "SAL",
        "TORR",
        "MXLI",
        "HER",
        "CHI",
        "LEO",
        "AGS",
        "SLP",
        "MER",
      ],
      indicators: [
        931,
        200,
        649,
        58,
        376,
        89,
        26,
        20,
        18,
        72,
        17,
        8,
        18,
        7,
        5,
        53,
      ],
    };

    const categoryData = {
      vehicleStatusCounts: {
        Active: 2455,
        Inactive: 866,
        Total: 3321,
      },
      categoryCounts: {
        "in-preparation": { count: 242 },
        stock: { count: 237 },
        assigned: { count: 39 },
        delivered: { count: 0 },
        insurance: { count: 34, subcategories: { "damage-payment": 28, valuation: 3, repair: 3 } },
        workshop: {
          count: 91,
          subcategories: {
            "aesthetic-repair": 33,
            "duplicate-key-missing": 0,
            "mechanical-repair": 39,
            "electrical-repair": 8,
            "engine-repair": 6,
            "waiting-for-parts": 5,
            "corrective-maintenance": 0,
          },
        },
        revision: {
          count: 76,
          subcategories: {
            "aesthetic-repair": 21,
            "duplicate-key-missing": 3,
            "mechanical-repair": 20,
            "electrical-repair": 13,
            "engine-repair": 0,
            "waiting-for-parts": 8,
            "corrective-maintenance": 5,
            management: 3,
            gps: 3,
          },
        },
        legal: { count: 36, subcategories: { demand: 11, "public-ministry": 15, complaint: 0, impounded: 8 } },
        collection: { count: 78, subcategories: { "payment-commitment": 0, "payment-extension": 0, "non-payment": 16, "incomplete-payment": 0, "in-recovery": 62 } },
        withdrawn: { count: 30, subcategories: { "total-loss": 29, "operational-loss": 1 } },
        sold: { count: 0 },
        adendum: { count: 0 },
        utilitary: { count: 3 },
      },
    };

    const data = {
      chartData,
      categoryData,
    }

    logger.info(`[dashboardStats] Successfully generated dashboard data`);
    return res.status(200).send({ message: 'success', data });
  } catch (error: any) {
    logger.error(`[dashboardStats] Error fetching dashboard data: ${error.message}`);
    return res.status(500).send({ message: error.message });
  }
};

export const dashboardStatusData: AsyncController = async (req, res) => {
  try {
    logger.info(`[dashboardStatusData] Starting to fetch dashboard data with country: ${req.query.country || 'default'}`);
    const { country } = req.query || '';

    const queryCountry = country || CountriesEnum.Mexico;

    const countryCondition =
      queryCountry === CountriesEnum['United States']
        ? { country: CountriesEnum['United States'] }
        : { $or: [{ country: CountriesEnum.Mexico }, { country: { $exists: false } }] };

    // Main category counts
    logger.info('[dashboardStatusData] Fetching vehicle counts for main categories');
    const vehicleCounts = await Promise.all([
      StockVehicle.countDocuments({
        vehicleStatus: UpdatedVehicleStatus.active,
        ...countryCondition,
      }),
      StockVehicle.countDocuments({
        vehicleStatus: UpdatedVehicleStatus.inactive,
        ...countryCondition,
      }),
      ...Object.values(VehicleCategory).map((category) =>
        StockVehicle.countDocuments({
          category,
          ...countryCondition,
        })
      ),
    ]);

    type SubcategoryCountsType = Record<string, Record<string, number>>;

    const subcategoriesByCategory: Record<string, string[]> = {
      insurance: [VehicleSubCategory['damage-payment'], VehicleSubCategory.valuation, VehicleSubCategory.repair],
      workshop: [
        VehicleSubCategory['aesthetic-repair'],
        VehicleSubCategory['duplicate-key-missing'],
        VehicleSubCategory['mechanical-repair'],
        VehicleSubCategory['electrical-repair'],
        VehicleSubCategory['engine-repair'],
        VehicleSubCategory['waiting-for-parts'],
        VehicleSubCategory['corrective-maintenance'],
      ],
      revision: [
        VehicleSubCategory['aesthetic-repair'],
        VehicleSubCategory['duplicate-key-missing'],
        VehicleSubCategory['mechanical-repair'],
        VehicleSubCategory['electrical-repair'],
        VehicleSubCategory['engine-repair'],
        VehicleSubCategory['waiting-for-parts'],
        VehicleSubCategory['corrective-maintenance'],
        VehicleSubCategory.management,
        VehicleSubCategory.gps,
      ],
      legal: [VehicleSubCategory.demand, VehicleSubCategory['public-ministry'], VehicleSubCategory.complaint, VehicleSubCategory.impounded],
      collection: [
        VehicleSubCategory['payment-commitment'],
        VehicleSubCategory['payment-extension'],
        VehicleSubCategory['non-payment'],
        VehicleSubCategory['incomplete-payment'],
        VehicleSubCategory['in-recovery'],
      ],
      withdrawn: [VehicleSubCategory['total-loss'], VehicleSubCategory['operational-loss']],
    };

    logger.info('[dashboardStatusData] Fetching subcategory counts');
    const subcategoryCounts: SubcategoryCountsType = {};

    for (const [category, subcategories] of Object.entries(subcategoriesByCategory)) {
      subcategoryCounts[category] = {};

      const counts = await Promise.all(
        subcategories.map((subcategory) => {
          const query = {
            category,
            subCategory: subcategory,
            ...countryCondition,
          };
          return StockVehicle.countDocuments(query);
        })
      );

      subcategories.forEach((subcategory, index) => {
        subcategoryCounts[category][subcategory] = counts[index];
      });
    }

    const activeCount = vehicleCounts[0];
    const inactiveCount = vehicleCounts[1];
    const totalCount = activeCount + inactiveCount;

    const dashboardData = {
      vehicleStatusCounts: {
        Active: activeCount,
        Inactive: inactiveCount,
        Total: totalCount,
      },
      categoryCounts: {
        [VehicleCategory['in-preparation']]: { count: vehicleCounts[10] },
        [VehicleCategory.stock]: { count: vehicleCounts[11] },
        [VehicleCategory.assigned]: { count: vehicleCounts[12] },
        [VehicleCategory.delivered]: { count: vehicleCounts[13] },
        [VehicleCategory.insurance]: { count: vehicleCounts[4],
          subcategories: subcategoryCounts.insurance || {} },
        [VehicleCategory.workshop]: { count: vehicleCounts[7],
          subcategories: subcategoryCounts.workshop || {} },
        [VehicleCategory.revision]: { count: vehicleCounts[8],
          subcategories: subcategoryCounts.revision || {} },
        [VehicleCategory.legal]: { count: vehicleCounts[6], subcategories: subcategoryCounts.legal || {} },
        [VehicleCategory.collection]: { count: vehicleCounts[5],
          subcategories: subcategoryCounts.collection || {} },
        [VehicleCategory.withdrawn]: { count: vehicleCounts[2],
          subcategories: subcategoryCounts.withdrawn || {} },
        [VehicleCategory.sold]: { count: vehicleCounts[3] },
        [VehicleCategory.adendum]: { count: vehicleCounts[9] },
        [VehicleCategory.utilitary]: { count: vehicleCounts[14] },
      },
    };

    logger.info(`[dashboardStatusData] Successfully fetched dashboard data with ${activeCount} active, ${inactiveCount} inactive vehicles (total: ${totalCount})`);
    return res.status(200).send({ message: 'success', dashboardData });
  } catch (error: any) {
    logger.error(`[dashboardStatusData] Error fetching dashboard data: ${error.message}`);
    return res.status(500).send({ message: error.message });
  }
};

export const updateStockVehicle: AsyncController = async (req, res) => {
  const { id } = req.params;
  const { stepName, receptionDate } = req.body;

  if (Object.keys(req.body).length === 0) {
    return res.status(400).send({ message: stockVehiclesText.errors.missingBody });
  }
  const selectedVehicle = await StockVehicle.findById(id);
  if (!selectedVehicle) {
    return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
  }

  try {
    if (stepName !== 'Vehiculo listo') {
      return res.status(400).json({ message: ' Nombre de paso incorrecto! ' });
    }

    if (receptionDate) {
      selectedVehicle.receptionDate = receptionDate;
    }
    const { userId } = req.userId;

    checkAndUpdateVehicleStatus(selectedVehicle, userId)

    await selectedVehicle.save();
    return res.status(200).send({
      message: 'Vehiculo enviado a stock', vehicle: selectedVehicle,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const deleteStockVehicle: AsyncController = async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  const { id } = req.params;
  if (!token) {
    return res.status(403).json({ message: ' Authorization required! ' });
  }
  jwt.verify(token, accessTokenSecret);

  try {
    const stockSearch = StockVehicle.findById(id);
    if (!stockSearch) {
      await StockVehicle.deleteOne({ _id: id });
    } else {
      return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
    }
    return res.status(201).send({ message: stockVehiclesText.success.vehicleDeletedFromStock });
  } catch (error) {
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const getStockVehicleById: AsyncController = async (req, res) => {
  const { id } = req.params;
  if (!id) return res.status(404).send({ message: 'ID requeried' });

  try {
    const stockVehicle = await StockVehicle.findById(id).lean();
    if (!stockVehicle) return res.status(404).send({ message: 'Stock vehicle not found' });

    const carPlates = await replaceSingleObjectWithDocUrl(stockVehicle?.carPlates, ['frontImg', 'backImg', 'platesDocument']);
    const circulationCard = await replaceSingleObjectWithDocUrl(stockVehicle?.circulationCard, ['frontImg', 'backImg']);

    const contract = stockVehicle.contract ? await replaceDocWithUrl(stockVehicle?.contract?.toString() || '') : null

    const policiesArrayWithUrls = await mapReplaceObjectWithDocUrl(stockVehicle?.policiesArray, 'policyDocument');

    const tenancyWithUrls = await mapReplaceObjectWithDocUrl(stockVehicle?.tenancy, 'tenancyDocument');


    // const vehiclePhoto = await replaceDocWithUrl(stockVehicle.vehiclePhoto?.toString() || '');

    const bill = await replaceDocWithUrl(stockVehicle?.bill?.toString() || '');

    // Get QR code URL if it exists
    const qrCode = await replaceDocWithUrl(stockVehicle?.qrCode?.toString() || '');

    const historyInfo = await getUpdateHistoryWithUserInfo(stockVehicle?.updateHistory);

    // Fetch QR scan history for this vehicle
    const qrScanHistory = await getQRScanHistoryForVehicle(stockVehicle._id);

    let readmissions: any[] = []
    const readmissionsDb = await Readmission.find({ stockVehicleId: stockVehicle._id })
      .sort({ readmissionDate: -1 })
      // .populate('associateId')
      .populate('promissoryNote')
      .populate('readmissionDoc')
      .populate('contractCanceled')
      .populate('signedPromissoryNote')
      .lean();

    if (readmissionsDb.length > 0) {
      let i = 0;
      for (let readmission of readmissionsDb) {
        const kmImgs = await mapReplaceArrayOfDocsId(readmission?.kmImgs);
        const evidenceImgs = await mapReplaceArrayOfDocsId(readmission?.evidenceImgs);
        const readmissionDoc = await replaceDocWithUrl(readmission?.readmissionDoc?._id?.toString())
        const promissoryNote = await replaceDocWithUrl(readmission?.promissoryNote?._id?.toString())
        const contractCanceled = await replaceDocWithUrl(readmission?.contractCanceled?._id?.toString())
        const signedPromissoryNote =
          await replaceDocWithUrl(readmission?.signedPromissoryNote?._id?.toString())

        const associate = await Associate.findById(readmission.associateId).lean();
        let docs: any;
        let signDocs: any;
        let bankStatements: any;
        let unSignedContractDoc: any;
        let picture: any;
        let terminationFiles: any;
        if (associate) {
          docs = await replaceSingleObjectWithDocUrlSureste(associate?.documents, ['ineFront', 'ineBack', 'curp', 'addressVerification', 'taxStatus', 'driverLicenseFront', 'driverLicenseBack'])
          signDocs = await replaceSingleObjectWithDocUrlSureste(associate?.signDocs, ['contract', 'promissoryNote', 'deliveryReceipt', 'warranty', 'invoice', 'privacy', 'contactInfo'])
          picture = await replaceDocWithUrl(associate?.picture?._id?.toString());
          bankStatements = await replaceSingleObjectWithDocUrlSureste(associate?.bankStatement, ['bankStatementOne', 'bankStatementTwo', 'bankStatementThree', 'bankStatementFour', 'bankStatementFive', 'bankStatementSix'])
          unSignedContractDoc = await replaceDocWithUrl(associate?.unSignedContractDoc?.toString())
          terminationFiles = await replaceSingleObjectWithDocUrlSureste(readmission?.terminationFiles, ['promissoryNote', 'agreement', 'promissoryNoteSigned', 'agreementSigned', 'recessionSigned'])
        }
        readmissions.push({
          ...readmission,
          deliveredDate: stockVehicle.deliveredDate[i],
          associate: {
            firstName: associate?.firstName,
            lastName: associate?.lastName,
            birthDay: associate?.birthDay,
            curp: associate?.curp,
            phone: associate?.phone,
            rfc: associate?.rfc,
            email: associate?.email,
            picture,
            documents: {
              ine: {
                ineFront: docs?.ineFront,
                ineBack: docs?.ineBack,
              },
              driverLicense: {
                driverLicenseFront: docs?.driverLicenseFront,
                driverLicenseBack: docs?.driverLicenseBack,
              },
              curp: docs?.curp,
              addressVerification: docs?.addressVerification,
              taxStatus: docs?.taxStatus,
            },
            address: {
              addressStreet: associate?.addressStreet,
              exterior: associate?.exterior,
              interior: associate?.interior,
              colony: associate?.colony,
              delegation: associate?.delegation,
              city: associate?.city,
              state: associate?.state,
              postalCode: associate?.postalCode,
            },
            unSignedContractDoc,
            signDocs,
            bankStatements,
          },
          kmImgs,
          evidenceImgs,
          readmissionDoc,
          promissoryNote,
          contractCanceled,
          signedPromissoryNote,
          terminationFiles,
        })
      }
    }
    const drivers = await associateService.getAssociateByVehicle(stockVehicle);

    let lastDeliveredDate = null
    if (stockVehicle.deliveredDate) {
      lastDeliveredDate = stockVehicle.deliveredDate[stockVehicle.deliveredDate.length - 1] || null
    }

    let dischargedData = null
    if (stockVehicle.dischargedData) {
      const data = stockVehicle.dischargedData;
      const dischargedDataWithDocs = await replaceSingleObjectWithDocUrlSureste(data, ['platesDischargedDoc', 'reportDoc', 'dictamenDoc'])
      dischargedData = dischargedDataWithDocs
    }

    const purchaseAgreement = await replaceDocWithUrl(stockVehicle?.purchaseAgreement?.toString() || '');
    const soldInvoicePdf = await replaceDocWithUrl(stockVehicle?.soldInvoicePdf?.toString() || '');
    const soldInvoiceXml = await replaceDocWithUrl(stockVehicle?.soldInvoiceXml?.toString() || '');
    const platesCancelation = await replaceDocWithUrl(stockVehicle?.platesCancelation?.toString() || '');

    const dataToSend = {
      ...stockVehicle,
      carPlates,
      tenancy: tenancyWithUrls,
      policiesArray: policiesArrayWithUrls,
      // vehiclePhoto,
      updateHistory: historyInfo,
      qrScanHistory,
      bill,
      contract,
      circulationCard,
      drivers,
      firstDeliveryDate: stockVehicle.deliveredDate?.length > 0 ? stockVehicle.deliveredDate[0] : null,
      allDeliveries: stockVehicle.deliveredDate,
      deliveredDate: lastDeliveredDate,
      readmissions,
      dischargedData,
      purchaseAgreement,
      soldInvoicePdf,
      soldInvoiceXml,
      platesCancelation,
      qrCode, // Add QR code to the response
    };

    return res.status(200).send({ message: 'Información del vehiculo de stock', data: dataToSend });
  } catch (error) {
    console.log('[STOCK GET BY ID]', error)
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const getStockVehicleByIdV2: AsyncController = async (req, res) => {
  const { id } = req.params;
  if (!id) return res.status(404).send({ message: 'ID requeried' });

  try {
    const stockVehicle = await StockVehicle.findById(id)
      .populate('tenancy.tenancyDocument policiesArray.policyDocument circulationCard.frontImg circulationCard.backImg')
      .populate('carPlates.frontImg carPlates.backImg carPlates.platesDocument qrCode')
      // .populate('policiesArray.policyDocument')
      // .populate({
      //   path: 'drivers',
      //   populate: {
      //     path: '_id',
      //     model: 'Associate',
      //   },
      // })
      // .populate({
      //   path: 'drivers.documents',
      //   model: 'Document',
      // })
      .lean();
    if (!stockVehicle) return res.status(404).send({ message: 'Stock vehicle not found' });

    return res.status(200).send({ message: 'Información del vehiculo de stock', data: stockVehicle });
  } catch (error) {
    console.log('[STOCK GET BY ID]', error)
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

// UPDATE STEPS
// PLATES UPDATE

export const createAndUpdateCarPlates: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  try {
    // const images = req.files as Express.Multer.File[];
    const { historyData, isEditing, plates } = req.body
    const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

    const frontImgFiles = ('frontImg' in files) ? files.frontImg[0] : undefined;
    const backImgFiles = ('backImg' in files) ? files.backImg[0] : undefined;
    const platesDocumentFile = ('platesDocument' in files) ? files.platesDocument[0] : undefined;

    const removeSpacesFrontImg = removeEmptySpacesNameFile(frontImgFiles);
    const removeSpacesBackImg = removeEmptySpacesNameFile(backImgFiles);
    const removeSpacesPlatesDoc = removeEmptySpacesNameFile(platesDocumentFile);

    const stockVehicle = await StockVehicle.findById(vehicleId);

    if (!stockVehicle) return res.status(404).send({ message: 'Vehiculo no encontrado' });

    if (!historyData || !historyData.userId.toString() || !historyData.step || !historyData.description)
      return res.status(404)
        .send({ message: 'Información del usuario para historial de actividades requerido' });

    const historyUpdate = {
      ...historyData,
      userId: new Types.ObjectId(historyData.userId),
    }
    stockVehicle.updateHistory.push(historyUpdate);

    const isEdit = JSON.parse(isEditing);

    let carPlatesObj: any = {}

    const carNumber = stockVehicle.carNumber;
    if (!isEdit) {
      /* DENTRO DE ESTE IF QUIERE DECIR QUE NO SE ESTA EDITANDO, SE ESTA CREANDO */

      if (!plates || !historyData /* || !frontImgFiles || !backImgFiles || !platesDocumentFile */)
        return res.status(404).send({ message: stockVehiclesText.errors.missingBody });

      const { userId } = req.userId;

      if (plates) carPlatesObj.plates = plates;

      if (frontImgFiles) {
        const frontImgDoc = new Document({
          originalName: removeSpacesFrontImg,
          path: `stock/${carNumber}/` + removeSpacesFrontImg,
        });

        await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`)
        await frontImgDoc.save();
        carPlatesObj.frontImg = frontImgDoc._id

      }
      if (backImgFiles) {
        const backImgDoc = new Document({
          originalName: removeSpacesBackImg,
          path: `stock/${carNumber}/` + removeSpacesBackImg,
        });
        await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`)
        await backImgDoc.save();
        carPlatesObj.backImg = backImgDoc._id
      }

      if (platesDocumentFile) {
        const policyDoc = new Document({
          originalName: removeSpacesPlatesDoc,
          path: `stock/${carNumber}/` + removeSpacesPlatesDoc,
        });
        await uploadFile(platesDocumentFile, removeSpacesPlatesDoc, `stock/${carNumber}/`)
        await policyDoc.save();
        carPlatesObj.platesDocument = policyDoc._id
      }

      stockVehicle.carPlates = carPlatesObj

      checkAndUpdateVehicleStatus(stockVehicle, userId)

      await stockVehicle.save();

      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })

    } else {
      if (stockVehicle.carPlates) {

        const oldFrontImgDoc = await Document.findById(stockVehicle.carPlates?.frontImg);
        const oldBackImgDoc = await Document.findById(stockVehicle.carPlates?.backImg);
        const oldPolicyDoc = await Document.findById(stockVehicle.carPlates?.platesDocument)

        if (plates) stockVehicle.carPlates.plates = plates.trim()

        if (frontImgFiles && oldFrontImgDoc) {
          stockVehicle.oldDocuments.push(oldFrontImgDoc);
          oldFrontImgDoc.originalName = removeSpacesFrontImg
          oldFrontImgDoc.path = `stock/${carNumber}/${removeSpacesFrontImg}`

          await oldFrontImgDoc.save();
          await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`)

        } else if (frontImgFiles && !oldFrontImgDoc) {
          const frontImgDoc = new Document({
            originalName: removeSpacesFrontImg,
            path: `stock/${carNumber}/` + removeSpacesFrontImg,
          });

          await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`)
          await frontImgDoc.save();
          stockVehicle.carPlates.frontImg = frontImgDoc._id

        }

        if (backImgFiles && oldBackImgDoc) {
          stockVehicle.oldDocuments.push(oldBackImgDoc);
          oldBackImgDoc.originalName = removeSpacesBackImg
          oldBackImgDoc.path = `stock/${carNumber}/${removeSpacesBackImg}`

          await oldBackImgDoc.save();
          await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`)

        } else if (backImgFiles && !oldBackImgDoc) {
          const backImgDoc = new Document({
            originalName: removeSpacesBackImg,
            path: `stock/${carNumber}/` + removeSpacesBackImg,
          });

          await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`)
          await backImgDoc.save();
          stockVehicle.carPlates.backImg = backImgDoc._id

        }

        if (platesDocumentFile && oldPolicyDoc) {
          stockVehicle.oldDocuments.push(oldPolicyDoc);
          oldPolicyDoc.originalName = removeSpacesPlatesDoc
          oldPolicyDoc.path = `stock/${carNumber}/${removeSpacesPlatesDoc}`

          await uploadFile(platesDocumentFile, removeSpacesPlatesDoc, `stock/${carNumber}/`)
          await oldPolicyDoc.save();

        } else if (platesDocumentFile && !oldPolicyDoc) {
          const platesDoc = new Document({
            originalName: removeSpacesPlatesDoc,
            path: `stock/${carNumber}/` + removeSpacesPlatesDoc,
          });

          await uploadFile(platesDocumentFile, removeSpacesPlatesDoc, `stock/${carNumber}/`)
          await platesDoc.save();
          stockVehicle.carPlates.platesDocument = platesDoc._id
        }
      }
      await stockVehicle.save();
      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })
    }
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error })
  }
}

// CIRCULATION CARD UPDATE

export const createAndUpdateCirculationCard: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { historyData, isEditing, validity, number } = req.body

  const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

  const frontImgFiles = ('frontImg' in files) ? files.frontImg[0] : undefined;
  const backImgFiles = ('backImg' in files) ? files.backImg[0] : undefined;

  const removeSpacesFrontImg = removeEmptySpacesNameFile(frontImgFiles);
  const removeSpacesBackImg = removeEmptySpacesNameFile(backImgFiles);

  const stockVehicle = await StockVehicle.findById(vehicleId);

  if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

  if (!historyData || !historyData.userId.toString() || !historyData.step || !historyData.description)
    return res.status(404)
      .send({ message: stockVehiclesText.errors.historyDataMissing });

  const historyUpdate = {
    ...historyData,
    userId: new Types.ObjectId(historyData.userId),
  }
  stockVehicle.updateHistory.push(historyUpdate);

  const carNumber = stockVehicle.carNumber;

  try {
    if (!JSON.parse(isEditing)) {
      //Codigo para crear nuevas circulations cards
      if (!historyData || !number || !frontImgFiles || !backImgFiles)
        return res.status(404).send({ message: stockVehiclesText.errors.missingBody });

      const { userId } = req.userId;

      const frontImgDoc = new Document({
        originalName: removeSpacesFrontImg,
        path: `stock/${carNumber}/` + removeSpacesFrontImg,
      });
      await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`)
      await frontImgDoc.save();

      const backImgDoc = new Document({
        originalName: removeSpacesBackImg,
        path: `stock/${carNumber}/` + removeSpacesBackImg,
      });
      await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`)
      await backImgDoc.save();

      stockVehicle.circulationCard = {
        number: number,
        ...(validity && { validity }), // Only include validity if it exists
        frontImg: frontImgDoc._id,
        backImg: backImgDoc._id,
      };

      checkAndUpdateVehicleStatus(stockVehicle, userId)

      await stockVehicle.save();
      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })

    } else {
      if (stockVehicle.circulationCard) {
        if (validity) stockVehicle.circulationCard.validity = validity
        if (number) stockVehicle.circulationCard.number = number

        const oldFrontImgDoc = await Document.findById(stockVehicle.circulationCard.frontImg);
        const oldBackImgDoc = await Document.findById(stockVehicle.circulationCard.backImg);

        if (frontImgFiles) {
          if(oldFrontImgDoc){
            stockVehicle.oldDocuments.push(oldFrontImgDoc);
            oldFrontImgDoc.originalName = removeSpacesFrontImg
            oldFrontImgDoc.path = `stock/${carNumber}/${removeSpacesFrontImg}`
  
            await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`)
            await oldFrontImgDoc.save();
          } else {
            const frontImgDoc = new Document({
              originalName: removeSpacesFrontImg,
              path: `stock/${carNumber}/` + removeSpacesFrontImg,
            });
            await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/`)
            await frontImgDoc.save();
            stockVehicle.circulationCard.frontImg = frontImgDoc._id;
          }
        }
        if (backImgFiles) {
          if(oldBackImgDoc){
            stockVehicle.oldDocuments.push(oldBackImgDoc);
            oldBackImgDoc.originalName = removeSpacesBackImg
            oldBackImgDoc.path = `stock/${carNumber}/${removeSpacesBackImg}`
  
            await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`)
            await oldBackImgDoc.save();
          } else {
            const backImgDoc = new Document({
              originalName: removeSpacesBackImg,
              path: `stock/${carNumber}/` + removeSpacesBackImg,
            });
            await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/`)
            await backImgDoc.save();
            stockVehicle.circulationCard.backImg = backImgDoc._id;
          }
        }

      }

      await stockVehicle.save();
      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })
    }
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error })
  }
}

// UPDATE TIJUANA CIRCULATION CARD

export const createTijCirculationCard: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { historyData, validity, number } = req.body

  const files = req.files as Express.Multer.File[] | { [fieldname: string]: Express.Multer.File[] };

  const frontImgFiles = ('frontImg' in files) ? files.frontImg[0] : undefined;
  const backImgFiles = ('backImg' in files) ? files.backImg[0] : undefined;

  if (!historyData || !number || !frontImgFiles || !backImgFiles) {
    return res.status(404).send({ message: stockVehiclesText.errors.missingBody });
  }
  const removeSpacesFrontImg = removeEmptySpacesNameFile(frontImgFiles);
  const removeSpacesBackImg = removeEmptySpacesNameFile(backImgFiles);

  const stockVehicle = await StockVehicle.findById(vehicleId);

  if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

  if (!historyData || !historyData.userId.toString() || !historyData.step || !historyData.description)
    return res.status(404)
      .send({ message: stockVehiclesText.errors.historyDataMissing });

  const historyUpdate: typeof stockVehicle.updateHistory[0] = {
    ...historyData,
    description: 'Tarjeta de circulación agregada',
    group: 'vehicle-info',
    userId: new Types.ObjectId(historyData.userId),
  }
  stockVehicle.updateHistory.push(historyUpdate);

  const carNumber = stockVehicle.carNumber;

  try {

    const circulationCard = stockVehicle.circulationCard;
    if (circulationCard) {

      const frontImgDoc = new Document({
        originalName: removeSpacesFrontImg,
        path: `stock/${carNumber}/circulationCard/tij/${(circulationCard.older?.length || 0) + 1}/${removeSpacesFrontImg}`,
        vehicleId,
      });
      await uploadFile(frontImgFiles, removeSpacesFrontImg, `stock/${carNumber}/circulationCard/tij/${(circulationCard.older?.length || 0) + 1}/`)
      await frontImgDoc.save();

      const backImgDoc = new Document({
        originalName: removeSpacesBackImg,
        path: `stock/${carNumber}/circulationCard/tij/${(circulationCard.older?.length || 0) + 1}/${removeSpacesBackImg}`,
        vehicleId,
      });
      await uploadFile(backImgFiles, removeSpacesBackImg, `stock/${carNumber}/circulationCard/tij/${(circulationCard.older?.length || 0) + 1}/`)
      await backImgDoc.save();

      // create older circulation card object
      const oldCirculationCard = {
        frontImg: circulationCard.frontImg!,
        backImg: circulationCard.backImg!,
        number: circulationCard.number!,
        validity: circulationCard.validity || '', // Handle case where validity might be undefined
      }

      if (circulationCard.older && circulationCard.older.length > 0) {
        circulationCard.older.unshift(oldCirculationCard);
      } else {
        circulationCard.older = [oldCirculationCard];
      }
      circulationCard.frontImg = frontImgDoc._id;
      circulationCard.backImg = backImgDoc._id;

      circulationCard.number = number;
      if (validity) {
        circulationCard.validity = validity;
      } else if (circulationCard.validity) {
        // If no new validity provided but there was an old one, remove it
        delete circulationCard.validity;
      }
    }

    await stockVehicle.save();
    return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error })
  }
}

export const getOlderCirculationCard: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;

  try {
    const stockVehicle = await StockVehicle.findById(vehicleId)
      .populate({
        path: 'circulationCard.older',
        populate: {
          path: 'frontImg backImg',
          model: 'Document',
        },
      }).lean();

    if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

    if (stockVehicle.vehicleState !== 'tij') return res.status(404).send({ message: 'No es un vehiculo de Tijuana' });

    const circulationCard = stockVehicle.circulationCard;
    if (!circulationCard || !circulationCard.older || circulationCard.older.length === 0)
      return res.status(404).send({ message: 'No hay tarjetas de circulación antiguas' });

    const circulationData = []

    if (circulationCard.older.length > 0) {
      for (let i = 0; i < circulationCard.older.length; i++) {
        const frontImg = await replaceDocWithUrl(circulationCard.older[i].frontImg?._id?.toString() || '');
        const backImg = await replaceDocWithUrl(circulationCard.older[i].backImg._id?.toString() || '');

        circulationData.push({
          ...circulationCard.older[i],
          frontImg,
          backImg,
        })
      }
    }

    return res.status(200).send({ message: 'Tarjetas de circulación antiguas', data: circulationData });
  } catch (error) {
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }


}

// GPS UPDATE

export const createAndUpdateGPS: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { isEditing, gpsNumber, gpsSerie, historyData } = req.body

  const stockVehicle = await StockVehicle.findById(vehicleId);

  if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

  const historyUpdate = {
    ...historyData,
    userId: new Types.ObjectId(historyData.userId),
  }
  stockVehicle.updateHistory.push(historyUpdate);

  try {
    if (!isEditing) {
      if (!gpsNumber || !gpsSerie) return res.status(404).send({
        message: stockVehiclesText.errors.missingBody,
      });

      const { userId } = req.userId;

      stockVehicle.gpsNumber = gpsNumber;
      stockVehicle.gpsSerie = gpsSerie;

      checkAndUpdateVehicleStatus(stockVehicle, userId)

      await stockVehicle.save();

      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated });
    } else {
      // Edición del gps
      if (gpsNumber) stockVehicle.gpsNumber = gpsNumber;
      if (gpsSerie) stockVehicle.gpsSerie = gpsSerie;

      await stockVehicle.save();
      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated });
    }

  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }

}

type BodyPolicy = {
  policyNumber: number;
  validity: string;
  insurer: string;
  broker: string;
  isEditing?: boolean;
  historyData: {
    userId: Types.ObjectId,
    step: string,
    description: string,
    time: string,
  }
}

// POLICY UPDATE

export const createAndUpdatePolicy: AsyncController = async (req, res) => {
  const { policyNumber, insurer, broker, validity, isEditing, historyData }: BodyPolicy = req.body;
  const { vehicleId } = req.params;
  const policyDocument: Express.Multer.File | undefined = req.file;

  const removeSpacesNamefile = removeEmptySpacesNameFile(policyDocument);

  if (!vehicleId) return res.status(404).send({ message: 'Se requiere ID del vehiculo' })

  const stockVehicle = await StockVehicle.findById(vehicleId);
  if (!stockVehicle) return res.status(404).send({ message: 'Vehiculo no encontrado' });

  if (!historyData || !historyData.userId || !historyData.step || !historyData.description)
    return res.status(404)
      .send({ message: stockVehiclesText.errors.historyDataMissing });

  const historyUpdate = {
    ...historyData,
    userId: new Types.ObjectId(historyData.userId),
  }
  stockVehicle.updateHistory.push(historyUpdate);

  const carNumber = stockVehicle.carNumber;

  try {
    if (!isEditing) {

      // El siguiente codigo es para crear nuevas tenencias
      if (!policyNumber || !validity || !policyDocument || !insurer || !broker)
        return res.status(404).send({ message: stockVehiclesText.errors.missingBody });

      const { userId } = req.userId;


      const policyDoc = new Document({
        originalName: removeSpacesNamefile,
        path: `stock/${carNumber}/` + removeSpacesNamefile,
      });
      await uploadFile(policyDocument, removeSpacesNamefile, `stock/${carNumber}/`)
      await policyDoc.save();

      const newPolicy = {
        _id: new Types.ObjectId(),
        policyNumber,
        validity,
        policyDocument: policyDoc._id,
        broker,
        insurer,
      };

      stockVehicle.policiesArray.push(newPolicy);

      checkAndUpdateVehicleStatus(stockVehicle, userId)

      await stockVehicle.save();

      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated });

    } else {
      // Código para editar una tenencia existente
      const { policyId, updatePolicyNumber, updateValidity, policyDocId, updateBroker }: {
        policyId: string;
        updatePolicyNumber?: number;
        updateValidity?: string;
        updateBroker?: string;
        policyDocId: string;
      } = req.body;

      if (!policyId)
        return res.status(400).send({ message: 'Se requiere el ID de póliza' });

      const tenancyIndex = stockVehicle.policiesArray.findIndex(policy => policy._id.toString() === policyId);
      if (tenancyIndex === -1)
        return res.status(404).send({ message: 'Póliza no encontrada' });

      if (updatePolicyNumber) stockVehicle.policiesArray[tenancyIndex].policyNumber = updatePolicyNumber;

      if (updateValidity) stockVehicle.policiesArray[tenancyIndex].validity = updateValidity;

      if (updateBroker) stockVehicle.policiesArray[tenancyIndex].broker = updateBroker;

      const policyDoc = await Document.findById(policyDocId);

      if (policyDoc && policyDocument) await uploadFile(policyDocument, removeSpacesNamefile,
        `stock/${carNumber}/`)

      if (policyDoc) {
        // Actualizar campos del documento
        stockVehicle.oldDocuments.push(policyDoc);
        policyDoc.originalName = removeSpacesNamefile || '';
        policyDoc.path = `stock/${carNumber}/` + removeSpacesNamefile;
        policyDoc.updatedAt = getCurrentDateTime();
        await policyDoc.save();
      }
      // stockVehicle.updateHistory.push(historyUpdate);
      stockVehicle.updatedAt = getCurrentDateTime();
      await stockVehicle.save();
      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })
    }

  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error })
  }
}

type BodyTenancy = {
  payment?: number;
  validity?: string;
  isEditing?: boolean;
  historyData: {
    userId: string,
    step: string,
    description: string,
    time: string,
  }
}

// TENANCY purchaseAgreement

export const createAndUpdateTenancy: AsyncController = async (req, res) => {
  const { payment, validity, isEditing, historyData }: BodyTenancy = req.body;
  const { vehicleId } = req.params;
  const tenancyDocument: Express.Multer.File | undefined = req.file;

  const removeSpacesNamefile = removeEmptySpacesNameFile(tenancyDocument);


  if (!vehicleId) return res.status(404).send({ message: 'Se requiere ID del vehiculo' })

  const stockVehicle = await StockVehicle.findById(vehicleId);
  if (!stockVehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });

  if (!historyData || !historyData.userId || !historyData.step || !historyData.description)
    return res.status(404)
      .send({ message: stockVehiclesText.errors.historyDataMissing });

  const historyUpdate = {
    ...historyData,
    userId: new Types.ObjectId(historyData.userId),
  }

  const carNumber = stockVehicle.carNumber;

  try {

    if (!isEditing) {

      // El siguiente codigo es para crear nuevas tenencias
      if (!payment || !validity || !tenancyDocument)
        return res.status(404).send({ message: stockVehiclesText.errors.missingBody });

      const { userId } = req.userId;

      const tenancyDoc = new Document({
        originalName: removeSpacesNamefile,
        path: `stock/${carNumber}/` + removeSpacesNamefile,
      });
      await uploadFile(tenancyDocument, removeSpacesNamefile, `stock/${carNumber}/`)
      await tenancyDoc.save();

      const newTenancy = {
        _id: new Types.ObjectId(),
        payment,
        validity,
        tenancyDocument: tenancyDoc._id,
      };

      stockVehicle.updateHistory.push(historyUpdate);
      stockVehicle.tenancy.push(newTenancy);

      checkAndUpdateVehicleStatus(stockVehicle, userId)

      await stockVehicle.save();

      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated });

    } else {
      // Código para editar una tenencia existente
      const { tenancyId, updatePayment, updateValidity, tenancyDocId }: {
        tenancyId: string;
        updatePayment?: number;
        updateValidity?: string;
        tenancyDocId: string;
      } = req.body;
      if (!tenancyId)
        return res.status(400).send({ message: 'Se rquiere el ID de tenencia' });

      const tenancyIndex = stockVehicle.tenancy.findIndex(tenancy => tenancy._id.toString() === tenancyId);

      if (tenancyIndex === -1)
        return res.status(404).send({ message: 'Tenencia no encontrada' });

      if (updatePayment) stockVehicle.tenancy[tenancyIndex].payment = updatePayment;

      if (updateValidity) stockVehicle.tenancy[tenancyIndex].validity = updateValidity;

      const tenancyDoc = await Document.findById(tenancyDocId);

      if (tenancyDoc && tenancyDocument) await uploadFile(tenancyDocument, removeSpacesNamefile,
        `stock/${carNumber}/`)

      if (tenancyDoc) {
        // Actualizar campos del documento
        stockVehicle.oldDocuments.push(tenancyDoc)
        tenancyDoc.originalName = removeSpacesNamefile || '';
        tenancyDoc.path = `stock/${carNumber}/` + removeSpacesNamefile;
        tenancyDoc.updatedAt = getCurrentDateTime();
        await tenancyDoc.save();
      }

      stockVehicle.updateHistory.push(historyUpdate);

      stockVehicle.updatedAt = getCurrentDateTime();
      await stockVehicle.save();
      return res.status(200).send({ message: stockVehiclesText.success.vehicleUpdated })
    }

  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error })
  }
}

// purchaseAgreement UPDATE

export const createAndUpdateSoldDocument: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { historyData, type } = req.body;
  const file = req.file as Express.Multer.File;

  // Validar campos requeridos
  if (!vehicleId) return res.status(400).send({ message: 'ID de vehículo requerido' });
  if (!type) return res.status(400).send({ message: 'Tipo de documento requerido' });
  if (!file) return res.status(400).send({ message: 'Archivo requerido' });

  // Definir tipos válidos
  enum DocumentType {
    purchaseAgreement = 'purchaseAgreement',
    soldInvoiceXml = 'soldInvoiceXml',
    soldInvoicePdf = 'soldInvoicePdf',
    platesCancelation = 'platesCancelation',
  }

  try {
    // Validar tipo de documento
    if (!Object.values(DocumentType).includes(type as DocumentType)) {
      return res.status(400).send({ message: 'Tipo de documento no válido' });
    }

    const documentType = type as DocumentType;

    // Buscar vehículo
    const stockVehicle = await StockVehicle.findById(vehicleId);
    if (!stockVehicle) {
      return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
    }

    // Validar datos de historial
    if (!historyData?.userId || !historyData?.step || !historyData?.description) {
      return res.status(400).send({ message: stockVehiclesText.errors.historyDataMissing });
    }

    // Procesar archivo
    const cleanFileName = removeEmptySpacesNameFile(file);
    const carNumber = stockVehicle.carNumber;
    const filePath = `stock/${carNumber}/${cleanFileName}`;

    // Obtener documento actual si existe
    const currentDocId = stockVehicle[documentType];
    let document = currentDocId ? await Document.findById(currentDocId) : null;

    // Manejar documento existente
    if (document) {
      // Mover a historial
      stockVehicle.oldDocuments.push(document._id);
      // Actualizar documento existente
      document.originalName = cleanFileName;
      document.path = filePath;
    } else {
      // Crear nuevo documento
      document = new Document({
        originalName: cleanFileName,
        path: filePath,
      });
    }

    // Subir archivo y guardar documento
    await uploadFile(file, cleanFileName, `stock/${carNumber}/`);
    await document.save();

    // Actualizar referencia en el vehículo
    stockVehicle[documentType] = document._id;

    // Registrar en historial
    stockVehicle.updateHistory.push({
      userId: new Types.ObjectId(historyData.userId),
      step: historyData.step,
      description: historyData.description,
    });

    // Guardar cambios
    await stockVehicle.save();

    return res.status(200).send({
      message: stockVehiclesText.success.vehicleUpdated,
      documentId: document._id,
      documentType: type,
    });

  } catch (error) {
    console.error('Error en documento de venta:', error);
    return res.status(500).send({
      message: stockVehiclesText.errors.error,
      error: error instanceof Error ? error.message : 'Error desconocido',
    });
  }
};

export const validateIfStockVehicleExistsByProps: AsyncController = async (req, res) => {
  const { plates, vin, vehicleId } = req.body;

  const query: { [key: string]: any } = {};
  try {
    if (!plates && !vin) return res.status(400).send({ message: 'Se requiere al menos un parametro' });
    if (plates) {
      query['carPlates.plates'] = plates.trim();
    }
    if (vin) query.vin = vin.trim();

    const stockVehicle = await StockVehicle.findOne(query);

    if (stockVehicle && stockVehicle._id.toString() !== vehicleId)
      return res.status(404)
        .send({ message: 'El vehiculo con la información proporcionada ya existe', carNumber: stockVehicle.carNumber });

    if (!stockVehicle) return res.status(200).send({ message: 'Vehiculo no encontrado' });
    return res.status(200).send({ message: 'Es el mismo vehiculo y se puede editar', stockVehicle });
  } catch (error) {
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
}

export const gpsInstalled: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;

  try {
    const stockVehicle = await StockVehicle.findById(vehicleId);

    if (!stockVehicle) return res.status(404).send({ message: 'Vehiculo no encontrado' });

    stockVehicle.gpsInstalled = true;

    stockVehicle.updateHistory.push({
      userId: req.userId.userId,
      step: 'GPS instalado',
      description: '',
      time: getCurrentDateTime(),
      group: 'vehicle-info',
    });

    await stockVehicle.save();
    return res.status(200).send({ message: 'GPS instalado', stockVehicle });
  } catch (error) {
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
}

export const changeVehicleState: AsyncController = async (req, res) => {
  const { region, id } = req.body;
  if (!region) return res.status(400).send({ message: 'Se requiere el estado del vehiculo' });
  if (!id) return res.status(404).send({ message: 'Se requiere el ID del vehiculo' });
  try {
    const stockVehicle = await StockVehicle.findById(id);
    const stockVehicleId = stockVehicle?._id;
    const regionByRegionNumber = citiesByRegion[region];
    const contractNumber = (await getLastContractNumber(regionByRegionNumber)) || 1;
    // const regionByRegionNumberString = regionByRegionNumber.toString();
    // const alias = regionByRegionNumberString + contractNumber.toString().padStart(4 - contractNumber.toString().length, '0');
    const ceros = regionByRegionNumber.toString().length === 1 ? '0' : '0'.repeat(3 - contractNumber.toString().length);
    const alias = regionByRegionNumber + ceros + contractNumber;

    if (!stockVehicle) return res.status(404).send({ message: 'Vehiculo no encontrado' });
    await StockVehicle.updateOne({ _id: id },
      {
        $set:
          { vehicleState: region, carNumber: alias, gpsNumber: alias, gpsSerie: alias },
      });


    const contract = new Contract({
      region: regionByRegionNumber,
      contractNumber,
      alias: Number(alias),
      stockVehicleId,
    });
    await contract.save();

    stockVehicle.updateHistory.push({
      userId: req.userId.userId,
      step: 'Vehiculo con cambio de región',
      description: '',
      time: getCurrentDateTime(),
    });

    await stockVehicle.save();

    return res.status(200).send({ message: 'Estado del vehiculo cambiado', alias,region });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
}

export const addRandomCarData: AsyncController = async (req, res) => {
  const { carNumber } = req.body as { carNumber: string };

  if (!carNumber) return res.status(400).send({ message: 'Se requiere el numero de placa' });

  const stockVehicle = await StockVehicle.findOne({ carNumber });

  if (!stockVehicle) return res.status(404).send({ message: 'Vehiculo no encontrado' });

  const newDocumetst = {
    carPlates: {
      plates: carNumber,
      backImg: new Types.ObjectId("65c551702797c632d387302c"),
      frontImg: new Types.ObjectId("65c551702797c632d387302c"),
      platesDocument: new Types.ObjectId("65c551702797c632d387302c"),
    },
    circulationCard: {
      number: carNumber,
      validity: '2021-12-31',
      frontImg: new Types.ObjectId("65c551702797c632d387302c"),
      backImg: new Types.ObjectId("65c551702797c632d387302c"),
    },
    gpsNumber: carNumber,
    gpsSerie: carNumber,
    tenancy: [
      {
      _id: new Types.ObjectId(),
      payment: 1000,
      validity: '2021-12-31',
        tenancyDocument: new Types.ObjectId("65c551702797c632d387302c"),
      },

    ],
    policiesArray: [
      {
        _id: new Types.ObjectId(),
        policyNumber: 123456,
        validity: '2021-12-31',
        policyDocument: new Types.ObjectId("65c551702797c632d387302c"),
        insurer: 'Seguros Atlas',
        broker: 'Broker',
      },
    ],
  }

  stockVehicle.carPlates = newDocumetst.carPlates;
  stockVehicle.circulationCard = newDocumetst.circulationCard;
  stockVehicle.gpsNumber = newDocumetst.gpsNumber;
  stockVehicle.gpsSerie = newDocumetst.gpsSerie;
  stockVehicle.tenancy = newDocumetst.tenancy;
  stockVehicle.policiesArray = newDocumetst.policiesArray;
  stockVehicle.updateHistory.push({
    userId: req.userId.userId,
    step: 'Datos del vehículo aleatorios agregados',
    description: '',
    time: getCurrentDateTime(),
  });

  await stockVehicle.save();

  return res.status(200).send({ message: 'Datos agregados', stockVehicle });

}

export const checkIftheLastAllPaymentDateIsTreeDaysBefore: AsyncController = async (req, res) => {

  const today = new Date();
  const threeDaysAgo = new Date();
  threeDaysAgo.setDate(today.getDate() - 3);

  const todayStr = today.toISOString().split('T')[0]; // Formato YYYY-MM-DD
  const threeDaysAgoStr = threeDaysAgo.toISOString().split('T')[0]; // Formato YYYY-MM-DD

  const stock = await MainContractSchema.aggregate([
    {
      $project: {
        contractNumber: 1,
        finalPrice:1,
        allPayments: {
          $filter: {
            input: "$allPayments",
            as: "payment",
            cond: {
              $and: [
                {
                  $gte: [
                    {
                      $dateToString: {
                        format: "%Y-%m-%d",
                        date: {
                          $dateFromString: {
                            dateString: "$$payment.day",
                            format: "%d-%m-%Y", // Formato original: DD-MM-YYYY
                          },
                        },
                      },
                    },
                    threeDaysAgoStr, // Fecha de 3 días atrás en YYYY-MM-DD
                  ],
                },
                {
                  $lte: [
                    {
                      $dateToString: {
                        format: "%Y-%m-%d",
                        date: {
                          $dateFromString: {
                            dateString: "$$payment.day",
                            format: "%d-%m-%Y", // Formato original: DD-MM-YYYY
                          },
                        },
                      },
                    },
                    todayStr, // Fecha de hoy en YYYY-MM-DD
                  ],
                },
                {
                  $eq: ["$$payment.number", "156"], // Nueva condición: number debe ser igual a 155
                },
              ],
            },
          },
        },
      },
    },
    {
      $match: {
        "allPayments.0": { $exists: true }, // Filtra documentos con al menos un pago en el rango
      },
    },
  ]);


  return res.status(200).send({ message: 'Contratos con pagos en los últimos 3 días', stock });
}

export const deleteVehicleDocs: AsyncController = async (req, res) => {
  const { vehicleId, documentToDelete } = req.body;

  if (!vehicleId) {
    logger.warn('[deleteVehicleDocs] Missing vehicleId in request');
    return res.status(404).send({ message: 'Se requiere el ID del vehiculo' });
  }
  if (!documentToDelete) {
    logger.warn('[deleteVehicleDocs] Missing documentToDelete in request');
    return res.status(404).send({ message: 'Se requiere los documentos a eliminar' });
  }

  try {
    const stockVehicle = await StockVehicle.findById(vehicleId);
    if (!stockVehicle) {
      logger.warn(`[deleteVehicleDocs] Vehicle not found: ${vehicleId}`);
      return res.status(404).send({ message: 'Vehiculo no encontrado' });
    }

    logger.info(`[deleteVehicleDocs] Deleting document: ${documentToDelete} for vehicleId: ${vehicleId}`);

    if (documentToDelete === 'policiesArray') {
      await deleteMostRecentPolicy(stockVehicle);
    }

    if (documentToDelete === 'tenancy') {
      await deleteMostRecentTenancy(stockVehicle);
    }

    if (documentToDelete === 'carPlates') {
      if (stockVehicle.carPlates) {
        if (stockVehicle.carPlates.frontImg) await deleteDocument(stockVehicle.carPlates.frontImg);
        if (stockVehicle.carPlates.backImg) await deleteDocument(stockVehicle.carPlates.backImg);
        if (stockVehicle.carPlates.platesDocument) await deleteDocument(
          stockVehicle.carPlates.platesDocument
        );
        stockVehicle.carPlates = undefined;
        stockVehicle.markModified('carPlates');
        stockVehicle.vehicleDocsComplete = false;
      }
    }

    if (documentToDelete === 'circulationCard') {
      if (stockVehicle.circulationCard) {
        if (stockVehicle.circulationCard.frontImg) await deleteDocument(
          stockVehicle.circulationCard.frontImg
        );
        if (stockVehicle.circulationCard.backImg) await deleteDocument(stockVehicle.circulationCard.backImg);
        stockVehicle.circulationCard = undefined;
        stockVehicle.markModified('circulationCard');
        stockVehicle.vehicleDocsComplete = false;
      }
    }

    const docHistoryName = documentToDelete === 'policiesArray' ? 'Polizas' :
                           documentToDelete === 'tenancy' ? 'tenencia' :
                           documentToDelete === 'carPlates' ? 'Placas' :
                           documentToDelete === 'circulationCard' ? 'Tarjeta de circulación' : '';

    const { userId } = req.userId;

    stockVehicle.updateHistory.push(
      {
        step: 'DOCUMENTACIÓN',
        description: `${docHistoryName} eliminada`,
        userId: new Types.ObjectId(userId),
        time: getCurrentDateTime(),
      }
    );

    if (
      stockVehicle.step?.stepName === steps.vehicleReady.name &&
      stockVehicle.category === VehicleCategory.stock &&
      stockVehicle.vehicleDocsComplete === false
    ) {
      logger.info(`[deleteVehicleDocs] Moving vehicleId: ${vehicleId} to in-preparation due to document deletion.`);
      stockVehicle.updateHistory.push(
        {
          userId: new Types.ObjectId(userId),
          step: 'Vehículo enviado para preparación.',
          description: '',
          time: getCurrentDateTime(),
        }
      );
      stockVehicle.step.stepName = steps.stock.name;
      stockVehicle.step.stepNumber = steps.stock.number;
      stockVehicle.vehicleStatus = UpdatedVehicleStatus.inactive;
      stockVehicle.category = VehicleCategory['in-preparation'];
      stockVehicle.subCategory = VehicleSubCategory.default;
    }

    await stockVehicle.save();

    logger.info(`[deleteVehicleDocs] Document ${docHistoryName} deleted successfully for vehicleId: ${vehicleId}`);

    return res.status(200).send({ message: 'Documentos eliminados correctamente' });
  } catch (error: any) {
    logger.error(`[deleteVehicleDocs] Error deleting document: ${error.message}`);
    return res.status(500).send({ message: error.message || 'Error deleting documents' , error: error });
  }
}

export const bulkUploadXML: AsyncController = async (req, res) => {
  const { country, region, userName, userEmail } = req.body;
    try {
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      logger.warn('[bulkUploadXML] No files were uploaded for processing');
      return res.status(400).send({ message: 'No files uploaded' });
    }

    logger.info('[bulkUploadXML] Starting bulk XML upload process');
    const { userId } = req.userId;

    const xmlFiles: Express.Multer.File[] = [];
    const nonXmlFiles: Express.Multer.File[] = [];

    files.forEach(file => {
      const fileExtension = file.originalname?.split('.').pop()?.toLowerCase();
      if (fileExtension === 'xml') {
        xmlFiles.push(file);
      } else {
        nonXmlFiles.push(file);
      }
    });

    const initialErrorDetails = nonXmlFiles.map(file => ({
      fileName: file.originalname,
      errorType: 'INVALID_FILE_TYPE',
      userMessage: 'El archivo no es un documento XML válido',
      affectedField: 'Tipo de archivo',
    }));

    logger.info('[bulkUploadXML] Starting async processing of XML files', {
      totalFiles: files.length,
      xmlFiles: xmlFiles.length,
      nonXmlFiles: nonXmlFiles.length,
      country,
      region,
      userId,
    });

    const uploader = await User.findOne({ _id: userId });
    if (!uploader) {
      logger.warn('[bulkUploadXML] User not found');
    }

    try {
      processBulkXMLFiles({
        files: xmlFiles,
        country,
        region,
        userId,
        userName,
        initialErrorDetails,
        userEmail,
      })
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      logger.error('[processBulkXMLFiles] Critical error during XML processing:', error);

      const notificationMessage = slackTexts.criticalError(errorMessage);
      const notificationSuccess = await slackChannelNotifier({
        message: createSlackErrorNotification({
          headerText: slackTexts.processFailedTitle,
          userName,
          errorMessage: notificationMessage,
          bottomText: slackTexts.fileProcessErrorBottomText,
        }),
        BotToken: SLACK_NOTIFIER_BOT_TOKEN,
        ChannelId: SLACK_BULK_UPLOAD_CHANNEL_ID,
      });

      if (notificationSuccess) {
        logger.info('[processBulkXMLFiles] Successfully sent summary notification');
      } else {
        await sendSlackFallbackEmail({
          userName: userName,
          message: notificationMessage,
          isError: true,
          fileContent: undefined,
          fileName: undefined,
        });
      }
    }

    return res.status(202).send({
      message: 'Files received and processing started',
      totalFiles: files.length,
      xmlFiles: xmlFiles.length,
      nonXmlFiles: nonXmlFiles.length,
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    logger.error('[bulkUploadXML] Critical error in bulk upload endpoint:', error);
    
    const notificationMessage = slackTexts.uploadError(errorMessage);
    const notificationSuccess = await slackChannelNotifier({
      message: createSlackErrorNotification({
        headerText: slackTexts.bulkUploadFailedTitle,
        userName,
        errorMessage: notificationMessage,
        bottomText: slackTexts.bulkUploadErrorBottomText,
      }),
      BotToken: SLACK_NOTIFIER_BOT_TOKEN,
      ChannelId: SLACK_BULK_UPLOAD_CHANNEL_ID,
    });

    if (notificationSuccess) {
      logger.info('[bulkUploadXML] Successfully sent summary notification');
    } else {
      await sendSlackFallbackEmail({
        userName: userName,
        message: notificationMessage,
        isError: true,
        fileContent: undefined,
        fileName: undefined,
      });
    }
    return res.status(500).send({
      message: 'Se produjo un error al procesar los archivos xml',
      error: errorMessage,
    });
  }
};

/**
 * Gets redirect information for a vehicle by ID.
 * This is used by the QR code redirection system to determine
 * the correct dynamic URL based on vehicle's current state.
 */
export const getVehicleRedirectInfo: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { handover } = req.query;

  if (!Types.ObjectId.isValid(vehicleId)) {
    logger.warn(`[getVehicleRedirectInfo] Invalid vehicle ID format received: ${vehicleId}`);
    return res.status(400).send({ message: 'Invalid vehicle ID format.' });
  }

  try {
    const vehicle = await StockVehicle.findById(vehicleId)
      .select('_id carNumber vehicleStatus category country physicalStatus')
      .lean();
    if (!vehicle) {
      logger.warn(`[getVehicleRedirectInfo] Vehicle not found: ${vehicleId}`);
      return res.status(404).send({ message: 'Vehicle not found.' });
    }

    if (!vehicle.vehicleStatus || !vehicle.category || !vehicle._id) {
      logger.error(`[getVehicleRedirectInfo] Missing required vehicle data for URL construction for vehicle ${vehicleId}. Status: ${vehicle.vehicleStatus}, Category: ${vehicle.category}`);
      return res.status(500).send({ message: 'Internal server error: Could not retrieve necessary vehicle data for redirect.' });
    }

    // Generate a short-lived QR scan token
    const tokenValue = crypto.randomBytes(16).toString('hex');
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // Token expires in 10 minutes

    const qrScanToken = new QRActionToken({
      token: tokenValue,
      vehicleId: vehicle._id,
      type: 'qr_scan',
      expiresAt,
      used: false,
    });
    await qrScanToken.save();

    // NEW: If physicalStatus is REPAIR_COMPLETE_BY_VENDOR and no handover is specified, require frontend to ask
    if (
      vehicle.physicalStatus === PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR &&
      !handover
    ) {
      return res.status(200).json({
        handoverRequired: true,
        physicalStatus: vehicle.physicalStatus,
        vehicleCountry: vehicle?.country,
        message: 'Handover type required to determine redirect URL.',
      });
    }

    let frontendVehicleDetailUrl;

    if (
      vehicle.physicalStatus === PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR &&
      handover
    ) {
      if (handover === HandoverType.CUSTOMER) {
        const locale = 'en';
        frontendVehicleDetailUrl = `${VENDOR_PANEL_URL}/${locale}/dashboard/vehicles/${vehicle._id}?qr_scan_token=${tokenValue}`;
        logger.info(`[getVehicleRedirectInfo] (handover=customer) Vendor panel redirect URL: ${frontendVehicleDetailUrl}`);
      } else if (handover === HandoverType.AGENT) {
        const frontendBaseUrl = FRONTEND_ADMIN_URL;
        if (!frontendBaseUrl) {
          logger.error('[getVehicleRedirectInfo] Critical: FRONTEND_URL environment variable is not set.');
          return res.status(500).send({ message: 'Server configuration error: Frontend URL not configured.' });
        }
        const vehicleStatusPath = encodeURIComponent(vehicle.vehicleStatus);
        const categoryPath = encodeURIComponent(vehicle.category);
        const countryQuery = vehicle.country ? `?country=${encodeURIComponent(vehicle.country)}` : '';
        const tokenQuery = `${countryQuery ? '&' : '?'}qr_scan_token=${tokenValue}`;
        frontendVehicleDetailUrl = `${frontendBaseUrl}/dashboard/flotilla/${vehicleStatusPath}/${categoryPath}/${vehicle._id}${countryQuery}${tokenQuery}`;
        logger.info(`[getVehicleRedirectInfo] (handover=agent) Admin panel redirect URL: ${frontendVehicleDetailUrl}`);
      } else {
        return res.status(400).json({ message: 'Invalid handover type. Must be "customer" or "agent".' });
      }
    } else {
      const isVendorRepairStatus = [
        PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP,
        PhysicalVehicleStatus.RECEIVED_BY_VENDOR_WORKSHOP,
        PhysicalVehicleStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP,
      ].includes(vehicle.physicalStatus as PhysicalVehicleStatus);

      if (isVendorRepairStatus) {
        const locale = 'en';
        frontendVehicleDetailUrl = `${VENDOR_PANEL_URL}/${locale}/dashboard/vehicles/${vehicle._id}?qr_scan_token=${tokenValue}`;
        logger.info(`[getVehicleRedirectInfo] Generated vendor panel redirect URL: ${frontendVehicleDetailUrl}`);
      } else {
        const frontendBaseUrl = FRONTEND_ADMIN_URL;
        if (!frontendBaseUrl) {
          logger.error('[getVehicleRedirectInfo] Critical: FRONTEND_URL environment variable is not set.');
          return res.status(500).send({ message: 'Server configuration error: Frontend URL not configured.' });
        }
        const vehicleStatusPath = encodeURIComponent(vehicle.vehicleStatus);
        const categoryPath = encodeURIComponent(vehicle.category);
        const countryQuery = vehicle.country ? `?country=${encodeURIComponent(vehicle.country)}` : '';
        const tokenQuery = `${countryQuery ? '&' : '?'}qr_scan_token=${tokenValue}`;
        frontendVehicleDetailUrl = `${frontendBaseUrl}/dashboard/flotilla/${vehicleStatusPath}/${categoryPath}/${vehicle._id}${countryQuery}${tokenQuery}`;
        logger.info(`[getVehicleRedirectInfo] Generated admin panel redirect URL: ${frontendVehicleDetailUrl}`);
      }
    }

    return res.status(200).json({
      redirectUrl: frontendVehicleDetailUrl,
      physicalStatus: vehicle?.physicalStatus,
      vehicleCountry: vehicle?.country,
    });

  } catch (error) {
    logger.error(`[getVehicleRedirectInfo] Error generating redirect info for vehicle ${vehicleId}: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).send({ message: 'Error generating vehicle redirect info.' });
  }
};

export const initiateQrScanAction: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { qrScanToken, source, vendorUserName } = req.body;
  const deviceInfo = req.headers['user-agent'] || 'Unknown device';

  if (!Types.ObjectId.isValid(vehicleId)) {
    return res.status(400).json({ message: 'Invalid vehicle ID format.' });
  }
  if (!qrScanToken || typeof qrScanToken !== 'string') {
    return res.status(400).json({ message: 'QR scan token is required.' });
  }

  try {
    const tokenDoc = await QRActionToken.findOne({
      token: qrScanToken,
      vehicleId: new Types.ObjectId(vehicleId),
      type: QRActionTokenType.QR_SCAN,
      used: false,
      expiresAt: { $gte: new Date() },
    });

    if (!tokenDoc) {
      return res.status(400).json({ actionAvailable: false, message: 'Invalid or expired QR scan session.' });
    }

    tokenDoc.used = true;

    const vehicle = await StockVehicle.findById(vehicleId).select('physicalStatus carNumber brand model'); // Fetch brand/model
    if (!vehicle) {
      return res.status(404).json({ actionAvailable: false, message: 'Vehicle not found.' });
    }

    await createQRScanHistoryEntry({
      vehicleId: vehicle._id,
      userId: req.userId?.userId || req.userVendor.userId,
      statusChangedFrom: vehicle.physicalStatus as PhysicalVehicleStatus,
      statusChangedTo: vehicle.physicalStatus as PhysicalVehicleStatus, // No change yet, just the scan
      deviceInfo,
      actionToken: qrScanToken,
      actionType: QRScanHistoryActionType.SCAN,
      notes: `QR code scanned for vehicle ${vehicle.carNumber}`,
      vendorUserName,
    });

    const {
      nextPhysicalStatusToDisplay,
      nextStepOptions,
      message,
      intendedNextStatusForToken,
      dealershipName,
    } = getQrScanStepResult(vehicle.physicalStatus as PhysicalVehicleStatus, vehicle.brand, source);

    const actionAvailable = nextPhysicalStatusToDisplay !== null || nextStepOptions.length > 0;

    if (actionAvailable) {
      const confirmationTokenValue = await createQrActionToken({
        vehicleId: vehicle._id,
        type: QRActionTokenType.CONFIRMATION,
        intendedNextStatus: intendedNextStatusForToken,
        expiresInMinutes: 5,
      });
      await tokenDoc.save();

      return res.status(200).json({
        actionAvailable: true,
        currentPhysicalStatus: vehicle.physicalStatus,
        currentStatusDealershipName: vehicle.physicalStatus === PhysicalVehicleStatus.AWAITING_RECEIPT
          ? dealershipName
          : undefined,
        nextPhysicalStatusToDisplay,
        nextStepOptions: nextStepOptions.length > 0 ? nextStepOptions : undefined,
        confirmationToken: confirmationTokenValue,
        message,
      });
    } else {
      await tokenDoc.save(); // Save the used qr_scan_token even if no action
      return res.status(200).json({
        actionAvailable: false,
        currentPhysicalStatus: vehicle.physicalStatus,
        message,
      });
    }
  } catch (error) {
    logger.error(`[initiateQrScanAction] Error: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).json({ actionAvailable: false, message: 'Error initiating QR scan action.' });
  }
};

export const confirmQrStatusChange: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { confirmedNextStatus, confirmationToken, photoPath, vendorRegion, vendorWorkshopName } = req.body;
  const deviceInfo = req.headers['user-agent'] || 'Unknown device';

  if (!Types.ObjectId.isValid(vehicleId)) {
    return res.status(400).json({ message: 'Invalid vehicle ID format.' });
  }
  if (!confirmationToken || typeof confirmationToken !== 'string') {
    return res.status(400).json({ message: 'Confirmation token is required.' });
  }
  if (!confirmedNextStatus || !Object.values(PhysicalVehicleStatus)
    .includes(confirmedNextStatus as PhysicalVehicleStatus)) {
    return res.status(400).json({ message: 'Valid confirmed next status is required.' });
  }

  // @TODO: uncomment this once we have a photo path for admin
  /*
  if (!photoPath) {
    // Ensure explicit return
    return res.status(400).json({ message: 'Photo verification is required.' });
  }
  */

  try {
    const tokenDoc = await QRActionToken.findOne({
      token: confirmationToken,
      vehicleId: new Types.ObjectId(vehicleId),
      type: QRActionTokenType.CONFIRMATION,
      used: false,
      expiresAt: { $gte: new Date() },
    });

    if (!tokenDoc) {
      return res.status(400).json({ success: false, message: 'Invalid or expired confirmation session.' });
    }

    tokenDoc.used = true;
    await tokenDoc.save();

    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({ success: false, message: 'Vehicle not found.' });
    }

    const oldStatus = vehicle.physicalStatus;
    vehicle.physicalStatus = confirmedNextStatus as PhysicalVehicleStatus;

    const userId = req.userId?.userId || req.userVendor?.userId;
    if (!userId) {
      logger.warn(`[confirmQrStatusChange] User ID not found on request for vehicle ${vehicleId}. History log will be incomplete.`);
    }

    const photoDoc = await Document.findOne({ path: photoPath });
    if (!photoDoc) {
      logger.warn(`[confirmQrStatusChange] Photo document not found: ${photoPath}`);
    }

    const historyEntry: any = {
      vehicleId: vehicle._id,
      userId: userId ? new Types.ObjectId(userId) : new Types.ObjectId(), // Ensure userId is an ObjectId
      statusChangedFrom: oldStatus as PhysicalVehicleStatus,
      statusChangedTo: vehicle.physicalStatus as PhysicalVehicleStatus,
      deviceInfo,
      actionToken: confirmationToken,
      actionType: QRScanHistoryActionType.STATUS_CHANGE,
      notes: `Physical status changed via QR confirmation`,
      photoDocId: photoDoc?._id, 
      vendorUserName: req.body.vendorUserName,
    };

    // Conditionally add vendor workshop details
    const repairStatuses = [
      PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP, 
      PhysicalVehicleStatus.RECEIVED_BY_VENDOR_WORKSHOP,
      PhysicalVehicleStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP,
      PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR,
      ];  
    if (repairStatuses.includes(vehicle.physicalStatus as PhysicalVehicleStatus)) {
      if (vendorRegion) historyEntry.vendorRegion = vendorRegion;
      if (vendorWorkshopName) historyEntry.vendorWorkshopName = vendorWorkshopName;
      historyEntry.notes = `Vehicle in transit to workshop: ${vendorWorkshopName || 'N/A'} in region: ${vendorRegion || 'N/A'}. Status changed via QR.`;
    }

    await createQRScanHistoryEntry(historyEntry);

    await vehicle.save();
    return res.status(200).json({
      success: true,
      newPhysicalStatus: vehicle.physicalStatus,
      message: `Vehicle ${vehicle.carNumber} physical status updated to ${vehicle.physicalStatus}.`,
    });

  } catch (error) {
    logger.error(`[confirmQrStatusChange] Error: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).json({ success: false, message: 'Error confirming physical status change.' });
  }
};

export const uploadQrScanPhoto: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const file: Express.Multer.File | undefined = req.file;

  if (!Types.ObjectId.isValid(vehicleId)) {
    return res.status(400).json({ success: false, message: 'Invalid vehicle ID format.' });
  }

  if (!file) {
    return res.status(400).json({ success: false, message: 'No file uploaded.' });
  }

  try {
    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({ success: false, message: 'Vehicle not found.' });
    }

    const cleanFileName = removeEmptySpacesNameFile(file);
    
    const filePath = `stock/${vehicleId}/qr_scan_photos/${cleanFileName}`;
    
    await uploadFile(file, cleanFileName, `stock/${vehicleId}/qr_scan_photos/`);
    
    const photoDoc = new Document({
      originalName: cleanFileName,
      path: filePath,
      vehicleId: new Types.ObjectId(vehicleId),
    });
    
    await photoDoc.save();
    
    logger.info(`[uploadQrScanPhoto] Successfully uploaded photo for vehicle ${vehicleId}`);
    
    return res.status(200).json({
      success: true,
      message: 'Photo uploaded successfully',
      filePath,
      documentId: photoDoc._id,
    });
  } catch (error) {
    logger.error(`[uploadQrScanPhoto] Error: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).json({ success: false, message: 'Error uploading photo.' });
  }
};

/**
 * Get QR scan history for a specific vehicle
 */
export const getVehicleQRScanHistory: AsyncController = async (req, res) => {
  logger.info(`[getVehicleQRScanHistory] Request received for vehicle ${req.params?.vehicleId}`);
  const { vehicleId } = req.params;

  if (!vehicleId) {
    return res.status(400).json({ message: 'Vehicle ID is required' });
  }

  try {
    
    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({ message: 'Vehicle not found' });
    }

    const qrScanHistory = await getQRScanHistoryForVehicle(new Types.ObjectId(vehicleId));

    return res.status(200).json({
      message: 'QR scan history retrieved successfully',
      data: qrScanHistory,
    });
  } catch (error) {
    logger.error(`[getVehicleQRScanHistory] Error retrieving QR scan history: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).json({ 
      message: 'Error retrieving QR scan history', 
      error: error instanceof Error ? error.message : 'Unknown error', 
    })
  }
}
export const getVehiclesWithQRCodes: AsyncController = async (req, res) => {
  try {
    const vehicles = await StockVehicle.find({ qrCode: { $exists: true, $ne: null } })
      .select('carNumber model brand year color vin vehicleState qrCode status vehicleStatus category subCategory')
      .lean();

    if (!vehicles || vehicles.length === 0) {
      return res.status(404).json({
        message: 'No vehicles found with QR codes',
        data: [],
      });
    }

    // Convert qrCode document IDs to complete URLs for each vehicle
    const vehiclesWithQRUrls = await Promise.all(
      vehicles.map(async (vehicle) => {
        const qrCode = await replaceDocWithUrl(vehicle?.qrCode?.toString() || '');
        return {
          ...vehicle,
          qrCode,
        };
      })
    );

    return res.status(200).json({
      message: 'Vehicles with QR codes retrieved successfully',
      data: vehiclesWithQRUrls,
    });

  } catch (error) {
    logger.error('[getVehiclesWithQRCodes] Error:', error);
    return res.status(500).json({
      message: 'Error retrieving vehicles with QR codes',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

export const adminUpdatePhysicalStatus: AsyncController = async (req, res) => {
  const { vehicleId } = req.params;
  const { newPhysicalStatus, notes, isAdminCorrection } = req.body;

  try {
    const { userId } = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        message: 'User authentication required.', 
      });
    }

    const currentUser = await User.findById(userId);
    if (!currentUser) {
      return res.status(401).json({ 
        success: false, 
        message: 'User not found.', 
      });
    }

    if (currentUser.role !== 'superadmin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied. Superadmin role required.', 
      });
    }

    const vehicle = await StockVehicle.findById(vehicleId);
    if (!vehicle) {
      return res.status(404).json({ 
        success: false, 
        message: 'Vehicle not found.', 
      });
    }

    const oldPhysicalStatus = vehicle.physicalStatus;

    vehicle.physicalStatus = newPhysicalStatus as PhysicalVehicleStatus;

    await createQRScanHistoryEntry({
      vehicleId: vehicle._id,
      userId: new Types.ObjectId(userId),
      statusChangedFrom: oldPhysicalStatus as PhysicalVehicleStatus,
      statusChangedTo: newPhysicalStatus as PhysicalVehicleStatus,
      actionType: QRScanHistoryActionType.STATUS_CHANGE,
      notes: notes || `Admin correction: Status changed from ${oldPhysicalStatus} to ${newPhysicalStatus}`,
      isAdminCorrection: isAdminCorrection ?? true,
      vendorUserName: req.body.vendorUserName,
    });

    await vehicle.save();

    logger.info(`[adminUpdatePhysicalStatus] Admin ${currentUser.name} updated vehicle ${vehicle.carNumber} status from ${oldPhysicalStatus} to ${newPhysicalStatus}`);

    return res.status(200).json({
      success: true,
      newPhysicalStatus: vehicle.physicalStatus,
      message: 'Physical status updated successfully',
    });

  } catch (error) {
    logger.error(`[adminUpdatePhysicalStatus] Error updating physical status for vehicle ${vehicleId}: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error updating physical status.', 
    });
  }
};

export const getPreviousDriverPrice: AsyncController = async (req, res) => {
  const { stockId } = req.params;

  try {
    const stockVehicle = await StockVehicle.findById(stockId).select('drivers');
    if (!stockVehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found.',
      });
    }

    const lastDriver = stockVehicle.drivers[stockVehicle.drivers.length - 2];

    if (!lastDriver) {
      return res.status(404).json({
        success: false,
        message: 'No previous associate found.',
      });
    }
    const previousStartPayment = await StartPayFlow.findOne({
      associateId: lastDriver._id,
      stockId: stockVehicle._id,
    })
      .sort({ createdAt: -1 })
      .select('rentingProduct assistanceProduct ');

    let rentingProduct = previousStartPayment?.rentingProduct;
    let assistanceProduct = previousStartPayment?.assistanceProduct;

    if (!previousStartPayment) {

      const associate = await Associate.findById(lastDriver._id).select('clientId');
      if (!associate) {
        return res.status(404).json({
          success: false,
          message: 'Associate not found.',
        });
      }

      const clientId = associate.clientId;
      if (!clientId) {
        return res.status(404).json({
          success: false,
          message: 'Client not found.',
        });
      }

      const { data } = await axios.get(`${PAYMENTS_API_URL}/subscriptions?clientId=${clientId}`, {
        headers: {
          Authorization: `Bearer ${PAYMENTS_API_KEY}`,
        },
      });

      const suscription = data.data[data.data.length - 1];
      if (!rentingProduct || !assistanceProduct) {
        return res.status(404).json({
          success: false,
          message: 'Products not found.',
        });
      }

      rentingProduct = suscription.products.find((p: any) => p.name.includes('Renting'));
      assistanceProduct = suscription.products.find((p: any) => p.name.includes('Asistencia'));
    }

    const response = {
      message: 'Last subscription found.',
      success: true,
      data: {
        rentingProduct,
        assistanceProduct,
      },
    }
    console.log('response', response);
    return res.status(200).json(response);
  } catch (error) {
    logger.error(`[getLastSubscriptionOfAssociate] Error: ${error instanceof Error ? error.message : error}`, error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching last subscription.',
    });
  }
}
