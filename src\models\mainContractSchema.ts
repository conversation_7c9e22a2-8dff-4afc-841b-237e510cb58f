import { DateTime } from 'luxon';
import { Schema, model } from 'mongoose';

const mainContractSchema = new Schema({
  contractNumber: {
    type: String,
    unique: true,
    required: [true, 'Contract number is required'],
  },
  subContracts: {
    type: Array,
    default: [],
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  documentId: {
    type: Schema.Types.ObjectId,
    ref: 'DocumentSchema',
    // required: [true, 'Document is required'],
  },
  stockId: {
    type: Schema.Types.ObjectId,
    ref: 'StockVehicleSchema',
  },
  associatedId: {
    type: Schema.Types.ObjectId,
    ref: 'associateSchema',
  },
  downPayment: {
    type: Number,
    default: 0,
  },
  finalPrice: {
    type: Number,
    required: [true, 'Finally car price is required'],
  },
  weeklyRent: {
    type: Number,
    required: [true, 'Week payment is required'],
  },
  allPayments: {
    type: Array,
    required: [true, 'All payments is required'],
  },
  totalPrice: {
    type: Number,
    required: [true, 'Total price is required'],
  },
  deliveredDate: {
    type: Date,
    required: [true, 'Delivered date is required'],
  },

  deliveryData: {
    type: {
      date: Date, // UTC date of delivery
      // dateString: String,
      timezone: String,
      // Iso string to save the date exactly as it is, to avoid any timezone conversion
      isoStringRealDate: String,
    },
  },

  createdAt: {
    type: Date,
    default: Date.now,
  },
  updateAt: {
    type: Date,
  },
});

mainContractSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

mainContractSchema.pre('save', function (next) {
  if (this.isModified('deliveryData') || this.isNew) {
    // if (this.deliveryData && this.deliveryData.date && this.deliveryData.timezone) {
    const date = this.deliveryData?.date || this.deliveredDate;
    const timezone = this.deliveryData?.timezone || 'America/Mexico_City';
    this.deliveryData = {
      date,
      timezone,
      isoStringRealDate:
        DateTime.fromJSDate(date, {
          zone: timezone,
        }).toISO() || undefined,
    };
  }

  // Validate if deliveredDate changed, if so, update the deliveryData object with the new date and timezone if it exists
  // to keep the isoStringRealDate updated with the new deliveredDate
  // if deliveryDate doesn't exists, create it with the current date and timezone

  if (this.isModified('deliveredDate') || this.isNew) {
    if (!this.deliveryData) {
      this.deliveryData = {
        date: this.deliveredDate,
        timezone: 'America/Mexico_City',
        isoStringRealDate:
          DateTime.fromJSDate(this.deliveredDate, {
            zone: 'utc',
          }).toISO() || undefined,
      };
    } else {
      this.deliveryData.date = this.deliveredDate;
      this.deliveryData.timezone = this.deliveryData?.timezone || 'America/Mexico_City';
      this.deliveryData.isoStringRealDate =
        DateTime.fromJSDate(this.deliveredDate, {
          zone: this.deliveryData.timezone,
        }).toISO() || undefined;
    }
  }

  next();
});

mainContractSchema.pre('updateOne', function (next) {
  const getUpdate = this.getUpdate() as any;

  if (getUpdate.deliveredDate) {
    if (!getUpdate.deliveryData) {
      getUpdate.deliveryData = {
        date: getUpdate.deliveredDate,
        timezone: 'America/Mexico_City',
        isoStringRealDate:
          DateTime.fromJSDate(getUpdate.deliveredDate, {
            zone: 'utc',
          }).toISO() || undefined,
      };
    } else {
      getUpdate.deliveryData.date = getUpdate.deliveredDate;
      getUpdate.deliveryData.timezone = getUpdate.deliveryData?.timezone || 'America/Mexico_City';
      getUpdate.deliveryData.isoStringRealDate =
        DateTime.fromJSDate(getUpdate.deliveredDate, {
          zone: getUpdate.deliveryData.timezone,
        }).toISO() || undefined;
    }
  }

  next();
});

const MainContractSchema = model('MainContractSchema', mainContractSchema);

const mainContractInstance = new MainContractSchema();

export type MainContractInstanceType = typeof mainContractInstance;

export default MainContractSchema;
