import vendorDB from '@/vendor-platform/db';
import { Types, Schema, Document } from 'mongoose';
import { FleetOrderStatus } from './fleet-order.model';

export enum SLAAlertType {
  SLA_WARNING = 'sla_warning', // 2 días antes del vencimiento
  SLA_EXCEEDED = 'sla_exceeded' // Cuando se excede el SLA
}

export interface IFleetOrderSLAAlert extends Document {
  orderId: Types.ObjectId;
  orderNumber: string; // Para referencia rápida
  alertType: SLAAlertType;
  status: FleetOrderStatus;
  deadline: Date;
  daysRemaining: number;
  isResolved: boolean;
  sentToSlack: boolean;
  slackMessageId?: string; // Para poder actualizar el mensaje en Slack
  createdAt: Date;
  updatedAt: Date;
}

const fleetOrderSLAAlertSchema = new Schema<IFleetOrderSLAAlert>(
  {
    orderId: {
      type: Schema.Types.ObjectId,
      ref: 'FleetOrder',
      required: true,
    },
    
    orderNumber: {
      type: String,
      required: true,
    },
    
    alertType: {
      type: String,
      enum: Object.values(SLAAlertType),
      required: true,
    },
    
    status: {
      type: String,
      enum: Object.values(FleetOrderStatus),
      required: true,
    },
    
    deadline: {
      type: Date,
      required: true,
    },
    
    daysRemaining: {
      type: Number,
      required: true,
    },
    
    isResolved: {
      type: Boolean,
      default: false,
    },
    
    sentToSlack: {
      type: Boolean,
      default: false,
    },
    
    slackMessageId: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

// Índices para búsquedas eficientes
fleetOrderSLAAlertSchema.index({ orderId: 1 });
fleetOrderSLAAlertSchema.index({ alertType: 1 });
fleetOrderSLAAlertSchema.index({ status: 1 });
fleetOrderSLAAlertSchema.index({ isResolved: 1 });
fleetOrderSLAAlertSchema.index({ sentToSlack: 1 });
fleetOrderSLAAlertSchema.index({ deadline: 1 });
fleetOrderSLAAlertSchema.index({ createdAt: 1 });

// Índice compuesto para evitar alertas duplicadas
fleetOrderSLAAlertSchema.index(
  { orderId: 1, alertType: 1, status: 1 },
  { unique: true }
);

// Virtual para popular la orden
fleetOrderSLAAlertSchema.virtual('order', {
  ref: 'FleetOrder',
  localField: 'orderId',
  foreignField: '_id',
  justOne: true,
});

fleetOrderSLAAlertSchema.set('toJSON', { virtuals: true });
fleetOrderSLAAlertSchema.set('toObject', { virtuals: true });

const FleetOrderSLAAlert = vendorDB.model<IFleetOrderSLAAlert>(
  'FleetOrderSLAAlert',
  fleetOrderSLAAlertSchema
);

export default FleetOrderSLAAlert;
