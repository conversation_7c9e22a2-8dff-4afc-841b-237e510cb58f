import { Router } from 'express';
import { updateAssociatePaymentById, updateByRelationId } from '../controllers/editAssociatePayment';
import {
  getAssociatePaymentById,
  getAssociatePaymentByVehicleId,
  reviewReactivationPayment,
} from '../controllers/getAssociatePayments';

const associatePaymentsRoute = Router();

associatePaymentsRoute.patch('/updateByRelationId', updateByRelationId);
associatePaymentsRoute.patch('/update/:associatePaymentId', updateAssociatePaymentById);

associatePaymentsRoute.get('/get/:associatePaymentId', getAssociatePaymentById);
associatePaymentsRoute.get('/getByVehicleId/:vehicleId', getAssociatePaymentByVehicleId);
associatePaymentsRoute.post('/review-unlook-payment', reviewReactivationPayment);

export default associatePaymentsRoute;
