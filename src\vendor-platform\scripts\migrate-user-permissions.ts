import { connectDB } from '@/database';
import UserVendorModel from '../modules/users/models/user.model';
import UserRolesPermissions, {
  GlobalUserRole,
  UserType,
} from '../modules/user-roles-permissions/models/user-roles-permissions.model';
import { UserRolesPermissionsService } from '../modules/user-roles-permissions/services/user-roles-permissions.service';
import { Types } from 'mongoose';

const userRolesPermissionsService = new UserRolesPermissionsService();

/**
 * Script de migración para mover usuarios existentes al nuevo sistema de roles y permisos
 */
async function migrateUserPermissions() {
  console.log('🚀 Iniciando migración de permisos de usuarios...');
  try {
    // Conectar a la base de datos
    await connectDB();
    console.log('✅ Conectado a la base de datos');

    // Obtener todos los usuarios existentes
    const users = await UserVendorModel.find({}).lean();
    console.log(`📊 Encontrados ${users.length} usuarios para migrar`);

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const user of users) {
      try {
        // Verificar si ya tiene permisos en el nuevo sistema
        const existingPermissions = await UserRolesPermissions.findOne({ userId: user._id });

        if (existingPermissions) {
          console.log(`⏭️ Usuario ${user.email} ya tiene permisos configurados, saltando...`);
          skippedCount++;
          continue;
        }

        // Mapear userType del usuario
        let userType: UserType = user.userType || 'workshop';

        // Cambiar 'superAdmin' a 'ocn'
        if (userType === ('superAdmin' as any)) {
          userType = 'ocn';
        }

        // Determinar roles basados en el userType y roles existentes
        const roles = mapUserTypeToRoles(userType, user.roles || []);

        // Determinar organizationId si es necesario
        let organizationId: Types.ObjectId | undefined;
        if (userType === 'organization' && user.organizationId) {
          organizationId = new Types.ObjectId(user.organizationId);
        }

        // Crear permisos para el usuario
        await userRolesPermissionsService.createUserPermissions({
          userId: new Types.ObjectId(user._id),
          userType,
          organizationId,
          roles,
          createdBy: new Types.ObjectId(user._id), // El usuario se crea a sí mismo en la migración
        });

        console.log(`✅ Migrado usuario ${user.email} (${userType}) con roles: ${roles.join(', ')}`);
        migratedCount++;
      } catch (error) {
        console.error(`❌ Error migrando usuario ${user.email}:`, error);
        errorCount++;
      }
    }

    console.log('\n📊 Resumen de migración:');
    console.log(`✅ Usuarios migrados: ${migratedCount}`);
    console.log(`⏭️ Usuarios saltados: ${skippedCount}`);
    console.log(`❌ Errores: ${errorCount}`);
    console.log(`📊 Total procesados: ${migratedCount + skippedCount + errorCount}/${users.length}`);

    if (errorCount === 0) {
      console.log('\n🎉 Migración completada exitosamente!');
    } else {
      console.log('\n⚠️ Migración completada con algunos errores. Revisar logs arriba.');
    }
  } catch (error) {
    console.error('💥 Error fatal en la migración:', error);
    process.exit(1);
  }
}

/**
 * Mapear userType a roles del nuevo sistema
 */
function mapUserTypeToRoles(userType: UserType, existingRoles: string[]): GlobalUserRole[] {
  const roles: GlobalUserRole[] = [];

  switch (userType) {
    case 'ocn':
      // Usuarios OCN obtienen rol de super admin
      roles.push(GlobalUserRole.OCN_SUPER_ADMIN);
      break;

    case 'organization':
      // Mapear roles existentes a roles de organización
      if (existingRoles.includes('admin')) {
        roles.push(GlobalUserRole.ORG_ADMIN);
      } else if (existingRoles.includes('manager')) {
        roles.push(GlobalUserRole.ORG_MANAGER);
      } else if (existingRoles.includes('supervisor')) {
        roles.push(GlobalUserRole.ORG_SUPERVISOR);
      } else if (existingRoles.includes('operator')) {
        roles.push(GlobalUserRole.ORG_OPERATOR);
      } else {
        roles.push(GlobalUserRole.ORG_VIEWER);
      }
      break;

    case 'workshop':
      // Mapear roles de taller
      if (existingRoles.includes('admin')) {
        roles.push(GlobalUserRole.WORKSHOP_ADMIN);
      } else if (existingRoles.includes('manager')) {
        roles.push(GlobalUserRole.WORKSHOP_MANAGER);
      } else {
        roles.push(GlobalUserRole.WORKSHOP_TECHNICIAN);
      }
      break;

    case 'company':
      // Mapear roles de empresa
      if (existingRoles.includes('admin')) {
        roles.push(GlobalUserRole.COMPANY_ADMIN);
      } else if (existingRoles.includes('manager')) {
        roles.push(GlobalUserRole.COMPANY_MANAGER);
      } else {
        roles.push(GlobalUserRole.COMPANY_ADMIN); // Por defecto admin
      }
      break;

    case 'company-gestor':
      roles.push(GlobalUserRole.COMPANY_GESTOR);
      break;

    default:
      // Por defecto, asignar rol de viewer de organización
      roles.push(GlobalUserRole.ORG_VIEWER);
      break;
  }

  return roles;
}

/**
 * Función para revertir la migración (rollback)
 */
async function rollbackMigration() {
  console.log('🔄 Iniciando rollback de migración...');

  try {
    await connectDB();
    console.log('✅ Conectado a la base de datos');

    const result = await UserRolesPermissions.deleteMany({});
    console.log(`🗑️ Eliminados ${result.deletedCount} registros de permisos`);

    console.log('✅ Rollback completado exitosamente');
  } catch (error) {
    console.error('💥 Error en rollback:', error);
    process.exit(1);
  }
}

/**
 * Función para verificar el estado de la migración
 */
async function checkMigrationStatus() {
  console.log('🔍 Verificando estado de migración...');

  try {
    await connectDB();
    console.log('✅ Conectado a la base de datos');

    const [totalUsers, usersWithPermissions] = await Promise.all([
      UserVendorModel.countDocuments({}),
      UserRolesPermissions.countDocuments({}),
    ]);

    console.log('\n📊 Estado actual:');
    console.log(`👥 Total usuarios: ${totalUsers}`);
    console.log(`🔐 Usuarios con permisos: ${usersWithPermissions}`);
    console.log(`📈 Progreso: ${((usersWithPermissions / totalUsers) * 100).toFixed(1)}%`);

    if (usersWithPermissions === totalUsers) {
      console.log('✅ Migración completa');
    } else if (usersWithPermissions > 0) {
      console.log('⚠️ Migración parcial');
    } else {
      console.log('❌ Migración no iniciada');
    }

    // Mostrar distribución por tipo de usuario
    const userTypeDistribution = await UserRolesPermissions.aggregate([
      { $group: { _id: '$userType', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    console.log('\n📊 Distribución por tipo de usuario:');
    userTypeDistribution.forEach((item) => {
      console.log(`  ${item._id}: ${item.count}`);
    });
  } catch (error) {
    console.error('💥 Error verificando estado:', error);
    process.exit(1);
  }
}

// Ejecutar según argumentos de línea de comandos
const command = process.argv[2];

switch (command) {
  case 'migrate':
    migrateUserPermissions()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
    break;

  case 'rollback':
    rollbackMigration()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
    break;

  case 'status':
    checkMigrationStatus()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
    break;

  default:
    console.log('📖 Uso:');
    console.log('  npm run migrate-permissions migrate   - Ejecutar migración');
    console.log('  npm run migrate-permissions rollback  - Revertir migración');
    console.log('  npm run migrate-permissions status    - Verificar estado');
    process.exit(1);
}

export { migrateUserPermissions, rollbackMigration, checkMigrationStatus };
