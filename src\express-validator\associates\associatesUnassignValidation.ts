import { associateText } from '../../constants';
import StockVehicle from '../../models/StockVehicleSchema';
import Associate from '../../models/associateSchema';

const unasignSchema = {
  vehicleId: {
    notEmpty: true,
    errorMessage: 'El id del vehiculo es requerido',
    custom: {
      options: async (value: string) => {
        const vehicle = await StockVehicle.findById(value);
        if (!vehicle) {
          return Promise.reject(associateText.errors.vehicleNotFound);
        }
        return null;
      },
    },
  },
  associateId: {
    notEmpty: true,
    errorMessage: 'El id del usuario es requerido',
    custom: {
      options: async (value: string) => {
        const associate = await Associate.findById(value);
        if (!associate) {
          return Promise.reject(associateText.errors.associateNotFound);
        }
        return null;
      },
    },
  },
};

export default unasignSchema;
