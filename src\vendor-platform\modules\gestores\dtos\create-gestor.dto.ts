import z from 'zod';
import mongoose from 'mongoose';

const isObjectId = (value: string): boolean => mongoose.Types.ObjectId.isValid(value);

export const createGestorDto = z.object({
  name: z
    .string({ required_error: 'El nombre es obligatorio.' })
    .trim()
    .min(3, { message: 'El nombre debe tener al menos 3 caracteres.' }),
  email: z
    .string({ required_error: 'El correo electrónico es obligatorio.' })
    .trim()
    .email({ message: 'El correo electrónico no es válido.' }),
  phone: z
    .string({ required_error: 'El teléfono es obligatorio.' })
    .trim()
    .min(7, { message: 'El teléfono parece demasiado corto.' }),
  tramites: z
    .array(z.string().refine(isObjectId, { message: 'Uno o más IDs de trámite no son válidos.' }))
    .optional()
    .default([]),

  scheduleConfig: z.any().optional(),
});

export type CreateGestorDtoType = z.infer<typeof createGestorDto>;
