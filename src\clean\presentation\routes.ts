const multer = require('multer');
import { Router } from 'express';
import { cleanAuthMiddleware } from '../../middlewares/cleanAuth';

import {
  createAdmissionRequestResource,
  getPublicAdmissionRequestResource,
  getAdmissionRequestResource,
  getAdmissionRequestScreenshots,
  updateRequestPersonalDataResource,
  updateRequestDocumentsResource,
  mediaUploadResource,
  palencaWebhookResource,
  addPalencaAccount,
  getPlatformEarningsResource,
  searchAdmissionRequestsResource,
  getRequestDocumentsAnalysisResource,
  approveRequestDocumentResource,
  rejectRequestDocumentResource,
  approveDocumentAnalysisResource,
  getHomeVisitResource,
  getPlatformMetricsResource,
  getEventsResource,
  approveRiskAnalysisResource,
  rejectRiskAnalysisResource,
  rejectDocumentsAnalysisResource,
  upadtePlatformMetricResource,
  createAdmissionRequestResourceByCsv,
  updateRequestHomeVisitResource,
  addAvalDataResource,
  addAvalDataResourceWithDocs,
  approveSocialAnalysisResource,
  executeSocialAnalysisResource,
  retryAnalysisResource,
  rejectSocialAnalysisResource,
  executeDocumentsAnalysisResource,
  latLongLookup,
  ipLookup,
  sendLocationGatheringMessage,
  resetHomeVisitScheduleLinkSendDate,
  findAdmissionRequestByAssociateId,
  fetchVehiceViolation,
  fetchVehiceAmountViolation,
  sendHomeImageUploadMessage,
} from './resources';
import { errorHandler } from '../errors/express';
import { upload as uploadDiskStorage } from '../../multer/multer';
import * as fields from '../../multer/fields/admissionRequest';
import {
  cancelAppointment,
  createAppointment,
  getAppointmentForDriverWebApp,
  getAvailableSlots,
} from '../../modules/Calendar/controllers/calendar.controller';

import { saveDriverProfileData, bulkUpdateDriverProfileData } from '../../controllers/ocrData';

const storage = multer.memoryStorage();
const upload = multer({ storage });
const publicRouter = Router();
const secureRouter = Router();
/**
 * making this because publicRouter isn't work with query strings,
 * and right now i don't want to change public router because it's being
 * used in critical flows.
 */
const routerWithoutAuthentication = Router();
secureRouter.use(cleanAuthMiddleware);

const PUBLIC_ADMISSION_REQUESTS_PATH = '/public/admission/requests';
const ADMISSION_PATH = '/admission/requests';

/*
 * Secure routes
 */

publicRouter.post(`${ADMISSION_PATH}`, errorHandler(createAdmissionRequestResource));
secureRouter.post(
  `${ADMISSION_PATH}/csv`,
  upload.single('file'),
  errorHandler(createAdmissionRequestResourceByCsv)
);
secureRouter.post(`${ADMISSION_PATH}/search`, errorHandler(searchAdmissionRequestsResource));
secureRouter.post(`${ADMISSION_PATH}/find`, errorHandler(findAdmissionRequestByAssociateId));
secureRouter.get(`${ADMISSION_PATH}/:requestId`, errorHandler(getAdmissionRequestResource));
secureRouter.get(`${ADMISSION_PATH}/screenshots/:requestId`, errorHandler(getAdmissionRequestScreenshots));
secureRouter.patch(
  `${ADMISSION_PATH}/:requestId/personal-data`,
  errorHandler(updateRequestPersonalDataResource)
);
secureRouter.patch(`${ADMISSION_PATH}/:requestId/documents`, errorHandler(updateRequestDocumentsResource));
secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents/:documentType/approve`,
  errorHandler(approveRequestDocumentResource)
);
secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents/:documentType/reject`,
  errorHandler(rejectRequestDocumentResource)
);
secureRouter.get(
  `${ADMISSION_PATH}/:requestId/documents-analysis/:documentClassification`,
  errorHandler(getRequestDocumentsAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents-analysis/execute`,
  errorHandler(executeDocumentsAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents-analysis/approve`,
  errorHandler(approveDocumentAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/documents-analysis/reject`,
  errorHandler(rejectDocumentsAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/social-analysis/execute`,
  errorHandler(executeSocialAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/social-analysis/approve`,
  errorHandler(approveSocialAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/social-analysis/reject`,
  errorHandler(rejectSocialAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/risk-analysis/approve`,
  errorHandler(approveRiskAnalysisResource)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/risk-analysis/reject`,
  errorHandler(rejectRiskAnalysisResource)
);

secureRouter.post(`${ADMISSION_PATH}/:requestId/retry-analysis`, errorHandler(retryAnalysisResource));

secureRouter.get(`${ADMISSION_PATH}/:requestId/home-visit`, errorHandler(getHomeVisitResource));
secureRouter.patch(`${ADMISSION_PATH}/:requestId/home-visit`, errorHandler(updateRequestHomeVisitResource));
secureRouter.patch(
  `${ADMISSION_PATH}/:requestId/reset-home-visit-schedule-link-send-date`,
  errorHandler(resetHomeVisitScheduleLinkSendDate)
);

secureRouter.get(
  `${ADMISSION_PATH}/:requestId/earnings/:platform`,
  errorHandler(getPlatformEarningsResource)
);
secureRouter.get(`${ADMISSION_PATH}/:requestId/metrics/:platform`, errorHandler(getPlatformMetricsResource));
secureRouter.post(
  `${ADMISSION_PATH}/:requestId/send-location-gathering-message`,
  errorHandler(sendLocationGatheringMessage)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/home-image-upload-message`,
  errorHandler(sendHomeImageUploadMessage)
);

secureRouter.patch(
  `${ADMISSION_PATH}/:requestId/update-platform-metric`,
  errorHandler(upadtePlatformMetricResource)
);

secureRouter.patch(
  `${ADMISSION_PATH}/:requestId/aval-data-with-documents-admin`,
  uploadDiskStorage.fields(fields.avalFiles),
  errorHandler(addAvalDataResourceWithDocs)
);

secureRouter.post(
  `${ADMISSION_PATH}/:requestId/save-driver-profile-data`,
  errorHandler(async (req, res, next) => {
    await saveDriverProfileData(req, res, next);
  })
);

secureRouter.post(
  `${ADMISSION_PATH}/bulk-update-driver-profile-data`,
  upload.single('file'),
  errorHandler(async (req, res, next) => {
    await bulkUpdateDriverProfileData(req, res, next);
  })
);
secureRouter.get(`/fetchVehiceViolation/:plate`, errorHandler(fetchVehiceViolation));
secureRouter.get(`/fetchVehiceAmountViolation/:plate`, errorHandler(fetchVehiceAmountViolation));

// Events
publicRouter.get(`/events/:entityType/:entityId`, errorHandler(getEventsResource));

/*
 * Public routes
 */

// Driver app
publicRouter.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId`,
  errorHandler(getPublicAdmissionRequestResource)
);
publicRouter.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/palenca-accounts`,
  errorHandler(addPalencaAccount)
);
publicRouter.patch(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/personal-data`,
  errorHandler(updateRequestPersonalDataResource)
);
publicRouter.patch(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/documents`,
  errorHandler(updateRequestDocumentsResource)
);
publicRouter.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/aval-data`,
  errorHandler(addAvalDataResource)
);
publicRouter.patch(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/aval-data-with-documents`,
  uploadDiskStorage.fields(fields.avalFiles),
  errorHandler(addAvalDataResourceWithDocs)
);

publicRouter.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/lat-long-lookup`,
  errorHandler(latLongLookup)
);
publicRouter.post(`${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/ip-lookup`, errorHandler(ipLookup));
publicRouter.patch(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/home-visit`,
  errorHandler(updateRequestHomeVisitResource)
);

// Media
publicRouter.post(
  '/media/upload',
  uploadDiskStorage.fields(fields.homeVisitDoc),
  errorHandler(mediaUploadResource)
);

// Webhooks
publicRouter.post('/webhooks/palenca', errorHandler(palencaWebhookResource));

publicRouter.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/home-visit`,
  errorHandler(getHomeVisitResource)
);

routerWithoutAuthentication.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/:date/calendar/slots/get-available-slots`,
  errorHandler(getAvailableSlots)
);

routerWithoutAuthentication.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/calendar/appointment/create`,
  errorHandler(createAppointment)
);

routerWithoutAuthentication.get(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/calendar/appointment`,
  errorHandler(getAppointmentForDriverWebApp)
);

routerWithoutAuthentication.post(
  `${PUBLIC_ADMISSION_REQUESTS_PATH}/:requestId/calendar/appointment/cancel`,
  errorHandler(cancelAppointment)
);

// Merge routers
const router = Router();
router.use(routerWithoutAuthentication);
router.use(publicRouter);
router.use(secureRouter);

export const cleanRouter = router;
