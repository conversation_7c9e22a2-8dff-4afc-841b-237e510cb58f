import { Schema, model } from 'mongoose';
import { getCurrentDateObject } from '../services/timestamps';

const palenca = new Schema({
  type: {
    type: String,
  },
  body: {
    type: Object,
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
palenca.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const Palenca = model('Palenca', palenca);

export default Palenca;
