# Fleet Orders API Testing
# @baseUrl = https://dev-api.onecarnow.com
@baseUrl = http://localhost:3000
@authToken = 
@ocnAuthToken = 

### Variables
@orderId = 
@orderNumber = FO-2024-03-001

### ==================== AUTHENTICATION ====================

### Login OCN User (required for Fleet Orders)
POST {{baseUrl}}/vendor-platform/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "your_password"
}

### ==================== USER ROLES & PERMISSIONS ====================

### Get my permissions
GET {{baseUrl}}/vendor-platform/user-roles-permissions/me
Authorization: Bearer {{ocnAuthToken}}

### Create user permissions (OCN only)
POST {{baseUrl}}/vendor-platform/user-roles-permissions
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "userId": "507f1f77bcf86cd799439011",
    "userType": "ocn",
    "roles": ["ocn_super_admin"]
}

### ==================== FLEET ORDERS - MAIN OPERATIONS ====================

### Create Fleet Order (OCN only)
POST {{baseUrl}}/vendor-platform/fleet-orders
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "month": 3,
    "year": 2024,
    "vehicles": [
        {
            "brand": "BYD",
            "dealer": "BYD Mexico",
            "model": "Dolphin Mini",
            "version": "Comfort",
            "quantity": 50,
            "unitPrice": 350000
        },
        {
            "brand": "MG",
            "dealer": "MG Mexico",
            "model": "MG5",
            "version": "Style",
            "quantity": 25,
            "unitPrice": 420000
        }
    ],
    "notificationEmails": [
        "<EMAIL>",
        "<EMAIL>"
    ]
}

### List Fleet Orders
GET {{baseUrl}}/vendor-platform/fleet-orders?page=1&limit=10
Authorization: Bearer {{ocnAuthToken}}

### List Fleet Orders with filters
GET {{baseUrl}}/vendor-platform/fleet-orders?status=created&year=2024&month=3
Authorization: Bearer {{ocnAuthToken}}

### Get Fleet Order by ID
GET {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}
Authorization: Bearer {{ocnAuthToken}}

### Get Fleet Order by Number
GET {{baseUrl}}/vendor-platform/fleet-orders/number/{{orderNumber}}
Authorization: Bearer {{ocnAuthToken}}

### Update Fleet Order Status to "sent"
PATCH {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}/status
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "status": "sent",
    "evidence": {
        "type": "log",
        "description": "Orden enviada por email a BYD Mexico y MG Mexico el 2024-03-05 a las 10:30 AM"
    },
    "notes": "Enviado a ambos proveedores con confirmación de recepción"
}

### Update Fleet Order Status to "dispersion"
PATCH {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}/status
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "status": "dispersion",
    "evidence": {
        "type": "document",
        "description": "Dispersión confirmada con fechas de entrega por ciudad",
        "url": "https://example.com/dispersion-document.pdf"
    }
}

### Update Dispersion
PATCH {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}/dispersion
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "dispersion": [
        {
            "state": "CDMX",
            "city": "Ciudad de México",
            "quantity": 40,
            "deliveryDate": "2024-03-25T00:00:00.000Z",
            "amount": 15800000
        },
        {
            "state": "Jalisco",
            "city": "Guadalajara",
            "quantity": 25,
            "deliveryDate": "2024-03-27T00:00:00.000Z",
            "amount": 8750000
        },
        {
            "state": "Nuevo León",
            "city": "Monterrey",
            "quantity": 10,
            "deliveryDate": "2024-03-30T00:00:00.000Z",
            "amount": 3950000
        }
    ]
}

### Delete Fleet Order (only if status is "created")
DELETE {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}
Authorization: Bearer {{ocnAuthToken}}

### ==================== SLA & ALERTS ====================

### Get Pending SLA Alerts
GET {{baseUrl}}/vendor-platform/fleet-orders/sla/alerts
Authorization: Bearer {{ocnAuthToken}}

### Get SLA Alerts by Type (warnings)
GET {{baseUrl}}/vendor-platform/fleet-orders/sla/alerts?type=sla_warning
Authorization: Bearer {{ocnAuthToken}}

### Get SLA Alerts by Type (exceeded)
GET {{baseUrl}}/vendor-platform/fleet-orders/sla/alerts?type=sla_exceeded
Authorization: Bearer {{ocnAuthToken}}

### Get Alerts for Specific Order
GET {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}/alerts
Authorization: Bearer {{ocnAuthToken}}

### Run Manual SLA Check
POST {{baseUrl}}/vendor-platform/fleet-orders/sla/check
Authorization: Bearer {{ocnAuthToken}}

### Get SLA Statistics
GET {{baseUrl}}/vendor-platform/fleet-orders/sla/statistics
Authorization: Bearer {{ocnAuthToken}}

### Get Daily SLA Summary
GET {{baseUrl}}/vendor-platform/fleet-orders/sla/daily-summary
Authorization: Bearer {{ocnAuthToken}}

### Resolve Alert
PATCH {{baseUrl}}/vendor-platform/fleet-orders/sla/alerts/507f1f77bcf86cd799439011/resolve
Authorization: Bearer {{ocnAuthToken}}

### Resolve All Alerts for Order
PATCH {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}/alerts/resolve-all
Authorization: Bearer {{ocnAuthToken}}

### ==================== SLACK INTEGRATION ====================

### Send Daily Summary to Slack
POST {{baseUrl}}/vendor-platform/fleet-orders/slack/daily-summary
Authorization: Bearer {{ocnAuthToken}}

### Send Pending Alerts to Slack
POST {{baseUrl}}/vendor-platform/fleet-orders/slack/send-pending-alerts
Authorization: Bearer {{ocnAuthToken}}

### ==================== MAINTENANCE ====================

### Cleanup Old Alerts
DELETE {{baseUrl}}/vendor-platform/fleet-orders/sla/cleanup
Authorization: Bearer {{ocnAuthToken}}

### ==================== ERROR TESTING ====================

### Try to create order for past period (should fail)
POST {{baseUrl}}/vendor-platform/fleet-orders
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "month": 1,
    "year": 2023,
    "vehicles": [
        {
            "brand": "BYD",
            "dealer": "BYD Mexico",
            "model": "Dolphin Mini",
            "version": "Comfort",
            "quantity": 10,
            "unitPrice": 350000
        }
    ],
    "notificationEmails": ["<EMAIL>"]
}

### Try to create duplicate order (should fail)
POST {{baseUrl}}/vendor-platform/fleet-orders
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "month": 3,
    "year": 2024,
    "vehicles": [
        {
            "brand": "BYD",
            "dealer": "BYD Mexico",
            "model": "Dolphin Mini",
            "version": "Comfort",
            "quantity": 10,
            "unitPrice": 350000
        }
    ],
    "notificationEmails": ["<EMAIL>"]
}

### Try invalid status transition (should fail)
PATCH {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}/status
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "status": "delivered"
}

### Try to update dispersion on wrong status (should fail)
PATCH {{baseUrl}}/vendor-platform/fleet-orders/{{orderId}}/dispersion
Authorization: Bearer {{ocnAuthToken}}
Content-Type: application/json

{
    "dispersion": [
        {
            "state": "CDMX",
            "city": "Ciudad de México",
            "quantity": 10,
            "deliveryDate": "2024-03-25T00:00:00.000Z",
            "amount": 3500000
        }
    ]
}
