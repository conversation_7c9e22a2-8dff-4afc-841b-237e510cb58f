{"compilerOptions": {"target": "es2016", "module": "commonjs", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@vendor/*": ["src/vendor-platform/*"], "@vendorDb/*": ["src/vendor-platform/db/*"], "@vendorModules/*": ["src/vendor-platform/modules/*"]}, "resolveJsonModule": true, "outDir": "./build", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictFunctionTypes": true, "noImplicitReturns": true, "skipLibCheck": true}, "include": ["src/**/*", "fix-imports.mjs"], "exclude": ["node_modules", "build"]}