import { SOCIAL_SCORING_URL } from '@/constants/onboarding';
import { logger } from '@/clean/lib/logger';
import { getHomeVisitModelBody, getPreApprovalBody } from '../lib/helpers';

/**
 * Service for risk score calculation
 */
export const getRiskScoreService = async (requestId: string) => {
  const body = await getPreApprovalBody(requestId);

  const response = await fetch(`${SOCIAL_SCORING_URL}/risk-scoring`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();

  if (data.status !== 'success') {
    logger.error(`[getRiskScoreService] Error in socialscoring service status: ${data.message}`);
    throw new Error(data.message);
  }

  return data;
};

/**
 * Service for pre-approval processing
 */
export const getPreApprovalService = async (requestId: string) => {
  const body = await getPreApprovalBody(requestId);

  const response = await fetch(`${SOCIAL_SCORING_URL}/v2/pre-approval`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
    },
    body: JSON.stringify(body),
  });

  const data = await response.json();
  let message = data.result;
  let status = '';

  if (data.result.includes(':')) {
    [message, status] = data.result.split(':');
    status = status.trim();
  }

  return {
    message,
    status,
  };
};

/**
 * Service for home visit scoring
 */
export const getHomevisitScoreService = async (requestId: string) => {
  const requestBody = await getHomeVisitModelBody(requestId);

  if (!requestBody || !requestBody.earningsAnalysis) {
    logger.warn(`[getHomevisitScoreService] Request not found ${requestId}`);
    throw new Error('Request not found');
  }

  const response = await fetch(`${SOCIAL_SCORING_URL}/homevisit`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
    },
    body: JSON.stringify(requestBody),
  });

  const data = await response.json();

  const score = data.probability;
  const message = `Probability: ${(score * 100).toFixed(2)}% for approval`;
  const status = data.message.toLowerCase();

  return {
    ...data,
    message,
    status,
    score,
  };
};

/**
 * Service for home visit stacking classifier scoring
 */
export const getHomevisitStackingclfScoringService = async (requestId: string) => {
  const requestBody = await getHomeVisitModelBody(requestId);

  if (!requestBody || !requestBody.earningsAnalysis) {
    logger.warn(`[getHomevisitStackingclfScoringService] Request not found ${requestId}`);
    throw new Error('Request not found');
  }

  const response = await fetch(`${SOCIAL_SCORING_URL}/homevisit/stacking-clf`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
    },
    body: JSON.stringify(requestBody),
  });

  const data = await response.json();

  const score = data.probability;
  const message = `Probability: ${(score * 100).toFixed(2)}% for approval`;
  const status = data.message.toLowerCase();

  return {
    ...data,
    message,
    status,
    score,
  };
};

/**
 * Service for home visit voting classifier scoring
 */
export const getHomevisitVotingclfScoringService = async (requestId: string) => {
  const requestBody = await getHomeVisitModelBody(requestId);

  if (!requestBody || !requestBody.earningsAnalysis) {
    logger.warn(`[getHomevisitVotingclfScoringService] Request not found ${requestId}`);
    throw new Error('Request not found');
  }

  const response = await fetch(`${SOCIAL_SCORING_URL}/homevisit/voting-clf`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
    },
    body: JSON.stringify(requestBody),
  });

  const data = await response.json();

  const score = data.probability;
  const message = `Probability: ${(score * 100).toFixed(2)}% for approval`;
  const status = data.message.toLowerCase();

  return {
    ...data,
    message,
    status,
    score,
  };
};
