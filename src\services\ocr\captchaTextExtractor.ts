import { AllowedMimeType, parseTextFromSource, Source } from './llmClients/geminiClient';
import { logger } from '@/clean/lib/logger';

const prompt = `Extract four character alphanumeric text from the provided Captcha image and and output a structured JSON object using the following JSON schema:
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "captcha": {
      "type": "string"
    }
  },
  "required": ["captcha"],
  "additionalProperties": false
}`;

const getTextFromCaptcha = async (image: Express.Multer.File) => {
  try {
    // Validate the image object
    if (!image || !image.buffer || !image.mimetype) {
      throw new Error('Invalid image file provided');
    }

    // Validate MIME type
    const allowedMimeTypes: AllowedMimeType[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedMimeTypes.includes(image.mimetype as AllowedMimeType)) {
      throw new Error(`Unsupported MIME type: ${image.mimetype}`);
    }

    // Prepare the source object
    const source: Source = {
      data: image.buffer.toString('base64'),
      media_type: image.mimetype as AllowedMimeType,
      prompt: prompt,
    };

    logger.info(`[getTextFromCaptcha] - Processing image with MIME type: ${source.media_type}`);

    // Call the Anthropic API to parse the image
    const result = await parseTextFromSource(source);
    return result;
  } catch (error) {
    logger.error(`[getTextFromCaptcha] - Error: ${JSON.stringify(error)}`);
    throw new Error(`Failed to extract captcha data: ${JSON.stringify(error)}`);
  }
};

export { getTextFromCaptcha };
