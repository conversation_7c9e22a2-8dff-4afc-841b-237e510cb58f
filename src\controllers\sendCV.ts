import { getFiles, getUrlSingleFile, uploadFile } from '../aws/s3';
import { sendCV } from '../middlewares/email';
import Candidate from '../models/candidateSchema';
import Document from '../models/documentSchema';
import { capitalizeWords } from '../services/functions';
import { AsyncController } from '../types&interfaces/types';
import fs from 'fs';

export const sendCVEmail: AsyncController = async (req, res) => {
  const { name, email, birthday, country, state, city } = req.body;

  const cv: Express.Multer.File | undefined = req.file; // Obtener el archivo adjunto

  if (!name || !email || !birthday || !cv || !country || !state || !city)
    return res.status(404).send({ message: 'Completa todos los campos' });

  const nameUpper = capitalizeWords(name);
  const stateUpper = capitalizeWords(state);
  const cityUpper = capitalizeWords(city);

  try {
    const candidateExists = await Candidate.findOne({ email });

    if (candidateExists) {
      fs.unlink(cv.path, (err) => {
        if (err) {
          console.error(err);
          // Manejar el error si ocurre al eliminar el archivo
        }
      });

      return res
        .status(400)
        .send({ message: 'Ya has enviado tu solicitud, porfavor espera a que te contactemos 🙂' });
    }

    await sendCV({ name: nameUpper, email, birthday, cv, country, state: stateUpper, city: cityUpper });
    await uploadFile(cv, cv.filename, 'cvs/');

    const document = new Document({
      originalName: cv.originalname,
      path: 'cvs/' + cv.filename,
    });

    const candidate = new Candidate({
      name: nameUpper,
      email,
      birthday,
      country,
      state,
      city,
      document: document._id,
    });

    await candidate.save();

    document.candidateId = candidate._id;

    await Promise.all([candidate.save(), document.save()]);

    fs.unlink(cv.path, (err) => {
      if (err) {
        // Manejar el error si ocurre al eliminar el archivo
      }
    });

    return res.status(200).send({ message: 'Email enviado correctamente' });
  } catch (error) {
    return res.status(500).send({ message: 'Algo salió mal', error });
  }
};

export const getBucketFiles: AsyncController = async (req, res) => {
  try {
    const { Contents } = await getFiles();
    return res.status(200).send({ message: 'Consulta satisfactoria', files: Contents });
  } catch (error) {
    return res.status(500).send({ message: 'Algo salio mal', error });
  }
};

export const getUrlByNameFile: AsyncController = async (req, res) => {
  const { pathName, fileName } = req.params;
  try {
    const url = await getUrlSingleFile(pathName + '/' + fileName);
    return res.status(200).send({ message: 'Consulta satisfactoria', url });
  } catch (error) {
    return res.status(500).send({ message: 'Algo salio mal', error });
  }
};

export const getUrls: AsyncController = async (req, res) => {
  const file = req.params.filename;
  try {
    const url = await getUrlSingleFile(file);
    return res.status(200).send({ message: 'Consulta satisfactoria', url });
  } catch (error) {
    return res.status(500).send({ message: 'Algo salio mal', error });
  }
};
