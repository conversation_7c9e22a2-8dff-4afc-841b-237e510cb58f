import { Types } from 'mongoose';

import { Document, Schema, model } from 'mongoose';

// Used for the queries
export interface IDailyEarning {
  amount: number;

  countTrips: number;

  earningDate: Date;

  currency: string;
}

export interface IWeeklyEarning {
  totalAmount: number;

  totalTrips: number;

  fromDate: Date;

  toDate: Date;

  week: number;

  year: number;

  currency: string;

  dailyEarnings: IDailyEarning[];
}

export interface EarningMongoI extends Document {
  _id: Types.ObjectId;
  amount: number;
  currency: string;
  earningDate: Date;
  countTrips: number;
  requestId: string;
  cashAmount: number;
  platform: string;
  createdAt: Date;
  updatedAt: Date;
}

const earningsSchema = new Schema(
  {
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: true,
    },
    earningDate: {
      type: Date,
      required: true,
    },
    countTrips: {
      type: Number,
      required: true,
    },
    requestId: {
      type: Schema.Types.ObjectId,
      ref: 'AdmissionRequest',
      required: true,
    },
    cashAmount: {
      type: Number,
      required: true,
    },
    platform: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

export const EarningMongo = model<EarningMongoI>('Earning', earningsSchema, 'earnings');
