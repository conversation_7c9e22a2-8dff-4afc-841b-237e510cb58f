import { Router } from 'express';
import { validateCompanyAccess } from '../middlewares/company-permissions.middleware';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import {
  inviteUserToCompany,
  updateUserCompanyPermissions,
} from '../controllers/company-permissions.controller';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';

const companyPermissionsRouter = Router();

companyPermissionsRouter.post(
  '/companies/:companyId/users/invite',
  verifyTokenVendorPlatform,
  validateCompanyAccess,
  errorHandlerV2(inviteUserToCompany)
);

companyPermissionsRouter.patch(
  '/companies/:companyId/users/:userId/permissions',
  verifyTokenVendorPlatform,
  validateCompanyAccess,
  errorHandlerV2(updateUserCompanyPermissions)
);

export default companyPermissionsRouter;
