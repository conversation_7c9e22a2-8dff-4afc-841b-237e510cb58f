import { Router } from 'express';
import { verifyToken } from '../../../middlewares/verifyToken';
import weeklyRecords from './weeklyRecords.routes';
import weeklyCronRecords from './cronJob.routes';

const weeklyRecordsMain = Router();

weeklyRecordsMain.use('/weekly-records-cronjob', weeklyCronRecords);

// routes for admin panel
weeklyRecordsMain.use(verifyToken);

weeklyRecordsMain.use('/weekly-records', weeklyRecords);

export default weeklyRecordsMain;
