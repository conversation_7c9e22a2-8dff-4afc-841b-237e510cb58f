import fs from 'node:fs/promises';
import { logger } from './logger';

export const homeVisitDefaultObject = {
  status: 'pending',
  residentOwnershipStatus: '',
  comments: '',
  images: [],
  visitDate: new Date(),
  visitTime: '',
  houseInformation: {
    ownProperty: '',
    nameOfOwner: '',
    ownerRelative: '',
    ownerRelativeRelation: '',
    ownerPhone: '',
    typeOfHousing: 'Apartment',
    noOfBedrooms: 0,
    livingRoom: '',
    dinningRoom: '',
    kitchen: '',
    television: '',
    audioSystem: '',
    stove: '',
    refrigerator: '',
    washingMachine: '',
  },
  proofOfPropertyOwnership: [],
  visitorEmailAddress: '',
  doesProofOfAddressMatchLocation: '',
  characteristicsOfGarage: '',
  behaviourOfCustomerDuringCall: '',
  homeVisitStepsStatus: {
    personal: '',
    contact: '',
    address: '',
    family: '',
    property: '',
    automobile: '',
    debt: '',
    references: '',
    outcome: '',
  },
};

export const removeFile = async (filePath: string) => {
  try {
    await fs.unlink(filePath);
    logger.info(`[removeFile] Successfully removed File: ${filePath}`);
  } catch (err) {
    logger.error(`[removeFile] Error occured while removing file: ${filePath}`);
  }
};

export const getMeetingIdFromMeetingLink = (meetingLink: string) => {
  const meetindId = meetingLink?.split('/').pop();
  return meetindId;
};
