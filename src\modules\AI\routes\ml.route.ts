import { Router } from 'express';
import {
  getRiskScore,
  getPreApproval,
  getHomevisitVotingclfScoring,
  getHomevisitStackingclfScoring,
  getHomevisitScore,
} from '../controllers/ml.controller';

const router = Router();

// Risk scoring endpoints
router.get('/risk-scoring/:requestId', getRiskScore);

// Home visit scoring endpoints
router.get('/homevisit/voting-clf/:requestId', getHomevisitVotingclfScoring);
router.get('/homevisit/stacking-clf/:requestId', getHomevisitStackingclfScoring);

// Home visit score service endpoint
router.get('/homevisit/:requestId', getHomevisitScore);

// Pre-approval endpoints
router.get('/pre-approval/:requestId', getPreApproval);

export default router;
