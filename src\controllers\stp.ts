import type { AsyncController } from '../types&interfaces/types';
import { getUserData } from '../services/getUserData';
import { getSign } from '../services/createStpSing';
import { createStpAccounts } from '../services/createStpAccounts';
import orderRegisterSchema from '../models/orderRegisterSchema';
import {
  STP_ORDER_REGISTER_URL,
  STP_CONCILIATION_URL,
  STP_CONSTANTS,
  STP_ACCOUNT_BALANCE_URL,
  STP_MAIN_ACCOUNT,
} from '../constants';
import stpStatesChangeSchema from '../models/stpStatesChangeSchema';
import StpPaymentsChangeSchema from '../models/stpPaymentsChangeSchema';

export const createOriginalString: AsyncController = async (req, res) => {
  const { originalString } = req.body;
  const url = req.path;
  if (!originalString) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const { userId: adminId } = req.userId;
  const user = await getUserData(adminId);
  if (!user) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  try {
    const encryptedString = getSign(originalString, user?.name, url);
    return res.status(200).send({ encryptedString });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.originalString, error });
  }
};

export const orderRegister: AsyncController = async (req, res) => {
  const {
    cuentaBeneficiario,
    tipoCuentaOrdenante,
    nombreBeneficiario,
    rfcCurpBeneficiario,
    conceptoPago,
    institucionOperante,
    referenciaNumerica,
    claveRastreo,
    monto,
    tipoCuentaBeneficiario,
    institucionContraparte,
    tipoPago,
    cuentaOrdenante,
    rfcCurpOrdenante,
    empresa,
  } = req.body;
  if (
    !cuentaBeneficiario ||
    !tipoCuentaOrdenante ||
    !nombreBeneficiario ||
    !rfcCurpBeneficiario ||
    !conceptoPago ||
    !institucionOperante ||
    !referenciaNumerica ||
    !claveRastreo ||
    !monto ||
    !tipoCuentaBeneficiario ||
    !institucionContraparte ||
    !tipoPago ||
    !cuentaOrdenante ||
    !empresa ||
    empresa !== STP_CONSTANTS.empresa ||
    rfcCurpOrdenante !== STP_CONSTANTS.rfcCurpOrdenante
  ) {
    return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  }

  const url = req.path;
  const { userId: adminId } = req.userId;
  const user = await getUserData(adminId);
  if (!user) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const newSing =
    '||' +
    institucionContraparte +
    '|' +
    empresa +
    '|' +
    '|' +
    '|' +
    claveRastreo +
    '|' +
    institucionOperante +
    '|' +
    monto +
    '|' +
    tipoPago +
    '|' +
    tipoCuentaBeneficiario +
    '|' +
    '|' +
    cuentaOrdenante +
    '|' +
    rfcCurpOrdenante +
    '|' +
    tipoCuentaOrdenante +
    '|' +
    nombreBeneficiario +
    '|' +
    cuentaBeneficiario +
    '|' +
    rfcCurpBeneficiario +
    '||||||' +
    conceptoPago +
    '||||||' +
    referenciaNumerica +
    '||||||||';

  try {
    const firma = await getSign(newSing, user?.name, url);
    const stpOrderRegister = {
      cuentaBeneficiario,
      tipoCuentaOrdenante,
      nombreBeneficiario,
      rfcCurpBeneficiario,
      conceptoPago,
      institucionOperante,
      referenciaNumerica,
      claveRastreo,
      monto,
      tipoCuentaBeneficiario,
      institucionContraparte,
      tipoPago,
      cuentaOrdenante,
      rfcCurpOrdenante,
      empresa,
      firma,
    };

    const response = await fetch(STP_ORDER_REGISTER_URL, {
      method: 'PUT',
      body: JSON.stringify(stpOrderRegister),
      headers: { 'Content-Type': 'application/json' },
    });
    const data = await response.json();
    if (data?.resultado?.descripcionError)
      return res
        .status(500)
        .send({ message: STP_CONSTANTS.errors.createRegister, error: data?.resultado?.descripcionError });
    const orderRegisterConfirmation = await orderRegisterSchema.create({
      ...stpOrderRegister,
      stpConfirmation: data?.resultado,
    });
    return res.status(200).send({ orderRegisterConfirmation });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.createRegister, error });
  }
};

export const conciliation: AsyncController = async (req, res) => {
  const { curp, tipoOrden, empresa = STP_CONSTANTS.empresa } = req.body;
  if (!curp || !tipoOrden) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const { userId: adminId } = req.userId;
  const user = await getUserData(adminId);
  if (!user) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const url = req.path;

  const originalString = '||' + empresa + '|' + tipoOrden + '|||';
  try {
    const firma = await getSign(originalString, user?.name, url);
    const stpOrderConciliation = {
      tipoOrden,
      empresa,
      page: 0,
      firma,
    };
    const response = await fetch(STP_CONCILIATION_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(stpOrderConciliation),
    });
    const { datos, resultado } = await response.json();
    if (resultado?.descripcionError)
      return res
        .status(500)
        .send({ message: STP_CONSTANTS.errors.conciliation, error: resultado?.descripcionError });

    const result = datos?.filter((data: any) => data?.rfcCurpBeneficiario === curp);

    if (!result) return res.status(404).send({ message: STP_CONSTANTS.errors.conciliation });
    return res.status(200).send({ result });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.conciliation, error });
  }
};

export const getAllConciliations: AsyncController = async (req, res) => {
  const path = req.path;
  const { userId: adminId } = req.userId;
  const user = await getUserData(adminId);
  if (!user) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const tipoOrden =
    path.split('/')[2] === 'send' ? STP_CONSTANTS.typeOrder.send : STP_CONSTANTS.typeOrder.received;
  const empresa = STP_CONSTANTS.empresa;

  if (!tipoOrden) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const originalString = '||' + empresa + '|' + tipoOrden + '|||';
  const url = req.path;
  try {
    const firma = await getSign(originalString, user?.name, url);
    const stpOrderConciliation = {
      tipoOrden,
      empresa,
      page: 0,
      firma,
    };
    const response = await fetch(STP_CONCILIATION_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(stpOrderConciliation),
    });
    const result = await response.json();

    return res.status(200).send({ result });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.conciliation, error });
  }
};

export const findConciliationsByDate: AsyncController = async (req, res) => {
  const { fechaOperacion, tipoOrden } = req.body;
  const empresa = STP_CONSTANTS.empresa;

  if (!tipoOrden) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const { userId: adminId } = req.userId;
  const user = await getUserData(adminId);
  if (!user) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const url = req.path;
  const originalString = '||' + empresa + '|' + tipoOrden + '|' + fechaOperacion + '||';
  try {
    const firma = await getSign(originalString, user?.name, url);
    const stpOrderConciliation = {
      tipoOrden,
      empresa,
      page: 0,
      fechaOperacion,
      firma,
    };
    const response = await fetch(STP_CONCILIATION_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(stpOrderConciliation),
    });
    const result = await response.json();
    return res.status(200).send({ result });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.conciliation, error });
  }
};

export const checkAccountBalance: AsyncController = async (req, res) => {
  const empresa = STP_CONSTANTS.empresa;
  const cuentaOrdenante = STP_MAIN_ACCOUNT;

  if (!cuentaOrdenante) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const { userId: adminId } = req.userId;
  const user = await getUserData(adminId);
  if (!user) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const url = req.path;
  const originalString = '||' + empresa + '|' + cuentaOrdenante + '|||';
  try {
    const firma = await getSign(originalString, user?.name, url);
    const stpOrderConciliation = {
      empresa,
      cuentaOrdenante,
      firma,
    };
    const response = await fetch(STP_ACCOUNT_BALANCE_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(stpOrderConciliation),
    });
    const result = await response.json();
    return res.status(200).send({ result });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.conciliation, error });
  }
};

export const saveChangeState: AsyncController = async (req, res) => {
  const { id, empresa, folioOrigen, estado, causaDevolucion, tsLiquidacion } = req.body;
  if (!id || !empresa || !estado || !tsLiquidacion || !STP_CONSTANTS.typeStates.includes(estado))
    return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });

  try {
    await stpStatesChangeSchema.create({
      id,
      empresa,
      folioOrigen,
      estado,
      causaDevolucion,
      tsLiquidacion,
    });
    return res.status(200).send({ mensaje: 'recibido' });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.createRegister, error });
  }
};

export const saveChangePayment: AsyncController = async (req, res) => {
  const {
    id,
    fechaOperacion,
    institucionOrdenante,
    institucionBeneficiaria,
    claveRastreo,
    monto,
    nombreOrdenante,
    tipoCuentaOrdenante,
    cuentaOrdenante,
    rfcCurpOrdenante,
    nombreBeneficiario,
    tipoCuentaBeneficiario,
    cuentaBeneficiario,
    nombreBeneficiario2,
    tipoCuentaBeneficiario2,
    cuentaBeneficiario2,
    rfcCurpBeneficiario,
    conceptoPago,
    referenciaNumerica,
    empresa,
    tipoPago,
    tsLiquidacion,
    folioCodi,
  } = req.body;
  if (
    !id ||
    !fechaOperacion ||
    !institucionOrdenante ||
    !institucionBeneficiaria ||
    !claveRastreo ||
    !monto ||
    !nombreBeneficiario ||
    !tipoCuentaBeneficiario ||
    !cuentaBeneficiario ||
    !rfcCurpBeneficiario ||
    !conceptoPago ||
    !referenciaNumerica ||
    !empresa ||
    !tipoPago ||
    !tsLiquidacion
  )
    return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const accountsBlacklist = STP_CONSTANTS.accounts.blacklist;
  const transformDate = (fecha: number) => {
    const year = fecha.toString().slice(0, 4);
    const month = fecha.toString().slice(4, 6);
    const day = fecha.toString().slice(6, 8);
    return `${year}-${month}-${day}`;
  };

  try {
    await StpPaymentsChangeSchema.create({
      id,
      fechaOperacion: transformDate(fechaOperacion),
      institucionOrdenante,
      institucionBeneficiaria,
      claveRastreo,
      monto,
      nombreOrdenante,
      tipoCuentaOrdenante,
      cuentaOrdenante,
      rfcCurpOrdenante,
      nombreBeneficiario,
      tipoCuentaBeneficiario,
      cuentaBeneficiario,
      nombreBeneficiario2,
      tipoCuentaBeneficiario2,
      cuentaBeneficiario2,
      rfcCurpBeneficiario,
      conceptoPago,
      referenciaNumerica,
      empresa,
      tipoPago,
      tsLiquidacion,
      folioCodi,
    });
    console.log(cuentaBeneficiario, cuentaBeneficiario2);
    if (accountsBlacklist.includes(cuentaBeneficiario) || accountsBlacklist.includes(cuentaBeneficiario2))
      return res.status(400).send({ mensaje: 'devolver', id: 2 });
    return res.status(200).send({ mensaje: 'confirmar' });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.createRegister, error });
  }
};

export const createAccount: AsyncController = async (req, res) => {
  const { account } = req.body;
  if (!account) return res.status(400).send({ message: STP_CONSTANTS.errors.missingBody });
  const accountString = account.toString();

  try {
    const message = await createStpAccounts(accountString);
    return res.status(200).send({ message });
  } catch (error) {
    return res.status(500).send({ message: STP_CONSTANTS.errors.conciliation, error });
  }
};
