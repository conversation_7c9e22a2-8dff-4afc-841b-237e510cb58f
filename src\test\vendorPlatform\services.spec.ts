import Associate from '@/models/associateSchema';
import StockVehicle from '@/models/StockVehicleSchema';
import { errorCodeUtils } from '@/utils/error.utils';
import { organizationsService } from '@/vendor-platform/modules/organizations/services/organizations.service';
import { getServicesAndVendorDetailsByAssociateId } from '@/vendor-platform/modules/services/controllers/services.controller';
import { serviceClassInstance } from '@/vendor-platform/modules/services/services/service.service';
import { stockVehicleService } from '@/vendor-platform/modules/stockVehicles/services/vehicles.service';

jest.mock('@/vendor-platform/modules/services/services/service.service');
jest.mock('@/vendor-platform/modules/organizations/services/organizations.service');
jest.mock('@/vendor-platform/modules/stockVehicles/services/vehicles.service');
jest.mock('@/models/associateSchema');
jest.mock('@/models/StockVehicleSchema');

const mockRes = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  return res;
};

describe('getServicesAndVendorDetailsByAssociateId', () => {
  const req: any = { params: { associateId: 'associate1' } };
  let res: any;

  beforeEach(() => {
    res = mockRes();
    jest.clearAllMocks();
  });

  it('should return 404 if no services found', async () => {
    (serviceClassInstance.findServicesByAssociateId as jest.Mock).mockResolvedValue(null);

    await getServicesAndVendorDetailsByAssociateId(req, res);

    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.send).toHaveBeenCalledWith(errorCodeUtils.SERVICE_NOT_FOUND);
  });

  it('should return 404 if associate not found', async () => {
    (serviceClassInstance.findServicesByAssociateId as jest.Mock).mockResolvedValue([]);
    (Associate.findById as jest.Mock).mockResolvedValue(null);

    await getServicesAndVendorDetailsByAssociateId(req, res);

    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.send).toHaveBeenCalledWith(errorCodeUtils.ASSOCIATE_NOT_FOUND);
  });

  it('should return 404 if vehicle details not found', async () => {
    (serviceClassInstance.findServicesByAssociateId as jest.Mock).mockResolvedValue([]);
    (Associate.findById as jest.Mock).mockResolvedValue({ vehiclesId: ['vehicle1'] });
    (StockVehicle.findById as jest.Mock).mockResolvedValue(null);

    await getServicesAndVendorDetailsByAssociateId(req, res);

    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.send).toHaveBeenCalledWith(errorCodeUtils.VEHICLE_DETAILS_NOT_FOUND);
  });

  it('should return 200 with empty services and vehicle info if no services', async () => {
    (serviceClassInstance.findServicesByAssociateId as jest.Mock).mockResolvedValue([]);
    (Associate.findById as jest.Mock).mockResolvedValue({
      vehiclesId: ['vehicle1'],
      firstName: 'John',
      lastName: 'Doe',
      phone: '123',
    });
    (StockVehicle.findById as jest.Mock).mockResolvedValue({
      brand: 'Toyota',
      model: 'Corolla',
      _id: 'vehicle1',
    });
    (stockVehicleService.getVehicleDeliveryDate as jest.Mock).mockResolvedValue(new Date('2023-01-01'));

    await getServicesAndVendorDetailsByAssociateId(req, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.send).toHaveBeenCalledWith({
      services: [],
      vehicleDeliveryDate: new Date('2023-01-01').toISOString(),
      vehicleInfo: { brand: 'Toyota', model: 'Corolla' },
    });
  });

  it('should return 404 if no organizations found', async () => {
    (serviceClassInstance.findServicesByAssociateId as jest.Mock).mockResolvedValue([
      {
        organizationId: 'org1',
        toObject: () => ({ organizationId: 'org1' }),
      },
    ]);
    (Associate.findById as jest.Mock).mockResolvedValue({ vehiclesId: ['vehicle1'] });
    (StockVehicle.findById as jest.Mock).mockResolvedValue({
      brand: 'Toyota',
      model: 'Corolla',
      _id: 'vehicle1',
    });
    (stockVehicleService.getVehicleDeliveryDate as jest.Mock).mockResolvedValue(new Date('2023-01-01'));
    (organizationsService.getMultipleOrganizationsByIds as jest.Mock).mockResolvedValue([]);

    await getServicesAndVendorDetailsByAssociateId(req, res);

    expect(res.status).toHaveBeenCalledWith(404);
    expect(res.send).toHaveBeenCalledWith(errorCodeUtils.ORGANIZATION_NOT_FOUND);
  });

  it('should return 200 with services and organization names', async () => {
    (serviceClassInstance.findServicesByAssociateId as jest.Mock).mockResolvedValue([
      {
        organizationId: 'org1',
        toObject: () => ({ organizationId: 'org1', foo: 'bar' }),
      },
    ]);
    (Associate.findById as jest.Mock).mockResolvedValue({ vehiclesId: ['vehicle1'] });
    (StockVehicle.findById as jest.Mock).mockResolvedValue({
      brand: 'Toyota',
      model: 'Corolla',
      _id: 'vehicle1',
    });
    (stockVehicleService.getVehicleDeliveryDate as jest.Mock).mockResolvedValue(new Date('2023-01-01'));
    (organizationsService.getMultipleOrganizationsByIds as jest.Mock).mockResolvedValue([
      {
        _id: 'org1',
        name: 'OrgName',
      },
    ]);

    await getServicesAndVendorDetailsByAssociateId(req, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.send).toHaveBeenCalledWith({
      services: [{ organizationId: 'org1', foo: 'bar', organizationName: 'OrgName' }],
      vehicleDeliveryDate: new Date('2023-01-01').toISOString(),
      vehicleInfo: { brand: 'Toyota', model: 'Corolla' },
    });
  });

  it('should handle internal errors', async () => {
    (serviceClassInstance.findServicesByAssociateId as jest.Mock).mockRejectedValue(new Error('fail'));

    await getServicesAndVendorDetailsByAssociateId(req, res);

    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.send).toHaveBeenCalledWith(expect.objectContaining({ code: 'INTERNAL_SERVER_ERROR' }));
  });
});
