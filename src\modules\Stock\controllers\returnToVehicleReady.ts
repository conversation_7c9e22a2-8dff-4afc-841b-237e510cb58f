import axios from 'axios';
import { deleteFileFromS3 } from '../../../aws/s3';
import { emailUsersAllowed, genericMessages, isProd, steps, stockVehiclesText } from '../../../constants';
import StockVehicle from '../../../models/StockVehicleSchema';
import AssociatePayments from '../../../models/associatePayments';
import Associate from '../../../models/associateSchema';
import Document from '../../../models/documentSchema';
import MainContractSchema from '../../../models/mainContractSchema';
import StartPayFlow from '../../../models/start-pay-flow';
import User from '../../../models/userSchema';
import { AsyncController } from '../../../types&interfaces/types';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import { deleteWeetrustDocument } from '../../Associate/services/weetrust';

interface Body {
  howManyDeleteAssociates: number;
  deleteAll: boolean;
}

export const returnToVehicleReadyById: AsyncController = async (req, res) => {
  const { deleteAll }: Body = req.body;
  try {
    const userIdRequest = req.userId.userId;

    const user = await User.findById(userIdRequest);
    if (!user) return res.status(404).send({ message: 'Usuario no encontrado' });
    if (isProd && emailUsersAllowed.includes(user.email))
      return res.status(401).send({ message: 'No tienes permisos para realizar esta accion' });

    const vehicle = await StockVehicle.findById(req.params.vehicleId);
    if (!vehicle) return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
    const historyStepsToRemove = [
      'CONDUCTOR ASIGNADO',
      'CONTRATO GENERADO',
      'DOCUMENTOS FIRMADOS',
      'DOCUMENTO FIRMADO DEL CONDUCTOR ACTUALIZADO',
      'FOTO ACTUALIZADA DEL CONDUCTOR',
    ];
    const drivers = vehicle.drivers;
    if (deleteAll) {
      for (const driver of drivers) {
        const associate = await Associate.findById(driver);
        if (associate) {
          const mainContract = await MainContractSchema.findOne({
            stockId: vehicle._id,
            associatedId: associate._id,
          });

          if (mainContract) await mainContract.remove();

          const associatePayment = await AssociatePayments.findOne({
            vehiclesId: vehicle.id,
            associateId: associate._id,
          });
          if (associatePayment) await associatePayment.remove();
          if (associate.clientId) {
            try {
              // eslint-disable-next-line @typescript-eslint/no-use-before-define
              await deleteClient(associate.clientId);
            } catch (error: any) {
              console.log('ERROR AL DESACTIVAR CLIENTE', error.message);
            }
          }

          await associate.remove();

          await StartPayFlow.findOneAndDelete({
            associateId: associate._id,
            stockId: vehicle._id,
          });

          vehicle.drivers.pop();
          if (
            vehicle.step.stepName.toLowerCase() === steps.contractCreated.name.toLowerCase() &&
            vehicle.deliveredDate.length > 0
          ) {
            vehicle.deliveredDate.pop();
          }

          historyStepsToRemove.forEach((step) => {
            const index = vehicle.updateHistory.findLastIndexCustom((h) => h.step === step);
            if (index !== -1) {
              vehicle.updateHistory.splice(index, 1);
            }
          });
        }
      }
    } else {
      const associate = await Associate.findById(drivers[drivers.length - 1]);

      /* if (associate?.email) {
        console.log('associate email', associate.email);
        console.log('clientId', associate.clientId);
        return res.status(400).send({
          message: 'No puedes regresar un vehiculo a vehiculo listo si tiene un conductor asignado',
        });
      } */

      if (associate) {
        const mainContract = await MainContractSchema.findOne({
          stockId: vehicle._id,
          associatedId: associate._id,
        });

        if (associate.clientId) {
          try {
            console.log('deleting client', associate.clientId);
            const { data } = await axios.delete(`${PAYMENTS_API_URL}/clients/${associate.clientId}`, {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            });

            console.log('CLIENTE ELIMINADO', data);
          } catch (error: any) {
            const message = error.response.data.message || error.message;
            console.log('DELETE CLIENT ERROR', message);
            if (message.includes('cliente tiene pagos')) {
              return res.status(400).send({ message });
            }
          }
        }

        if (mainContract) await mainContract.remove();

        const associatePayment = await AssociatePayments.findOne({
          vehiclesId: vehicle.id,
          associateId: associate._id,
        });
        if (associatePayment) await associatePayment.remove();

        const unSignedContract = associate.unSignedContractDoc;

        if (unSignedContract) {
          const doc = await Document.findById(unSignedContract);
          if (doc) {
            await deleteFileFromS3(doc.path);
            await doc.remove();
          }
        }

        if (associate.clientId) {
          try {
            /* console.log('deleting client', associate.clientId);
            const { data } = await axios.delete(`${PAYMENTS_API_URL}/clients/${associate.clientId}`, {
              headers: {
                Authorization: `Bearer ${PAYMENTS_API_KEY}`,
              },
            });

            console.log('CLIENTE ELIMINADO', data); */
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            await deleteClient(associate.clientId);
          } catch (error: any) {
            const message = error.response.data.message;

            return res.status(400).send({ message: message || 'No se pudo eliminar el cliente de pagos' });
          }
        }

        vehicle.drivers.pop();
        if (
          vehicle.step.stepName.toLowerCase() === steps.contractCreated.name.toLowerCase() &&
          vehicle.deliveredDate.length > 0
        ) {
          vehicle.deliveredDate.pop();
        }

        if (associate.digitalSignature?.documentID) {
          await deleteWeetrustDocument(undefined, associate.digitalSignature.documentID);
          try {
          } catch (error: any) {
            console.log('error', error.message);
          }
        }

        if (associate.digitalSignature?.isSent) {
          associate.digitalSignature.isSent = false;
          associate.digitalSignature.documentID = '';
          await associate.save();
        }

        await associate.remove();

        /*  historyStepsToRemove.forEach((step) => {
          const index = vehicle.updateHistory.findLastIndexCustom((h) => h.step === step);
          if (index !== -1) {
            vehicle.updateHistory.splice(index, 1);
          }
        }); */
      }
    }

    vehicle.status = 'active';
    vehicle.step = {
      stepName: steps.vehicleReady.name,
      stepNumber: steps.vehicleReady.number,
    };

    console.log('VEHICULO REGRESADO A VEHICULO LISTO', vehicle.carNumber);
    await vehicle.save();

    return res.status(200).send({ message: 'Vehiculo regresado a vehiculo listo exitosamente' });
  } catch (error: any) {
    console.log('ERROR 2 BRO', error.message);

    const message =
      error.response?.data?.message || error.message || genericMessages.errors.somethingWentWrong;
    return res.status(500).send({ message });
  }
};

async function deleteClient(clientId: string) {
  try {
    await axios.delete(`${PAYMENTS_API_URL}/clients/${clientId}`, {
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    });
  } catch (error: any) {
    console.log('ERROR AL DESACTIVAR CLIENTE', error.message);
  }
}
