import z from 'zod';

export const createWorkshopDto = z.object({
  name: z.string().trim().min(3),
  organizationId: z.string().trim(),
  location: z
    .object({
      address: z.string().trim().optional(),
      city: z.string().trim().optional(),
      state: z.string().trim().optional(),
      zip: z.string().trim().optional(),
      mapsLink: z.string().trim().optional(),
    })
    .optional(),
  phone: z.string().trim().optional(),
  country: z.string().trim().optional(),
});

export type CreateWorkshopDtoType = z.infer<typeof createWorkshopDto>;
