import { Router } from 'express';
import {
  getAdendumSignaturesByAssociateId,
  // generate1yeartoken,
  getAssociateByStockPlates,
  getLastPayment,
  validateCURP,
  validateEmail,
  validateRFC,
} from '../controllers/get-associate.controller';
import associateWeetrustRouter from './digitalSignature.route';

const associateRouter = Router();

associateRouter.post('/get-by-plates', getAssociateByStockPlates);

associateRouter.post('/validate-email', validateEmail);
associateRouter.post('/validate-rfc', validateRFC);
associateRouter.post('/validate-curp', validateCURP);

associateRouter.post('/last-payment', getLastPayment);

associateRouter.get('/adendumDigitalSignature/:associateId', getAdendumSignaturesByAssociateId);

associateRouter.use('/signature', associateWeetrustRouter);

export default associateRouter;
