import app from '../app';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { accessTokenSecret, associateText, genericMessages, steps, stockVehiclesText } from '../constants';
import {
  /* createAassociateTest, */ createAdminTest,
  createAssociateTest,
  overHaulingTest,
  serviceStockTest,
  stockVehicleTest,
} from './functionts';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleSubCategory,
} from '@/models/StockVehicleSchema';
import Associate from '@/models/associateSchema';

let userAdminId: string = '';
let stockVehicleId: string = '';
let stockVehicleObjId: any;
let associateId: string = '';
let associateObjId: any;

const filePath = `${__dirname}/testFiles/test.pdf`;
const imagePath = `${__dirname}/testFiles/test.png`;

// let associateId: string = '';

let token: string = '';

beforeAll(async () => {
  const adminUser = await createAdminTest();
  const stockVehicle = await stockVehicleTest();
  const associate = await createAssociateTest();

  userAdminId = adminUser._id.toString();
  stockVehicleObjId = stockVehicle._id;
  stockVehicleId = stockVehicle._id.toString();

  associateObjId = associate._id;
  associateId = associate._id.toString();
  token = jwt.sign({ userId: userAdminId }, accessTokenSecret, {
    expiresIn: '6m',
  });
});

describe('PATCH /stock/update/:id', () => {
  let response: request.Response;

  it('Should respond with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/update/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should respond with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should respond with 400 status code if missing files in body', async () => {
    try {
      response = await request(app)
        .patch(`/stock/update/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.missingBody });
    }
  });

  it('should respond with 200 status code if body exists and docs are complete', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        carPlates: { plates: 'dsdad' },
        circulationCard: { validity: '2024-10-23' },
        gpsInstalled: true,
        tenancy: [
          {
            _id: stockVehicleObjId,
            payment: 123213,
            validity: '2024-10-23',
            tenancyDocument: stockVehicleObjId,
          },
        ],
        policiesArray: [
          {
            _id: stockVehicleObjId,
            policyNumber: 123213,
            insurer: 'Toyota',
            validity: '2024-10-23',
            broker: 'avv-sc',
            policyDocument: stockVehicleObjId,
          },
        ],
      });
      response = await request(app)
        .patch(`/stock/update/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({ stepName: 'Vehiculo listo', receptionDate: '2025-01-24' });

      expect(response.status).toBe(200);
      expect(response.body.message).toEqual('Vehiculo enviado a stock');
      expect(response.body.vehicle.vehicleStatus).toEqual(UpdatedVehicleStatus.inactive);
      expect(response.body.vehicle.category).toEqual(VehicleCategory.stock);
      expect(response.body.vehicle.subCategory).toEqual(VehicleSubCategory.default);
      expect(response.body.vehicle.step.stepName).toEqual(steps.vehicleReady.name);
      expect(response.body.vehicle.step.stepNumber).toEqual(steps.vehicleReady.number);
      expect(response.body.vehicle.receptionDate).toEqual('2025-01-24');
    } catch (error) {
      console.log('Failure in the 200 send-to-stock', response.body);
      throw new Error('Error sending to stock');
    }
  });
});

describe('PATCH /stock/update/description/:vehicleId', () => {
  let response: request.Response;
  it('should respond with 200 status code and send to stock if vehicle has all docs complete and existing stepNumber is 1', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        step: {
          stepName: steps.stock.name,
          stepNumber: steps.stock.number,
        },
        carPlates: { plates: 'dsdad' },
        circulationCard: { validity: '2024-10-23' },
        gpsInstalled: true,
        tenancy: [
          {
            _id: stockVehicleObjId,
            payment: 123213,
            validity: '2024-10-23',
            tenancyDocument: stockVehicleObjId,
          },
        ],
        policiesArray: [
          {
            _id: stockVehicleObjId,
            policyNumber: 123213,
            insurer: 'Toyota',
            validity: '2024-10-23',
            broker: 'avv-sc',
            policyDocument: stockVehicleObjId,
          },
        ],
      });
      response = await request(app)
        .patch(`/stock/update/description/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          receptionDate: '2025-01-24',
          historyData: {
            userId: associateId,
            step: 'Stock',
          },
        });
      let responseVehicle = await StockVehicle.findById(stockVehicleObjId);

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: stockVehiclesText.success.vehicleUpdated });
      expect(responseVehicle?.vehicleStatus).toEqual(UpdatedVehicleStatus.inactive);
      expect(responseVehicle?.category).toEqual(VehicleCategory.stock);
      expect(responseVehicle?.subCategory).toEqual(VehicleSubCategory.default);
      expect(responseVehicle?.step.stepName).toEqual(steps.vehicleReady.name);
      expect(responseVehicle?.step.stepNumber).toEqual(steps.vehicleReady.number);
      expect(responseVehicle?.receptionDate).toEqual('2025-01-24');
    } catch (error) {
      console.log('Failure in the 200 send-to-stock', response.body);
      throw new Error('Error sending to stock');
    }
  });
});

describe('PATCH /stock/sendOverHauling/:vehicleId', () => {
  let response: request.Response;

  it('Should respond with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/sendOverHauling/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should respond with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/sendOverHauling/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateIn', '2024-12-12')
        .field('comments', 'Test comments')
        .field('subCategory', VehicleSubCategory.management)
        .attach('quotationDoc', filePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should respond with 400 status code if missing files in body', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { category: VehicleCategory.stock });
      response = await request(app)
        .patch(`/stock/sendOverHauling/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateIn', '2024-12-12')
        .field('comments', 'Test comments')
        .field('subCategory', VehicleSubCategory.management);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Se requieren 5 imagenes' });
    }
  });

  it('should respond with 400 if category is not stock', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { category: VehicleCategory.revision });

      response = await request(app)
        .patch(`/stock/sendOverHauling/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateIn', '2024-12-12')
        .field('comments', 'Test comments')
        .field('subCategory', 'management')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'No se puede realizar este proceso en el estatus actual' });
    }
  });

  it('should respond with 200 and send to overhauling when params are complete', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { category: VehicleCategory.stock });

      response = await request(app)
        .patch(`/stock/sendOverHauling/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateIn', '2024-12-12')
        .field('comments', 'Test comments')
        .field('subCategory', VehicleSubCategory.management)
        .attach('quotationDoc', filePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath)
        .attach('inImgs', imagePath);

      expect(response.status).toBe(200);
      expect(response.body.message).toEqual(stockVehiclesText.success.vehicleUpdated);
    } catch (error) {
      console.log('Failure in the 200 sendOverHauling', response.body);
      throw new Error('Error sending to overhauling');
    }
  });
});

describe('PATCH /stock/finishOverHauling/:vehicleId', () => {
  let response: request.Response;

  it('Should respond with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/finishOverHauling/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should respond with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/finishOverHauling/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateOut', '2024-12-12')
        .field('comments', 'Test comments')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should respond with a 400 status code if dateOut is not sent', async () => {
    try {
      response = await request(app)
        .patch(`/stock/finishOverHauling/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('comments', 'ddsad');
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'La fecha de salida es requerida' });
    }
  });

  it('should respond with 400 if category is not revision', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { category: VehicleCategory.stock });
      response = await request(app)
        .patch(`/stock/finishOverHauling/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateOut', '2024-12-12')
        .field('comments', 'Test comments')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'No se puede realizar este proceso en el estatus actual' });
    }
  });

  it('should respond with 404 status code if Overhauling document not found for provided vehicle id', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { category: VehicleCategory.revision });

      response = await request(app)
        .patch(`/stock/finishOverHauling/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateOut', '2024-12-12')
        .field('comments', 'Test comments')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: 'No se ha encontrado el registro de revisión' });
    }
  });

  it('should respond with 200 and finish overhauling when params are complete', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { category: VehicleCategory.revision });
      await overHaulingTest(stockVehicleObjId);

      response = await request(app)
        .patch(`/stock/finishOverHauling/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('dateOut', '2024-12-12')
        .field('comments', 'Test comments')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);

      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: stockVehiclesText.success.vehicleUpdated });
    } catch (error) {
      console.log('Failure in the 200 finishOverHauling', response.body);
      throw new Error('Error finishig overhauling');
    }
  });
});

describe('PATCH /stock/changeStatus-processes/:vehicleId', () => {
  let response: request.Response;

  it('Should respond with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/changeStatus-processes/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should respond with a 400 if vehicle status not in body', async () => {
    try {
      response = await request(app)
        .patch(`/stock/changeStatus-processes/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Status es requerido' });
    }
  });

  it('should respond with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/changeStatus-processes/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          status: 'workshop',
        });
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should respond with a 404 if driver not found', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { drivers: [] });
      response = await request(app)
        .patch(`/stock/changeStatus-processes/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          status: 'workshop',
        });
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: associateText.errors.associateNotFound });
    }
  });

  it('should respond with a 400 if either dateIn or dateOut is not in body', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { drivers: [{ _id: associateId }] });
      response = await request(app)
        .patch(`/stock/changeStatus-processes/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          status: 'workshop',
          category: 'workshop',
          subCategory: 'aesthetic-repair',
        });
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Campos faltantes' });
    }
  });

  it('should respond with a 400 if "date" not in body', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { drivers: [{ _id: associateId }] });
      response = await request(app)
        .patch(`/stock/changeStatus-processes/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          status: 'legal',
          category: 'legal',
          subCategory: 'demand',
        });
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Campos faltantes' });
    }
  });

  it('should respond with a 200 if params are complete', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { drivers: [{ _id: associateId }] });
      response = await request(app)
        .patch(`/stock/changeStatus-processes/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          status: 'legal-process',
          category: 'legal',
          subCategory: 'demand',
          date: '2024-12-12',
        });

      expect(response.status).toBe(200);
      expect(response.body.message).toEqual(stockVehiclesText.success.vehicleUpdated);
      expect(response.body.stockVehicle.vehicleStatus).toEqual(UpdatedVehicleStatus.inactive);
      expect(response.body.stockVehicle.category).toEqual(VehicleCategory.legal);
      expect(response.body.stockVehicle.subCategory).toEqual(VehicleSubCategory.demand);
    } catch (error) {
      console.log('Failure in cambiar status', response.body);
      throw new Error('Error in cambiar status');
    }
  });
});

describe('PATCH /stock/changeStatus-finish-process/:vehicleId', () => {
  let response: request.Response;

  it('Should respond with 401 status code if token not provied', async () => {
    try {
      response = await request(app).patch(`/stock/changeStatus-finish-process/${stockVehicleId}`);
    } catch (error) {
      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        message: 'No autorizado',
        data: null,
        error: {
          code: 'unauthorized',
          errors: {},
        },
        pagination: null,
        success: false,
      });
    }
  });

  it('should respond with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', true);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should respond with a 404 if driver not found', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { drivers: [] });
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', true);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: associateText.errors.associateNotFound });
    }
  });

  it('should respond with a 400 if dateFinished not in body', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
        category: VehicleCategory.workshop,
      });
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', false)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Campos faltantes' });
    }
  });

  it('should respond with a 400 if files not in body', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
        category: VehicleCategory.workshop,
      });
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', false)
        .field('dateFinished', '2024-10-12');
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'No se han subido los archivos requeridos' });
    }
  });

  it('should respond with a 400 if serviceStock document doesnt exist', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
        category: VehicleCategory.workshop,
      });
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', false)
        .field('dateFinished', '2024-10-12')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'No existe un proceso anterior' });
    }
  });

  it('should respond with a 500 if adendumServiceCount doesnt exist', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
        category: VehicleCategory.workshop,
      });
      await serviceStockTest(stockVehicleObjId, associateObjId);
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', false)
        .field('dateFinished', '2024-10-12')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(500);
      expect(response.body).toEqual({ message: genericMessages.errors.somethingWentWrong });
    }
  });

  it('should respond with a 200 if all conditions met', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
        category: VehicleCategory.workshop,
        adendumServiceCount: 3,
      });
      await serviceStockTest(stockVehicleObjId, associateObjId);
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', false)
        .field('dateFinished', '2024-10-12')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);

      expect(response.status).toBe(200);
      expect(response.body.message).toEqual(stockVehiclesText.success.vehicleUpdated);
      expect(response.body.vehicle.vehicleStatus).toEqual(UpdatedVehicleStatus.active);
      expect(response.body.vehicle.category).toEqual(VehicleCategory.default);
      expect(response.body.vehicle.subCategory).toEqual(VehicleSubCategory.default);
    } catch (error) {
      console.log('Failure in the 200 return to active', response.body);
      throw new Error('Error returning to active');
    }
  });

  it('should respond with a 500 if subscription doesnt exist', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
        category: VehicleCategory.workshop,
        adendumServiceCount: 3,
      });
      await serviceStockTest(stockVehicleObjId, associateObjId);
      response = await request(app)
        .patch(`/stock/changeStatus-finish-process/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('cancelStatus', true)
        .field('dateFinished', '2024-10-12')
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath)
        .attach('files', imagePath);
    } catch (error) {
      expect(response.status).toBe(500);
      expect(response.body).toEqual({ message: genericMessages.errors.somethingWentWrong });
    }
  });
});

describe('PATCH /stock/sendReadmission/:id', () => {
  let response: request.Response;

  it('should respond with a 404 if stock vehicle not found', async () => {
    try {
      response = await request(app)
        .patch(`/stock/sendReadmission/6515aa68541c4f787f58d0b3`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.vehicleNotFound });
    }
  });

  it('should respond with a 404 if driver not found', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, { drivers: [] });
      response = await request(app)
        .patch(`/stock/sendReadmission/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({
        message: 'No se puede reasignar debido a que no existe un conductor en este vehiculo',
      });
    }
  });

  it('should respond with a 500 if body missing params', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
      });
      response = await request(app)
        .patch(`/stock/sendReadmission/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          readmissionDate: '2024-10-12',
        });
    } catch (error) {
      expect(response.status).toBe(500);
      expect(response.body).toEqual({ message: 'Hubo un error', error });
    }
  });

  it('should respond with a 500 if body has wrong params', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
      });
      response = await request(app)
        .patch(`/stock/sendReadmission/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          readmissionDate: '2024-10-12',
          readmissionReason: 'anyyyy',
        });
    } catch (error) {
      expect(response.status).toBe(500);
      expect(response.body).toEqual({ message: 'Hubo un error', error });
    }
  });

  it('should respond with a 500 if subscription not found', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
      });
      await Associate.findOneAndUpdate(associateObjId, {
        clientId: 'random',
      });
      response = await request(app)
        .patch(`/stock/sendReadmission/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          readmissionDate: '2024-10-12',
          readmissionReason: 'no-payment',
        });
    } catch (error) {
      expect(response.status).toBe(500);
      expect(response.body).toEqual({ message: 'Hubo un error', error });
    }
  });

  it('should respond with a 200 if all conditions are met', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        drivers: [{ _id: associateId }],
      });
      response = await request(app)
        .patch(`/stock/sendReadmission/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          readmissionDate: '2024-10-12',
          readmissionReason: 'no-payment',
        });
      expect(response.status).toBe(200);
      expect(response.body.message).toEqual('Vehiculo enviado a reingresos');
    } catch (error) {
      expect(response.status).toBe(404);
    }
  });
});

describe('PATCH /stock/returnToStock/:id', () => {
  let response: request.Response;

  it('should respond with a 404 if "km" missing in body', async () => {
    try {
      //   await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
      //     drivers: [{ _id: associateId }],
      //   });
      response = await request(app)
        .patch(`/stock/returnToStock/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({});
    } catch (error) {
      expect(response.status).toBe(404);
      expect(response.body).toEqual({ message: 'Kilometros requeridos' });
    }
  });
});

describe('PATCH /stock/sendDischarged/:id', () => {
  let response: request.Response;

  it('should respond with a 400 if reason or date missing in body', async () => {
    try {
      response = await request(app)
        .patch(`/stock/sendDischarged/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('reason', 'Robo');
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: stockVehiclesText.errors.dischargedMissing });
    }
  });

  it('should respond with a 200 if all conditions complete', async () => {
    try {
      response = await request(app)
        .patch(`/stock/sendDischarged/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .field('reason', 'Robo')
        .field('date', '2024-10-12')
        .field('subCategory', 'total-loss');

      expect(response.status).toBe(200);
      expect(response.body.message).toEqual(stockVehiclesText.success.dischargedVehicle);
      expect(response.body.stockVehicle.vehicleStatus).toEqual(UpdatedVehicleStatus.inactive);
      expect(response.body.stockVehicle.category).toEqual(VehicleCategory.withdrawn);
      expect(response.body.stockVehicle.subCategory).toEqual(VehicleSubCategory['total-loss']);
      expect(response.body.stockVehicle.dischargedData.reason).toEqual('Robo');
      expect(response.body.stockVehicle.dischargedData.date).toEqual('2024-10-12');
    } catch (error) {
      console.log('Failure in the 200 sendDischarged', response.body);
      throw new Error('Error in sendDischarged');
    }
  });
});

describe('PUT /stock/flows/return-to/:stockId', () => {
  let response: request.Response;

  it('should respond with a 400 if stepName missing in body', async () => {
    try {
      response = await request(app)
        .put(`/stock/flows/return-to/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`);
    } catch (error) {
      expect(response.status).toBe(400);
      expect(response.body).toEqual({ message: 'Step name is required' });
    }
  });

  it('should respond with a 500 if stockVehicle in wrong step', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        step: { stepName: 'Conductor asignado', stepNumber: 3 },
        drivers: [{ _id: associateId }],
      });
      response = await request(app)
        .put(`/stock/flows/return-to/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          stepName: 'Conductor asignado',
        });
    } catch (error) {
      expect(response.status).toBe(500);
    }
  });

  it('should respond with a 200 if stockVehicle in correct step Conductor asignado', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        step: { stepName: 'Contrato generado', stepNumber: 4 },
        drivers: [{ _id: associateId }],
      });
      response = await request(app)
        .put(`/stock/flows/return-to/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          stepName: 'Conductor asignado',
        });
      expect(response.status).toBe(200);
      expect(response.body).toEqual({ message: 'Vehiculo regresado a Conductor asignado' });
    } catch (error) {
      console.log('Failure in the return flow function', response.body);
      throw new Error('Error in return flow function');
    }
  });
  it('should respond with a 200 if stockVehicle in correct step and sent to Vehiculo listo', async () => {
    try {
      await StockVehicle.findOneAndUpdate(stockVehicleObjId, {
        step: { stepName: 'Entregado', stepNumber: 5 },
        drivers: [{ _id: associateId }],
      });
      response = await request(app)
        .put(`/stock/flows/return-to/${stockVehicleId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          stepName: 'Vehiculo listo',
        });
      expect(response.status).toBe(200);
      expect(response.body).toEqual({});
    } catch (error) {
      console.log('Failure in the return flow function', response.body);
      throw new Error('Error in return flow function');
    }
  });
});
