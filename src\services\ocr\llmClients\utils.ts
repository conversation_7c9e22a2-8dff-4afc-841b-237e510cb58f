import { AllowedMimeType } from './geminiClient';

// Helper function to determine MIME type from filename
export const getMimeTypeFromFilename = (filename: string): AllowedMimeType => {
  if (!filename) {
    throw new Error('Filename is required to determine MIME type');
  }

  // Extract extension (handle files with multiple dots correctly)
  const extension = filename.split('.').pop()?.toLowerCase();

  if (!extension) {
    throw new Error('Unable to determine file extension');
  }

  // Map extensions to MIME types - Add PDF case
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'pdf':
      return 'application/pdf';
    default:
      throw new Error(
        `Unsupported file extension: ${extension}. Supported types are jpg, jpeg, png, gif, webp, and pdf`
      );
  }
};
