import mongoose, { Document, Schema } from 'mongoose';

// Define the StepType schema
const StepTypeSchema = new Schema({
  status: {
    type: String,
    enum: ['accepted', 'pending'], // Only allow these values
    required: true,
  },
  timeStamp: { type: Date, required: true },
});

// Define the CustomerAcknowledgement schema
const CustomerAcknowledgementSchema = new Schema({
  associateId: { type: Schema.Types.ObjectId, ref: 'associates', required: true }, // Reference to admissionRequests
  stockVehicleId: { type: Schema.Types.ObjectId, ref: 'stockvehicles', required: true }, // Reference to stockvehicles
  vehicleMake: { type: String, required: true },
  vehicleModel: { type: String, required: true },
  fullName: { type: String, required: true },
  readBenfitsOffered: { type: StepTypeSchema, required: true },
  readInsuranceCoverage: { type: StepTypeSchema, required: true },
  readMaintenance: { type: StepTypeSchema, required: true },
  readTermConditions: { type: StepTypeSchema, required: true },
  readTheft: { type: StepTypeSchema, required: true },
  readAccident: { type: StepTypeSchema, required: true },
  readPayments: { type: StepTypeSchema, required: true },
  pointsAcknowledement: { type: StepTypeSchema, required: true },
});

// Define the CustomerAcknowledgement document interface
export interface ICustomerAcknowledgement extends Document {
  associateId: mongoose.Types.ObjectId;
  stockVehicleId: mongoose.Types.ObjectId;
  vehicleMake: string;
  vehicleModel: string;
  fullName: string;
  readBenfitsOffered: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
  readInsuranceCoverage: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
  readMaintenance: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
  readTermConditions: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
  readTheft: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
  readAccident: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
  readPayments: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
  pointsAcknowledement: {
    status: 'accepted' | 'pending'; // Updated type
    timeStamp: Date;
  };
}

// Create the Mongoose model
const CustomerAcknowledgement = mongoose.model<ICustomerAcknowledgement>(
  'CustomerAcknowledgement',
  CustomerAcknowledgementSchema
);

export default CustomerAcknowledgement;
