import { emailUsersAllowed, genericMessages, isProd, steps, stockVehiclesText } from '../../../constants';
import AssociatePayments from '../../../models/associatePayments';
import MainContractSchema from '../../../models/mainContractSchema';
import Associate from '../../../models/associateSchema';
import StockVehicle, { VehicleStatus } from '../../../models/StockVehicleSchema';
import User from '../../../models/userSchema';
import { MyRequest } from '../../../types&interfaces/interfaces';
import axios from 'axios';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import StartPayFlow from '../../../models/start-pay-flow';
import Document from '../../../models/documentSchema';
import { deleteFileFromS3 } from '../../../aws/s3';
import { deleteWeetrustDocument } from '../../Associate/services/weetrust';
import { AdmissionRequestMongo } from '@/models/admissionRequestSchema';
import { logger } from '@/clean/lib/logger';

const updateAdmissionRequest = async (associateId: any, associateEmail: string) => {
  try {
    logger.info(
      `[updateAdmissionRequest] Starting to find and update admission request for Associate ID: ${associateId}, Email: ${associateEmail}`
    );

    const result = await AdmissionRequestMongo.findOneAndUpdate(
      {
        $or: [
          { associateId: associateId },
          {
            'personalData.email': { $regex: `^${associateEmail}$`, $options: 'i' },
            convertedToAssociate: true,
          },
        ],
      },
      {
        $set: {
          convertedToAssociate: false,
        },
        $unset: { associateId: 1 },
      },
      { new: true }
    ).lean();

    if (result) {
      logger.info(
        `[updateAdmissionRequest] Successfully updated admission request for Associate ID: ${associateId}`
      );
    } else {
      logger.warn(
        `[updateAdmissionRequest] No admission request found for Associate ID: ${associateId}, Email: ${associateEmail}`
      );
    }
  } catch (error: any) {
    logger.error('[updateAdmissionRequest] Error updating Admission Request:', error);
    throw new Error(error.message);
  }
};

async function deleteClient(clientId: string) {
  try {
    logger.info(`[deleteClient] Deleting client with ID: ${clientId}`);
    await axios.delete(`${PAYMENTS_API_URL}/clients/${clientId}`, {
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    });
    logger.info(`[deleteClient] Successfully deleted client: ${clientId}`);
  } catch (error: any) {
    logger.error(`[deleteClient] Error deleting client ${clientId}:`, error.message);

    const message = error.response.data.message;
    throw new Error(message || 'No se pudo eliminar el cliente de pagos');
  }
}

export async function returnToVehicleReady(stockId: string, req: MyRequest) {
  try {
    logger.info(`[returnToVehicleReady] Processing returnToVehicleReady for Stock ID: ${stockId}`);
    const { deleteAll } = req.body;

    const userIdRequest = req.userId.userId;

    const user = await User.findById(userIdRequest);
    if (!user) throw new Error('Usuario no encontrado');
    if (isProd && emailUsersAllowed.includes(user.email))
      throw new Error('No tienes permisos para realizar esta accion');

    const vehicle = await StockVehicle.findById(stockId);
    if (!vehicle) {
      logger.info(`[returnToVehicleReady] Vehicle found with ID: ${stockId}`);
      throw new Error(stockVehiclesText.errors.vehicleNotFound);
    }
    const historyStepsToRemove = [
      'CONDUCTOR ASIGNADO',
      'CONTRATO GENERADO',
      'DOCUMENTOS FIRMADOS',
      'DOCUMENTO FIRMADO DEL CONDUCTOR ACTUALIZADO',
      'FOTO ACTUALIZADA DEL CONDUCTOR',
    ];
    const drivers = vehicle.drivers;

    const extensionCarNumber = vehicle.extensionCarNumber;
    const contractNumber = extensionCarNumber
      ? `${vehicle.carNumber}-${extensionCarNumber}`
      : vehicle.carNumber;

    if (deleteAll) {
      for (const driver of drivers) {
        const associate = await Associate.findById(driver);
        if (associate) {
          logger.info(`[returnToVehicleReady] Removing associate ID: ${associate._id}`);
          const mainContract = await MainContractSchema.findOne({
            $or: [
              {
                stockId: vehicle._id,
                associatedId: associate._id,
              },
              {
                contractNumber,
                stockId: vehicle._id,
              },
            ],
          });

          if (mainContract) {
            await mainContract.remove();
          }

          const associatePayment = await AssociatePayments.findOne({
            $or: [
              {
                associateId: associate._id,
                vehiclesId: vehicle._id,
              },
              {
                monexEmail: associate.email,
                vehiclesId: vehicle._id,
              },
              {
                monexEmail: associate.email,
              },
            ],
          });
          if (associatePayment) {
            await associatePayment.remove();
          }
          if (associate.clientId) {
            try {
              const { data } = await axios.delete(`${PAYMENTS_API_URL}/clients/${associate.clientId}`, {
                headers: {
                  Authorization: `Bearer ${PAYMENTS_API_KEY}`,
                },
              });

              logger.info('CLIENT DELETED SUCCESSFULLY ON RETURN TO VEHICLE READY', data);
            } catch (error: any) {
              logger.error('ERROR DELETING CLIENT ON RETURN TO VEHICLE READY', error.message);
            }
          }

          await updateAdmissionRequest(associate._id, associate.email);

          await associate.remove();

          await StartPayFlow.findOneAndDelete({
            associateId: associate._id,
            stockId: vehicle._id,
          });

          vehicle.drivers.pop();
          if (
            vehicle.step.stepName.toLowerCase() === steps.contractCreated.name.toLowerCase() &&
            vehicle.deliveredDate.length > 0
          ) {
            vehicle.deliveredDate.pop();
          }

          historyStepsToRemove.forEach((step) => {
            const index = vehicle.updateHistory.findLastIndexCustom((h) => h.step === step);
            if (index !== -1) {
              vehicle.updateHistory.splice(index, 1);
            }
          });
        }
      }
    } else {
      const associate = await Associate.findById(drivers[drivers.length - 1]);

      if (associate) {
        logger.info(`Removing associate ID: ${associate._id}`);
        const mainContract = await MainContractSchema.findOne({
          $or: [
            {
              stockId: vehicle._id,
              associatedId: associate._id,
            },
            {
              contractNumber,
              stockId: vehicle._id,
            },
          ],
        });

        if (associate.clientId) {
          await deleteClient(associate.clientId);
        }

        if (mainContract) {
          await mainContract.remove();
        }

        const associatePayment = await AssociatePayments.findOne({
          $or: [
            {
              associateId: associate._id,
              vehiclesId: vehicle._id,
            },
            {
              monexEmail: associate.email,
              vehiclesId: vehicle._id,
            },
            {
              monexEmail: associate.email,
            },
          ],
        });
        if (associatePayment) {
          await associatePayment.remove();
        }

        const unSignedContract = associate.unSignedContractDoc;

        if (unSignedContract) {
          const doc = await Document.findById(unSignedContract);
          if (doc) {
            await deleteFileFromS3(doc.path);
            await doc.remove();
          }
        }

        vehicle.drivers.pop();
        if (
          vehicle.step.stepName.toLowerCase() === steps.contractCreated.name.toLowerCase() &&
          vehicle.deliveredDate.length > 0
        ) {
          vehicle.deliveredDate.pop();
        }

        if (associate.digitalSignature?.documentID) {
          await deleteWeetrustDocument(undefined, associate.digitalSignature.documentID);
          try {
          } catch (error: any) {
            console.log('error', error.message);
          }
        }

        await updateAdmissionRequest(associate._id, associate.email);

        await associate.remove();
      }
    }

    vehicle.status = VehicleStatus.stock;
    vehicle.step = {
      stepName: steps.vehicleReady.name,
      stepNumber: steps.vehicleReady.number,
    };

    await vehicle.save();
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || genericMessages.errors.somethingWentWrong;
    throw new Error(message);
  }
}
