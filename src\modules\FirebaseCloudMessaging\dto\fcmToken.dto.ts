import z from 'zod';
import { FCMNotificationUserType } from '../common/enums';

export const fcmTokenDto = z.object({
  fcmToken: z.string(),
  userId: z.string(),
  deviceDetails: z.record(z.string(), z.any()),
  userType: z.enum([FCMNotificationUserType.ASSOCIATE, FCMNotificationUserType.ADMISSION_REQUEST]),
});

export const fcmTokenStatusUpdateDto = z.object({
  fcmToken: z.string(),
  userId: z.string(),
  isActive: z.boolean(),
});

export const fcmNotificationPayloadDto = z.object({
  userId: z.string(),
  payload: z.object({
    title: z.string(),
    body: z.string(),
    data: z.record(z.string(), z.any()).optional(),
  }),
});

export const fcmNotificationUnregisterDto = z.object({
  userId: z.string(),
  fcmToken: z.string(),
});

export const fcmUpdateNotificationTokenDto = z.object({
  userId: z.string(),
  oldToken: z.string(),
  newToken: z.string(),
});
