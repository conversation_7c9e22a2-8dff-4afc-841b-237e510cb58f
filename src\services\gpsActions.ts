import axios from 'axios';
import {
  GPS_API_TOKEN,
  GPS_API_URL,
  gpsText,
  GPS_API_PASSWORD,
  GPS_API_USERNAME,
  GPS_API_EVENTS,
} from '../constants';
import GpsSchema from '../models/gpsSchema';
import StockVehicle from '../models/StockVehicleSchema';
import { logger } from '@/clean/lib/logger';

export async function gpsActionAndStock(props: {
  gps: string;
  comando: string;
  user: object;
  historyData: string;
}) {
  const { gps, comando, user, historyData } = props;
  try {
    const vehicle = await StockVehicle.findOne({ gpsNumber: gps });
    if (!vehicle) {
      throw new Error(gpsText.errors.gpsNotFound);
    }
    const { name } = user as { name: string }; // Add this line to extract the 'name' property from the 'user' object;
    const response = await axios.get(`${GPS_API_URL}/${gps}`, {
      headers: { authorizationToken: GPS_API_TOKEN },
      params: { comando },
    });
    if (response.status !== 200) throw new Error(response.data);
    const message = response.data;
    const newGps = new GpsSchema({ gps, comando, responseGpsAPI: message, ocnUserName: name }); // Use the extracted 'name' property
    await newGps.save();
    await StockVehicle.findOneAndUpdate(
      { gpsNumber: gps },
      { isBlocked: comando === 'bloqueo', $push: { updateHistory: historyData } }
    );
    return message;
  } catch (error: any) {
    console.error({ error });
    throw new Error(error);
  }
}

export const getGPSToken = async () => {
  const response = await axios.post(
    GPS_API_TOKEN,
    {
      body: {
        user: GPS_API_USERNAME,
        password: GPS_API_PASSWORD,
      },
    },
    { headers: { 'Content-Type': 'application/json' } }
  );
  return JSON.parse(response.data.body).token;
};

export async function gpsStatus(gps: string, report: string) {
  try {
    const vehicle = await StockVehicle.findOne({ gpsNumber: gps });
    if (!vehicle) {
      throw new Error(gpsText.errors.gpsNotFound);
    }

    const headers = { 'Content-Type': 'application/json', Authorization: await getGPSToken() };

    const data = {
      body: {
        reportType: report,
        name: gps,
      },
    };

    const response = await axios.post(GPS_API_URL, data, { headers });
    const message = response.data;
    return JSON.parse(message.body);
  } catch (error: any) {
    logger.info(`[gpsStatus], geting the status of: ${gps},  ${error}`);
    throw new Error(error);
  }
}

export async function setEvents(gps: string, event: string) {
  try {
    const headers = { 'Content-Type': 'application/json', Authorization: await getGPSToken() };
    const body = { name: gps, command: event };

    const response = await axios.post(GPS_API_EVENTS, body, { headers });
    const message = response.data;
    const newGps = new GpsSchema({ gps, comando: event, responseGpsAPI: message, ocnUserName: 'openCX' });
    await newGps.save();

    return message;
  } catch (error: any) {
    console.error(error);
    logger.info(`[setEvents], setting events of: ${gps},  ${error}`);
    throw new Error(error);
  }
}
