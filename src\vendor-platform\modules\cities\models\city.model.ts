import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';
import { CompanyVendorModel } from '../../company/models/company.model';

export interface ICity extends Document {
  companyId: mongoose.Types.ObjectId;
  name: string;
  state: string;
  country: string;
  // postalCode: string;
}

const CitySchema = new Schema(
  {
    companyId: { type: Schema.Types.ObjectId, ref: CompanyVendorModel.modelName, required: true },
    name: { type: String, required: true },
    state: { type: String, required: true },
    country: { type: String, default: 'México' },
    // postalCode: { type: String },
    active: { type: Boolean, default: true },
    timezone: { type: String },
  },
  { timestamps: true }
);

export const City = vendorDB.model<ICity>('City', CitySchema);
