import { Request, Response } from 'express';
import { Types } from 'mongoose';
import { UserRolesPermissionsService } from '../services/user-roles-permissions.service';
import { GlobalUserRole, UserType } from '../models/user-roles-permissions.model';

const userRolesPermissionsService = new UserRolesPermissionsService();

/**
 * Crear permisos para un usuario
 */
export const createUserPermissions = async (req: Request, res: Response) => {
  try {
    const { userId, userType, organizationId, roles } = req.body;
    const createdBy = new Types.ObjectId(req.userVendor!.userId);
    
    // Validaciones
    if (!userId || !userType || !roles) {
      return res.status(400).json({
        message: 'userId, userType y roles son requeridos',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }
    
    if (userType === 'organization' && !organizationId) {
      return res.status(400).json({
        message: 'organizationId es requerido para usuarios tipo organization',
        code: 'ORGANIZATION_ID_REQUIRED'
      });
    }
    
    // Verificar si ya existen permisos para este usuario
    const existingPermissions = await userRolesPermissionsService.getUserPermissions(
      new Types.ObjectId(userId)
    );
    
    if (existingPermissions) {
      return res.status(409).json({
        message: 'El usuario ya tiene permisos configurados',
        code: 'PERMISSIONS_ALREADY_EXIST'
      });
    }
    
    const userPermissions = await userRolesPermissionsService.createUserPermissions({
      userId: new Types.ObjectId(userId),
      userType: userType as UserType,
      organizationId: organizationId ? new Types.ObjectId(organizationId) : undefined,
      roles: roles as GlobalUserRole[],
      createdBy
    });
    
    res.status(201).json({
      message: 'Permisos de usuario creados exitosamente',
      data: userPermissions
    });
  } catch (error) {
    console.error('Error creating user permissions:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Obtener permisos de un usuario
 */
export const getUserPermissions = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({
        message: 'userId es requerido',
        code: 'USER_ID_REQUIRED'
      });
    }
    
    const userPermissions = await userRolesPermissionsService.getUserPermissions(
      new Types.ObjectId(userId)
    );
    
    if (!userPermissions) {
      return res.status(404).json({
        message: 'Permisos de usuario no encontrados',
        code: 'USER_PERMISSIONS_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      message: 'Permisos de usuario obtenidos exitosamente',
      data: userPermissions
    });
  } catch (error) {
    console.error('Error getting user permissions:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Obtener permisos del usuario actual
 */
export const getMyPermissions = async (req: Request, res: Response) => {
  try {
    const userId = req.userVendor!.userId;
    
    const userPermissions = await userRolesPermissionsService.getUserPermissions(
      new Types.ObjectId(userId)
    );
    
    if (!userPermissions) {
      return res.status(404).json({
        message: 'Permisos de usuario no encontrados',
        code: 'USER_PERMISSIONS_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      message: 'Permisos obtenidos exitosamente',
      data: userPermissions
    });
  } catch (error) {
    console.error('Error getting my permissions:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Actualizar permisos de un usuario
 */
export const updateUserPermissions = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;
    const updatedBy = new Types.ObjectId(req.userVendor!.userId);
    
    if (!userId) {
      return res.status(400).json({
        message: 'userId es requerido',
        code: 'USER_ID_REQUIRED'
      });
    }
    
    const userPermissions = await userRolesPermissionsService.updateUserPermissions(
      new Types.ObjectId(userId),
      updateData,
      updatedBy
    );
    
    if (!userPermissions) {
      return res.status(404).json({
        message: 'Permisos de usuario no encontrados',
        code: 'USER_PERMISSIONS_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      message: 'Permisos de usuario actualizados exitosamente',
      data: userPermissions
    });
  } catch (error) {
    console.error('Error updating user permissions:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Listar usuarios por tipo
 */
export const getUsersByType = async (req: Request, res: Response) => {
  try {
    const { userType } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    if (!userType) {
      return res.status(400).json({
        message: 'userType es requerido',
        code: 'USER_TYPE_REQUIRED'
      });
    }
    
    const users = await userRolesPermissionsService.getUsersByType(userType as UserType);
    
    // Paginación simple
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedUsers = users.slice(startIndex, endIndex);
    
    res.status(200).json({
      message: 'Usuarios obtenidos exitosamente',
      data: paginatedUsers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: users.length,
        totalPages: Math.ceil(users.length / Number(limit))
      }
    });
  } catch (error) {
    console.error('Error getting users by type:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Listar usuarios por organización
 */
export const getUsersByOrganization = async (req: Request, res: Response) => {
  try {
    const { organizationId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    if (!organizationId) {
      return res.status(400).json({
        message: 'organizationId es requerido',
        code: 'ORGANIZATION_ID_REQUIRED'
      });
    }
    
    const users = await userRolesPermissionsService.getUsersByOrganization(
      new Types.ObjectId(organizationId)
    );
    
    // Paginación simple
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedUsers = users.slice(startIndex, endIndex);
    
    res.status(200).json({
      message: 'Usuarios de la organización obtenidos exitosamente',
      data: paginatedUsers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: users.length,
        totalPages: Math.ceil(users.length / Number(limit))
      }
    });
  } catch (error) {
    console.error('Error getting users by organization:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Desactivar permisos de un usuario
 */
export const deactivateUserPermissions = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const updatedBy = new Types.ObjectId(req.userVendor!.userId);
    
    if (!userId) {
      return res.status(400).json({
        message: 'userId es requerido',
        code: 'USER_ID_REQUIRED'
      });
    }
    
    const success = await userRolesPermissionsService.deactivateUserPermissions(
      new Types.ObjectId(userId),
      updatedBy
    );
    
    if (!success) {
      return res.status(404).json({
        message: 'Permisos de usuario no encontrados',
        code: 'USER_PERMISSIONS_NOT_FOUND'
      });
    }
    
    res.status(200).json({
      message: 'Permisos de usuario desactivados exitosamente'
    });
  } catch (error) {
    console.error('Error deactivating user permissions:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Verificar si un usuario tiene un permiso específico
 */
export const checkUserPermission = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { module, permission } = req.query;
    
    if (!userId || !module || !permission) {
      return res.status(400).json({
        message: 'userId, module y permission son requeridos',
        code: 'MISSING_REQUIRED_PARAMETERS'
      });
    }
    
    const hasPermission = await userRolesPermissionsService.hasPermission(
      new Types.ObjectId(userId),
      module as string,
      permission as string
    );
    
    res.status(200).json({
      message: 'Verificación de permiso completada',
      data: {
        userId,
        module,
        permission,
        hasPermission
      }
    });
  } catch (error) {
    console.error('Error checking user permission:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};

/**
 * Verificar si un usuario tiene alguno de los roles especificados
 */
export const checkUserRole = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { roles } = req.body;
    
    if (!userId || !roles || !Array.isArray(roles)) {
      return res.status(400).json({
        message: 'userId y roles (array) son requeridos',
        code: 'MISSING_REQUIRED_PARAMETERS'
      });
    }
    
    const hasRole = await userRolesPermissionsService.hasAnyRole(
      new Types.ObjectId(userId),
      roles as GlobalUserRole[]
    );
    
    res.status(200).json({
      message: 'Verificación de rol completada',
      data: {
        userId,
        roles,
        hasRole
      }
    });
  } catch (error) {
    console.error('Error checking user role:', error);
    res.status(500).json({
      message: 'Error interno del servidor',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
};
