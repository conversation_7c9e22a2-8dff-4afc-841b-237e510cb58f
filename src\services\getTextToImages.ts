// eslint-disable-next-line import/no-extraneous-dependencies
const { DocumentProcessorServiceClient } = require('@google-cloud/documentai').v1;
import { convertImgUrlToBase64 } from './convertImgUrlToBase64';
import { GOOGLE_PROJECT, GOOGLE_LOCATION, GOOGLE_PROCESSOR } from '../constants';

const client = new DocumentProcessorServiceClient();

export async function getTextToImages(imagePath: string) {
  const name = `projects/${GOOGLE_PROJECT}/locations/${GOOGLE_LOCATION}/processors/${GOOGLE_PROCESSOR}`;
  const encodedImage = await convertImgUrlToBase64(imagePath);
  const request = {
    name,
    rawDocument: {
      content: encodedImage,
      mimeType: 'image/jpeg',
    },
  };
  const [result] = await client.processDocument(request);
  const { document } = result;
  const entities = document.entities;

  let driverName = '';
  let address = '';
  let curp = '';
  let birthDate = '';
  let gender = '';
  entities.forEach((entity: any) => {
    if (entity.type === 'Nombre') {
      driverName = entity.mentionText.replace(/\n/g, ' ');
    }
    if (entity.type === 'Domicilio') {
      address = entity.mentionText.replace(/\n/g, ' ');
    }
    if (entity.type === 'curp') {
      curp = entity.mentionText;
    }
    if (entity.type === 'Fecha-de-nacimiento') {
      birthDate = entity.mentionText;
    }
    if (entity.type === 'Sexo') {
      gender = entity.mentionText;
    }
  });

  return { driverName, address, curp, birthDate, gender };
}
