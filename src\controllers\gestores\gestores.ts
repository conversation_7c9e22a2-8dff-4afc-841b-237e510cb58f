import { Async<PERSON>ontroller } from '@/types&interfaces/types';
import { Gestores } from '@/vendor-platform/modules/gestores/models/gestores.model';
import { Tramites } from '@/vendor-platform/modules/gestores/models/tramites.model';
import { Procedimiento } from '@/vendor-platform/modules/gestores/models/procedimientos.model';
import StockVehicle from '@/models/StockVehicleSchema';
import { sendGestorNotification } from '@/middlewares/email';

export const getAllGestores: AsyncController = async (req, res) => {
  const gestores = await Gestores.find()
    .populate('tramites', 'name description state documents')
    .sort({ name: 1 });

  if (!gestores || gestores.length === 0) {
    return res.status(200).send({
      message: 'No se encontraron gestores',
      data: [],
    });
  }

  return res.status(200).send({ message: 'Gestores encontrados', data: gestores });
};

export const getGestoresByState: AsyncController = async (req, res) => {
  const { state } = req.params;

  try {
    // Try to find tramites with the exact state
    const availableTramites = await Tramites.find({ state });

    // If no exact match, try case-insensitive search
    if (availableTramites.length === 0) {
      const caseInsensitiveTramites = await Tramites.find({
        state: { $regex: new RegExp(`^${state}$`, 'i') },
      });

      // If still no results, return empty response
      if (caseInsensitiveTramites.length === 0) {
        return res.status(200).send({
          message: `No se encontraron trámites disponibles en el estado: ${state}`,
          data: [],
        });
      }

      // Use the case-insensitive results
      const tramiteIds = caseInsensitiveTramites.map((t) => t._id);

      // Get gestores that have any of these tramites
      const gestores = await Gestores.find({
        tramites: { $in: tramiteIds },
      })
        .populate({
          path: 'tramites',
          match: { _id: { $in: tramiteIds } },
          select: 'name description state documents cost duration',
        })
        .sort({ name: 1 });

      if (gestores.length === 0) {
        return res.status(200).send({
          message: `No se encontraron gestores con trámites disponibles en el estado: ${state}`,
          data: [],
        });
      }

      return res.status(200).send({
        message: `Gestores con trámites disponibles en el estado: ${state}`,
        data: gestores,
      });
    }

    // If we found exact matches, use those
    const tramiteIds = availableTramites.map((t) => t._id);

    // Get gestores that have any of these tramites
    const gestores = await Gestores.find({
      tramites: { $in: tramiteIds },
    })
      .populate({
        path: 'tramites',
        match: { _id: { $in: tramiteIds } },
        select: 'name description state documents cost duration',
      })
      .sort({ name: 1 });

    if (gestores.length === 0) {
      return res.status(200).send({
        message: `No se encontraron gestores con trámites disponibles en el estado: ${state}`,
        data: [],
      });
    }

    return res.status(200).send({
      message: `Gestores con trámites disponibles en el estado: ${state}`,
      data: gestores,
    });
  } catch (error: any) {
    console.error('Error en getGestoresByState:', error);
    return res.status(500).send({
      message: 'Error al buscar gestores por estado',
      error: error.message || 'Error desconocido',
    });
  }
};

export const getAllTramites: AsyncController = async (req, res) => {
  const tramites = await Tramites.find();

  return res.status(200).send({ message: 'Tramites encontrados', data: tramites });
};

export const createProcedimiento: AsyncController = async (req, res) => {
  try {
    // Get the tramite information to determine cost and duration
    const tramiteId = req.body.tramiteId;
    if (!tramiteId) {
      return res.status(400).send({
        message: 'El ID del trámite es requerido para crear un procedimiento',
      });
    }

    // Get the tramite details
    const tramite = await Tramites.findById(tramiteId);
    if (!tramite) {
      return res.status(404).send({
        message: 'No se encontró el trámite especificado',
      });
    }

    // Add cost and duration from the tramite to the procedure
    const procedimientoData = {
      ...req.body,
      cost: tramite.cost,
      duration: tramite.duration,
      status: 'Pendiente', // Default status
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Create the procedure
    const procedimiento = await Procedimiento.create(procedimientoData);
    if (!procedimiento) {
      return res.status(500).send({
        message: 'Error al crear el procedimiento',
      });
    }

    // Optimized query to get gestor and vehicle data in one go
    const [gestorData, vehicleData] = await Promise.all([
      Gestores.findById(procedimiento.gestorId),
      StockVehicle.findById(procedimiento.vehicleId),
    ]);

    if (!gestorData) {
      return res.status(404).send({
        message: 'No se encontró el gestor especificado',
      });
    }

    if (!vehicleData) {
      return res.status(404).send({
        message: 'No se encontró el vehículo especificado',
      });
    }

    // Enhanced vehicle info with more details
    const vehicleInfo = [
      vehicleData.carPlates?.plates ? `Placas: ${vehicleData.carPlates.plates}` : '',
      vehicleData.vin ? `VIN: ${vehicleData.vin}` : '',
      vehicleData.circulationCard?.number
        ? `Tarjeta de Circulación: ${vehicleData.circulationCard.number}`
        : '',
    ]
      .filter(Boolean)
      .join(' - ');

    await sendGestorNotification({
      gestorName: gestorData.name,
      gestorEmail: gestorData.email,
      tramiteName: tramite.name,
      vehicleInfo: vehicleInfo,
      procedimientoId: procedimiento._id.toString(),
      cost: tramite.cost,
      duration: tramite.duration,
      status: 'Pendiente',
    });

    return res.status(200).send({
      message: 'Procedimiento creado exitosamente',
      data: procedimiento,
    });
  } catch (error: any) {
    console.error('Error en createProcedimiento:', error);
    return res.status(500).send({
      message: 'Error al crear el procedimiento',
      error: error.message || 'Error desconocido',
    });
  }
};

export const getProcedimientosByEmail: AsyncController = async (req, res) => {
  try {
    // Get all procedures for the email
    const procedimientos = await Procedimiento.find({ email: req.params.email })
      .populate('gestorId', 'name location phone email')
      .populate('tramiteId', 'name description state documents city cost duration')
      .sort({ createdAt: -1 });

    // Process each procedure to add document URLs for digital documents
    const processedProcedimientos = await Promise.all(
      procedimientos.map(async (procedimiento) => {
        const procedimientoObj = procedimiento.toObject();

        // Check if the tramite has documents and if any are digital
        if (procedimientoObj.tramiteId && procedimientoObj.tramiteId.documents) {
          // Create a new array for documents with URLs for digital ones
          const documentsWithUrls = await Promise.all(
            procedimientoObj.tramiteId.documents.map(async (doc: any) => {
              // If the document is digital, add a URL
              if (doc.format === 'digital') {
                // Determine the document type and get the appropriate URL
                let documentUrl = '';

                // Use specific constants for different document types
                switch (doc.name.toLowerCase()) {
                  case 'factura':
                  case 'invoice':
                  case 'bill':
                    documentUrl = `${process.env.API_URL}/api/documents/bill/${procedimientoObj._id}`;
                    break;
                  case 'placa':
                  case 'plate':
                  case 'license plate':
                    documentUrl = `${process.env.API_URL}/api/documents/plate/${procedimientoObj._id}`;
                    break;
                  case 'tarjeta de circulación':
                  case 'circulation card':
                    documentUrl = `${process.env.API_URL}/api/documents/circulation-card/${procedimientoObj._id}`;
                    break;
                  default:
                    documentUrl = `${process.env.API_URL}/api/documents/generic/${procedimientoObj._id}/${encodeURIComponent(doc.name)}`;
                }

                return {
                  ...doc,
                  url: documentUrl,
                };
              }

              // If not digital, return the document as is
              return doc;
            })
          );

          // Replace the documents array with our processed one
          procedimientoObj.tramiteId.documents = documentsWithUrls;
        }

        return procedimientoObj;
      })
    );

    return res.status(200).send({
      message: 'Procedimientos encontrados',
      data: processedProcedimientos,
    });
  } catch (error: any) {
    console.error('Error en getProcedimientosByEmail:', error);
    return res.status(500).send({
      message: 'Error al buscar procedimientos',
      error: error.message || 'Error desconocido',
    });
  }
};

export const getProcedimientosByVehicleId: AsyncController = async (req, res) => {
  try {
    const procedimientos = await Procedimiento.find({ vehicleId: req.params.id })
      .populate('gestorId', 'name location phone email')
      .populate('tramiteId', 'name description state documents city cost duration')
      .sort({ createdAt: -1 });

    return res.status(200).send({ message: 'Procedimientos encontrados', data: procedimientos });
  } catch (error: any) {
    console.error('Error en getProcedimientosByVehicleId:', error);
    return res.status(500).send({
      message: 'Error al buscar procedimientos',
      error: error.message || 'Error desconocido',
    });
  }
};
