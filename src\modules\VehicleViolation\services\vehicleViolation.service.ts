import { getTextFromCaptcha } from '@/services/ocr/captchaTextExtractor';
import puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';
import { Readable } from 'stream';
import StockVehicle, { Violation } from '@/models/StockVehicleSchema';
import { logger } from '@/clean/lib/logger';
import {
  CAPTCHA_LINK,
  DAYS_THRESHOLD,
  requestDelaysInMinutes,
  VIOLATION_LINK,
  SpanishMonths,
  VIOLATION_LINK_WITH_AMOUNT,
  VIOLATION_LINK_WITH_AMOUNT_LOGIN_EMAIL,
  VIOLATION_LINK_WITH_AMOUNT_LOGIN_PASSWORD,
  VIOLATION_LINK_WITH_AMOUNT_PLATE_PAGE,
  VIOLATION_LINK_WITH_AMOUNT_HOME_PAGE,
} from '@/constants';

interface CapturedResponse {
  status: string;
  html: string;
  msg: string;
  fotocivicas_verifica: boolean;
  sin_derecho: boolean;
  code: string;
}

interface HtmlResults {
  folio: string;
  fecha_de_infraccion: Date;
  situacion: string;
}

interface InfraccionData {
  infraccion?: string;
  montoEnPesos?: string;
  fechaInfraccion?: string;
  status?: string;
}

interface InfraccionResuts {
  pendingIntraccions: InfraccionData[];
  paidIntraccions: InfraccionData[];
}

enum ScrapedStatus {
  pagada = 'pagada',
  pendiente_por_pagar = 'pendiente por pagar',
}

class VehicleViolation {
  async runRandomizedViolationFetchCDMX() {
    logger.info(`[VehicleViolation] - runRandomizedViolationFetch started`);

    const scheduleNextRun = () => {
      const randomMinutes = requestDelaysInMinutes[Math.floor(Math.random() * requestDelaysInMinutes.length)];
      const delayMs = randomMinutes * 60 * 1000;
      logger.info(`[VehicleViolation] - runRandomizedViolationFetch try after ${randomMinutes} minutes`);

      setTimeout(async () => {
        try {
          logger.info(
            `[VehicleViolation] - runRandomizedViolationFetch Running violation fetch at ${new Date().toISOString()}`
          );
          await this.fetchVehiceViolationCDMX();
        } catch (error) {
          logger.error(`[VehicleViolation] runRandomizedViolationFetch error: ${error}`);
        } finally {
          scheduleNextRun();
        }
      }, delayMs);
    };

    scheduleNextRun();
  }

  async fetchVehiceViolationCDMX() {
    try {
      logger.info(`[VehicleViolation] - fetchVehiceViolation cron job started`);
      const thresholdDate = new Date(Date.now() - DAYS_THRESHOLD * 24 * 60 * 60 * 1000);

      const vehicle = await StockVehicle.findOne({
        $or: [{ lastViolationCheck: null }, { lastViolationCheck: { $lt: thresholdDate } }],
        'carPlates.plates': { $ne: null },
        country: { $regex: '^mexico$', $options: 'i' },
        vehicleState: { $regex: '^cdmx$', $options: 'i' },
        $expr: {
          $gt: [{ $strLenCP: '$carPlates.plates' }, 1],
        },
      });

      await this.fetchAndUpdateViolationCDMX(vehicle);
    } catch (error) {
      logger.error(`[VehicleViolation] - fetchVehiceViolation error ${error}`);
      return;
    }
  }

  async fetchAndUpdateViolationCDMX(vehicle: any) {
    if (vehicle && vehicle.carPlates?.plates) {
      const plates = vehicle.carPlates?.plates;
      const carNumber = vehicle.carNumber;
      logger.info(
        `[VehicleViolation] - fetchAndUpdateViolation fetch violation for vehicle ${carNumber} plates ${plates}`
      );
      const violationData = await this.fetchVehiceAmountViolation(plates);
      let success = true;
      for (const violations of Object.values(violationData!)) {
        if (violations) {
          logger.info(
            `[VehicleViolation] - fetchAndUpdateViolation found ${violations?.length} violations for vehicle ${vehicle.carNumber} plates ${plates}`
          );
          let updateCount = 0;
          let addCount = 0;
          if (violations.length > 0) {
            violations?.forEach((violation: InfraccionData) => {
              const findViolation = vehicle.violations.find(
                (v: Violation) => v.folio === violation.infraccion
              );
              const scrapedStatus = violation?.status?.trim().toLowerCase();
              let status = 'unknown';
              if (scrapedStatus === ScrapedStatus.pagada) status = 'paid';
              else if (scrapedStatus === ScrapedStatus.pendiente_por_pagar) status = 'unpaid';
              if (findViolation) {
                findViolation.status = status;
                updateCount++;
              } else {
                vehicle.violations.push({
                  folio: violation.infraccion,
                  status: status,
                  violationDate: violation.fechaInfraccion,
                  amount: violation.montoEnPesos,
                });
                addCount++;
              }
            });
            logger.info(
              `[VehicleViolation] - fetchAndUpdateViolation ${updateCount} violations updated and ${addCount} violations added for vehicle ${vehicle.carNumber} plates ${plates}`
            );
          }

          //Update StockVehicle
          await StockVehicle.updateOne(
            { _id: vehicle._id },
            {
              $set: {
                violations: vehicle.violations,
                lastViolationCheck: new Date(),
              },
            }
          );
        } else {
          success = false;
          logger.info(
            `[VehicleViolation] - fetchAndUpdateViolation unable to fetch violation will try later for vehicle ${vehicle.carNumber} plates ${plates}`
          );
        }
      }
      if (success) {
        return { status: true, message: 'Vehicle violations updated' };
      }
      return {
        status: false,
        message: 'No se pudieron obtener las infracciones. Por favor, inténtelo de nuevo más tarde.',
      };
    } else {
      return { status: false, message: 'Número de coche o matrícula inválido' };
    }
  }

  async updateViolationByCarNumber(carNumber: string) {
    try {
      const vehicle = await StockVehicle.findOne({
        carNumber: carNumber,
      });

      return await this.fetchAndUpdateViolationCDMX(vehicle);
    } catch (error) {
      logger.error(`[VehicleViolation] - updateViolationByCarNumber error ${error}`);
      return {
        status: false,
        message: 'No se pudieron obtener las infracciones. Por favor, inténtelo de nuevo más tarde.',
      };
    }
  }

  async fetchViolationByPlate(plate: string): Promise<HtmlResults[] | null> {
    try {
      plate = plate.replace(/[\s-]/g, '').toUpperCase();
      logger.info(`[VehicleViolation] - fetchViolationByPlate for plate ${plate}`);

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
      const page = await browser.newPage();
      await page.goto(VIOLATION_LINK, {
        waitUntil: 'networkidle2',
        timeout: 60000,
      });

      logger.info(`[VehicleViolation] - fetchViolationByPlate browser page open for plate ${plate}`);

      const captchaElement = await page.$(`img[src^="${CAPTCHA_LINK}"]`);
      if (!captchaElement) {
        logger.error(`[VehicleViolation] - fetchViolationByPlate CAPTCHA image not found for plate ${plate}`);
        return null;
      }

      // Take a screenshot of the exact image (not fetching it again)
      const captchaImageUint8 = await captchaElement.screenshot();
      const captchaImageBuffer = Buffer.from(captchaImageUint8);

      // Create a fake multer-style file object
      const mockMulterFile = {
        fieldname: 'captcha',
        originalname: 'captcha.png',
        encoding: '7bit',
        mimetype: 'image/png',
        size: captchaImageBuffer.length,
        buffer: captchaImageBuffer,
        destination: '',
        filename: '',
        path: '',
        stream: Readable.from(captchaImageBuffer),
      };

      // Cast to correct type
      const textResponse = await getTextFromCaptcha(mockMulterFile as unknown as Express.Multer.File);

      logger.info(
        `[VehicleViolation] - fetchViolationByPlate captcha from gemini ${textResponse.captcha} for plate ${plate}`
      );

      // Fill plate and captcha
      await page.type('#inputPlaca', plate);
      await page.type('#captcha_code', textResponse.captcha);

      // Set up a promise to capture the AJAX response and process the 'respuesta'
      const captureResponse = new Promise((resolve) => {
        page.on('response', async (response: { url: () => string | string[]; json: () => any }) => {
          // Use PuppeteerResponse type
          if (response.url().includes('consulta_adeudos/obtenAdeudos')) {
            // Match the AJAX request URL
            const responseBody = await response.json(); // Assuming the response is JSON
            if (responseBody.status) {
              // If status is true, process the response as needed
              resolve(responseBody); // Resolve the promise with the response data
            } else {
              // Handle cases where status is false or other error conditions
              logger.info(
                `[VehicleViolation] - fetchViolationByPlate Error with response for plate ${plate}: ${JSON.stringify(responseBody)}`
              );
              resolve(null); // Resolve with null in case of error
            }
          }
        });
      });

      // Trigger the form submission (simulate clicking the "Buscar" button)
      await page.click('button.btn-cdmx'); // Click the "Buscar" button

      logger.info(`[VehicleViolation] - fetchViolationByPlate request submitted for plate ${plate}`);
      // Wait for the response and process it
      const respuesta = (await captureResponse) as CapturedResponse;
      logger.info(
        `[VehicleViolation] - fetchViolationByPlate request received and browser closed for plate ${plate}`
      );
      // Close the browser
      await browser.close();

      if (respuesta) {
        // You can now process the respuesta (AJAX response)
        if (respuesta?.html) {
          const results = await this.htmlHandler(respuesta?.html);
          if (results?.length === 0) {
            logger.info(
              `[VehicleViolation] - fetchViolationByPlate response for plate ${plate}: ${JSON.stringify(respuesta)}`
            );
          }
          return results;
        } else {
          logger.info(
            `[VehicleViolation] - fetchViolationByPlate HTML not found in response for plate ${plate}: ${JSON.stringify(respuesta)}`
          );
          return [];
        }
      } else {
        logger.info(
          `[VehicleViolation] - fetchViolationByPlate No valid response received. for plate ${plate}`
        );
      }

      return null;
    } catch (error) {
      logger.error(`[VehicleViolation] - fetchViolationByPlate error ${error}`);
      return null;
    }
  }

  async htmlHandler(htmlString: string) {
    const results: HtmlResults[] = [];
    try {
      logger.info(`[VehicleViolation] - htmlHandler started`);
      const $ = cheerio.load(htmlString);

      // --- Strategy: Find the specific table ---
      // Let's target the table within the second "step-content" div,
      // as it seems to contain the "Infracciones" section based on the HTML structure.
      const infraccionesContent = $('div[data-ktwizard-type="step-content"]').eq(1); // 0-indexed, so 1 is the second one
      const table = infraccionesContent.find('table');

      if (table.length === 0) {
        // If the table isn't found using this strategy, return empty or an error
        logger.info(`[VehicleViolation] - htmlHandler Target table for infracciones not found.`);
        return results;
      }
      const rows = table.find('tbody > tr'); // Select direct children rows of tbody

      // --- Strategy: Iterate through rows assuming structure ---
      rows.each((index: any, element: any) => {
        const firstTd = $(element).find('td').first();
        const firstTdText = firstTd.text().trim();

        // Check if the first cell contains what looks like a Folio (digits, possibly with spaces)
        // This indicates the start of an infraction's data block
        if (firstTdText.match(/^\d[\d\s]*$/)) {
          const tds = $(element).find('td');

          const folio = $(tds[0]).text().trim();
          const fechaInfraccion = $(tds[1]).text().trim();

          // Extract Situación more carefully
          const situacionElement = $(tds[2]);
          // Attempt to get the primary status text (like "No pagada")
          let situacionStatus = situacionElement.find('span.nopagada').text().trim();
          // Fallback if span.nopagada isn't there or doesn't match
          if (!situacionStatus) {
            situacionStatus = situacionElement.find('span.pagada').text().trim();
          }
          if (!situacionStatus) {
            situacionStatus = 'unknown';
          }

          results.push({
            folio: folio,
            fecha_de_infraccion: new Date(fechaInfraccion), // Match requested field name
            situacion: situacionStatus, // Provide the extracted status
          });
        }
      });
      logger.info(`[VehicleViolation] - htmlHandler result: ${JSON.stringify(results)}`);

      return results;
    } catch (error) {
      logger.error(`[VehicleViolation] - htmlHandler Error parsing HTML: ${error}`);
      return results;
    }
  }

  async fetchVehiceAmountViolation(plate: string): Promise<InfraccionResuts | null> {
    try {
      plate = plate.replace(/[\s-]/g, '').toUpperCase();
      logger.info(`[VehicleViolation] - fetchVehiceAmountViolation for plate ${plate}`);

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });
      const page = await browser.newPage();

      logger.info(`[VehicleViolation] - Navigating to login page`);
      await page.goto(VIOLATION_LINK_WITH_AMOUNT);

      logger.info(`[VehicleViolation] - fetchVehiceAmountViolation Typing login credentials`);
      await page.waitForSelector('#frmLogin\\:txtCorreo');
      await page.type('#frmLogin\\:txtCorreo', VIOLATION_LINK_WITH_AMOUNT_LOGIN_EMAIL);
      await page.waitForSelector('#frmLogin\\:txtPassword');
      await page.type('#frmLogin\\:txtPassword', VIOLATION_LINK_WITH_AMOUNT_LOGIN_PASSWORD);

      logger.info(`[VehicleViolation] - fetchVehiceAmountViolation Attempting to log in`);
      await Promise.all([
        page.waitForNavigation({ waitUntil: 'networkidle0' }),
        page.click('#frmLogin\\:btnIngresar'),
      ]);

      if (page.url() === VIOLATION_LINK_WITH_AMOUNT_HOME_PAGE) {
        logger.info(`[VehicleViolation] - fetchVehiceAmountViolation Login successful for plate ${plate}`);

        const newPage = await browser.newPage();
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Opening violation page for plate ${plate}`
        );
        await newPage.goto(`${VIOLATION_LINK_WITH_AMOUNT_PLATE_PAGE}${plate}/`, {
          waitUntil: 'networkidle0',
        });

        const extractData = async (tabId: string, type: string): Promise<InfraccionData[]> => {
          logger.info(
            `[VehicleViolation] - fetchVehiceAmountViolation Extracting ${type} data for plate ${plate}`
          );
          return newPage.evaluate((id: any) => {
            const cards = document.querySelectorAll(`#${id} .card`);
            const results: InfraccionData[] = [];

            cards.forEach((card) => {
              const result: InfraccionData = {};

              const ths = card.querySelectorAll('thead tr th');
              ths.forEach((th) => {
                const text = (th as HTMLElement).innerText;
                if (text.includes('Infracción:'))
                  result.infraccion = th.querySelector('span')?.innerText.trim();
                if (text.includes('Monto en pesos:'))
                  result.montoEnPesos = th.querySelector('span')?.innerText.trim();
                if (text.includes('PAGADA')) result.status = th.querySelector('span')?.innerText.trim();
                if (text.includes('PENDIENTE POR PAGAR'))
                  result.status = th.querySelector('span')?.innerText.trim();
              });

              const dateThs = card.querySelectorAll('.card-body table thead tr th');
              dateThs.forEach((th) => {
                const strong = th.querySelector('strong');
                if (strong?.innerText.includes('Fecha infracción:')) {
                  result.fechaInfraccion = (th as HTMLElement).innerText
                    .replace('Fecha infracción: ', '')
                    .trim();
                }
              });

              results.push(result);
            });

            return results;
          }, tabId);
        };

        const pendingData = await extractData('Tokyo', 'pending');
        const parsedPending = pendingData.map((entry) => ({
          ...entry,
          fechaInfraccion: entry.fechaInfraccion ? this.parseSpanishDate(entry.fechaInfraccion) : undefined,
        }));
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Found ${parsedPending.length} pending violations for plate ${plate}`
        );

        const paidData = await extractData('Paris', 'paid');
        const parsedPaid = paidData.map((entry) => ({
          ...entry,
          fechaInfraccion: entry.fechaInfraccion ? this.parseSpanishDate(entry.fechaInfraccion) : undefined,
        }));
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Found ${parsedPaid.length} paid violations for plate ${plate}`
        );

        const infraccionResuts: InfraccionResuts = {
          pendingIntraccions: parsedPending,
          paidIntraccions: parsedPaid,
        };

        await browser.close();
        return infraccionResuts;
      } else {
        logger.info(
          `[VehicleViolation] - fetchVehiceAmountViolation Login failed. URL after attempt: ${page.url()} for plate ${plate}`
        );
      }

      await browser.close();
      return null;
    } catch (error: any) {
      logger.error(`[VehicleViolation] - fetchVehiceAmountViolation error for plate ${plate}`, {
        message: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  parseSpanishDate(dateStr: string): string | undefined {
    const match = dateStr.toLowerCase().match(/(\d{1,2})\s+de\s+([a-zñ]+)\s+de\s+(\d{4})/i);

    if (!match) return undefined;

    const day = match[1].padStart(2, '0');
    const month = SpanishMonths[match[2]];
    const year = match[3];

    if (!month) return undefined;

    return `${year}-${month}-${day}`;
  }
}

export const vehicleViolationService = new VehicleViolation();
