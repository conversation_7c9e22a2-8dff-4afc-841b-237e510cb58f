import axios from 'axios';
import { associatePaymentsConsts, associateText } from '../constants';
import Associate from '../models/associateSchema';
import RegionsPayments from '../models/regionPaymentsSchema';
import { ValidRegion, tokenAssignGigstack } from '../services/tokenAssignGigstack';
import AssociatePayments from '../models/associatePayments';
import MainContractSchema from '../models/mainContractSchema';
import { parse } from 'date-fns';
import moment from 'moment';

export async function createGigRecurrentPay(regionCode: string, associateID: any) {
  try {
    const region = await RegionsPayments.findOne({ region: regionCode });
    if (!region) {
      throw new Error(associatePaymentsConsts.errors.notValidRegion);
    }
    const gigToken = tokenAssignGigstack(regionCode as ValidRegion);
    const associate = await Associate.findById(associateID);
    if (!associate) {
      throw new Error(associateText.errors.associateNotFound);
    }

    const mainContract = await MainContractSchema.findOne({ associatedId: associateID });

    if (!mainContract) throw new Error(associatePaymentsConsts.errors.mainContract404);

    const associatePayment = await AssociatePayments.findOne({ contractId: mainContract._id });

    if (!associatePayment) {
      throw new Error(associatePaymentsConsts.errors.paymentNotFound);
    }

    if (!associatePayment.model) {
      throw new Error(associatePaymentsConsts.errors.modelNotFound);
    }

    // const formatStartDate = parse(mainContract.allPayments[0].day, 'dd-MM-yyyy', new Date());
    const formatEndDate = parse(
      mainContract.allPayments[mainContract.allPayments.length - 1].day,
      'dd-MM-yyyy',
      new Date()
    );
    const formatStartDate = moment(mainContract.deliveredDate).add(6, 'hours');

    // const timestampEnMilisegundos = formatStartDate.getTime();
    const timestampEnMilisegundosEnd = formatEndDate.getTime();
    // const timestampEnMilisegundosStart = formatStartDate.getTime();

    // const startDate = timestampEnMilisegundos;

    const neverStartDays = [4, 5];
    const startDate = () => {
      if (neverStartDays.includes(moment(formatStartDate).day())) {
        return moment(formatStartDate).day() === 4
          ? moment(formatStartDate).add(4, 'days').valueOf()
          : moment(formatStartDate).add(3, 'days').valueOf();
      }
      if (neverStartDays.includes(moment().day())) {
        return moment().day() === 4 ? moment().add(4, 'days').valueOf() : moment().add(3, 'days').valueOf();
      }
      return moment().valueOf();
    };

    const endDate = timestampEnMilisegundosEnd;

    const gigConfig = {
      headers: {
        Authorization: `Bearer ${gigToken}`,
        'Content-Type': 'application/json',
      },
    };

    const gigData = {
      payment: {
        client: {
          name: `${associate.firstName} ${associate.lastName}`,
          email: associate.email,
        },
        items: [
          {
            id: region.models[associatePayment.model].rentID,
            quantity: 1,
          },
          {
            id: region.models[associatePayment.model].assistanceID,
            quantity: 1,
          },
        ],
        currency: 'MXN',
        methodsTypesOptions: ['bank'],
        automateInvoiceOnComplete: true,
      },
      useClientBankAccount: true,
      temporality: 'weekly',
      onWeekday: 'thursday',
      onTime: '17:00',
      startDate: startDate(),
      endDate,
    };

    const { data } = await axios.post(
      'https://gigstack-cfdi-bjekv7t4.uc.gateway.dev/v1/recurring/payment',
      gigData,
      gigConfig
    );

    const suscriptionId = data.data.id;
    associatePayment.suscriptionId = suscriptionId;

    await associatePayment.save();

    return { data, startDate: moment(startDate()).format('DD-MM-YYYY') };
  } catch (error: any) {
    console.error(error);
    return error.response.data.message;
  }
}
