import { NextFunction, Response } from 'express';
import { MyRequest } from '../types&interfaces/interfaces';
import MainContractSchema from '../models/mainContractSchema';
import { associatePaymentsConsts } from '../constants';

type ControllerNextAsync = (
  req: MyRequest,
  res: Response,
  next: NextFunction
) => Promise<void | Response<any, Record<string, any>>>;

export const createTableValidator: ControllerNextAsync = async (req, res, next) => {
  const { id } = req.params;
  try {
    const contract = await MainContractSchema.findById(id);

    if (!contract) {
      return res.status(404).send({ message: associatePaymentsConsts.errors.mainContract404 });
    }
    return next();
  } catch (error) {
    console.error(error);

    return res.status(400).send({ message: 'Ocurrio un error', error });
  }
};
