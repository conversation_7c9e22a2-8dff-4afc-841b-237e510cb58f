import { Schema, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';

const gpsStatusSchema = new Schema({
  gps: { type: String, required: true },
  status: { type: String, required: true },
  createdAt: {
    type: String,
    default: getCurrentDateTime,
  },
  updatedAt: {
    type: String,
    default: getCurrentDateTime,
  },
});
gpsStatusSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});

const GpsStatusSchema = model('GpsStatusSchema', gpsStatusSchema);

export default GpsStatusSchema;
