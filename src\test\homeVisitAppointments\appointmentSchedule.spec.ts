/* eslint-disable array-bracket-newline */
import app from '../../app';
import request from 'supertest';
import jwt from 'jsonwebtoken';
import { createAdminTest, createHomeVisitor, createSchedule } from '../functionts';
import { accessTokenSecret } from '../../constants';

let userAdminId: string;
let userAdminRole: string;
let token: string;

beforeAll(async () => {
  const adminUser = await createAdminTest();
  userAdminId = adminUser._id.toString();
  userAdminRole = adminUser.role;
  token = jwt.sign({ userId: userAdminId, role: userAdminRole }, accessTokenSecret, {
    expiresIn: '5m',
  });
});

describe('Test Schedule Update', () => {
  let homeVisitor1: any;
  beforeAll(async () => {
    homeVisitor1 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 1',
    });
  });

  let response: request.Response;

  it('should throw 400 if payload is not sent or is incorrect', async () => {
    response = await request(app)
      .put(`/calendar/schedule/${homeVisitor1._id}`)
      .set('Authorization', `Bearer ${token}`);
    expect(response.status).toBe(400);
    expect(response.body.error).toBeDefined();
  });

  it('should update schedule', async () => {
    await createSchedule(homeVisitor1._id);
    response = await request(app)
      .put(`/calendar/schedule/${homeVisitor1._id}`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        user: homeVisitor1._id,
        name: 'test schedule',
        weeklySchedule: {
          monday: { start: '09:00', end: '06:00' },
          tuesday: { start: '09:00', end: '06:00' },
          wednesday: { start: '09:00', end: '06:00' },
          thursday: { start: '09:00', end: '06:00' },
          friday: { start: '09:00', end: '06:00' },
          saturday: { start: '09:00', end: '06:00' },
          sunday: { start: '09:00', end: '06:00' },
        },
        maxSimultaneousAppointments: 1,
        timezone: 'America/Mexico_City',
        breakTimes: [{ start: '01:00', end: '02:00' }],
        bufferTime: 0,
        duration: 30,
        maxAdvanceBookingDays: 5,
        minBookingNoticeHours: 1,
      });

    console.log('response: ', response.body);
    expect(response.status).toBe(201);
    expect(response.body.data.user).toBe(homeVisitor1._id.toString());
  });

  it('should block slots', async () => {
    const homeVisitor5 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 5',
    });
    await createSchedule(homeVisitor5._id.toString());
    response = await request(app)
      .put(`/calendar/schedule/${homeVisitor5._id}`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        user: homeVisitor5._id,
        name: 'test schedule',
        weeklySchedule: {
          monday: { start: '09:00', end: '06:00' },
          tuesday: { start: '09:00', end: '06:00' },
          wednesday: { start: '09:00', end: '06:00' },
          thursday: { start: '09:00', end: '06:00' },
          friday: { start: '09:00', end: '06:00' },
          saturday: { start: '09:00', end: '06:00' },
          sunday: { start: '09:00', end: '06:00' },
        },
        maxSimultaneousAppointments: 1,
        timezone: 'America/Mexico_City',
        breakTimes: [{ start: '01:00', end: '02:00' }],
        bufferTime: 0,
        duration: 30,
        maxAdvanceBookingDays: 5,
        minBookingNoticeHours: 1,
        blockSlots: [
          { startDate: '2025-02-28', endDate: '2025-02-28', startTime: '09:30am', endTime: '10:00am' },
          {
            startDate: '2025-02-28',
            endDate: '2025-02-28',
            startTime: '10:30am',
            endTime: '11:00am',
          },
        ],
        addedSlots: [],
      });

    expect(response.status).toBe(201);
    expect(response.body.data.blockSlotsErrors).toBeDefined();
  });

  it('should return block slots error array populated', async () => {
    const homeVisitor3 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 3',
    });

    await createSchedule(homeVisitor3._id.toString());
    response = await request(app)
      .put(`/calendar/schedule/${homeVisitor3._id}`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        user: homeVisitor3._id,
        name: 'test schedule',
        weeklySchedule: {
          monday: { start: '09:00', end: '06:00' },
          tuesday: { start: '09:00', end: '06:00' },
          wednesday: { start: '09:00', end: '06:00' },
          thursday: { start: '09:00', end: '06:00' },
          friday: { start: '09:00', end: '06:00' },
          saturday: { start: '09:00', end: '06:00' },
          sunday: { start: '09:00', end: '06:00' },
        },
        maxSimultaneousAppointments: 1,
        timezone: 'America/Mexico_City',
        breakTimes: [{ start: '01:00', end: '02:00' }],
        bufferTime: 0,
        duration: 30,
        maxAdvanceBookingDays: 5,
        minBookingNoticeHours: 1,
        blockSlots: [
          { startDate: '2025-02-20', endDate: '2025-02-20', startTime: '09:30am', endTime: '10:00am' },
          {
            startDate: '2025-02-21',
            endDate: '2025-02-21',
            startTime: '10:30am',
            endTime: '11:00am',
          },
        ],
        addedSlots: [],
      });

    expect(response.status).toBe(201);
    expect(response.body.data.blockSlotsErrors).toBeDefined();
    expect(response.body.data.blockSlotsErrors.length).toBe(2);
  });

  it('should add slots', async () => {
    const homeVisitor4 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 4',
    });

    await createSchedule(homeVisitor4._id.toString());

    const weeklySchedule = {
      monday: { start: '09:00', end: '06:00' },
      tuesday: { start: '09:00', end: '05:00' },
      wednesday: { start: '09:00', end: '06:00' },
      thursday: { start: '09:00', end: '05:00' },
      friday: { start: '09:00', end: '06:00' },
      saturday: { start: '09:00', end: '06:00' },
      sunday: { start: '09:00', end: '06:00' },
    };
    response = await request(app)
      .put(`/calendar/schedule/${homeVisitor4._id}`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        user: homeVisitor1._id,
        name: 'test schedule',
        weeklySchedule: weeklySchedule,
        maxSimultaneousAppointments: 1,
        timezone: 'America/Mexico_City',
        breakTimes: [{ start: '01:00', end: '02:00' }],
        bufferTime: 0,
        duration: 30,
        maxAdvanceBookingDays: 5,
        minBookingNoticeHours: 1,
        addedSlots: [
          { startDate: '2025-02-27', endDate: '2025-02-27', startTime: '06:30pm', endTime: '07:00pm' },
        ],
        blockSlots: [],
      });

    expect(response.status).toBe(201);
    expect(response.body.data.addedSlotsErrors).toBeDefined();
    expect(response.body.data.addedSlotsErrors.length).toBe(0);
  });

  it('should return add slots array populated', async () => {
    const homeVisitor6 = await createHomeVisitor({
      email: '<EMAIL>',
      name: 'Test Home Visitor 4',
    });

    await createSchedule(homeVisitor6._id.toString());

    const weeklySchedule = {
      monday: { start: '09:00', end: '06:00' },
      tuesday: { start: '09:00', end: '05:00' },
      wednesday: { start: '09:00', end: '06:00' },
      thursday: { start: '09:00', end: '05:00' },
      friday: { start: '09:00', end: '06:00' },
      saturday: { start: '09:00', end: '06:00' },
      sunday: { start: '09:00', end: '06:00' },
    };
    response = await request(app)
      .put(`/calendar/schedule/${homeVisitor6._id}`)
      .set('Authorization', `Bearer ${token}`)
      .send({
        user: homeVisitor1._id,
        name: 'test schedule',
        weeklySchedule: weeklySchedule,
        maxSimultaneousAppointments: 1,
        timezone: 'America/Mexico_City',
        breakTimes: [{ start: '01:00', end: '02:00' }],
        bufferTime: 0,
        duration: 30,
        maxAdvanceBookingDays: 5,
        minBookingNoticeHours: 1,
        // eslint-disable-next-line prettier/prettier
        addedSlots: [{ startDate: '2025-02-28', endDate: '2025-02-28', startTime: '09:30am', endTime: '10:00am' }],
        blockSlots: [],
      });

    expect(response.status).toBe(201);
    expect(response.body.data.addedSlotsErrors).toBeDefined();
    expect(response.body.data.addedSlotsErrors.length).toBe(0);
  });
});
