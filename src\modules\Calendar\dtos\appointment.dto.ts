import { AppointmentStatus } from '@/models/appointment';
import z from 'zod';

export const bookSlotDto = z.object({
  admissionRequestId: z.string(),
  slotId: z.string(),
});

export const createAppointmentDto = z.object({
  admissionRequestId: z.string(),
  slotId: z.string(),
  isAppointmentReschedule: z.boolean(),
  source: z.string().optional(),
  // will be user id
  entity: z.any().optional(),
});

export const cancelAppointmentDto = z.object({
  id: z.string(),
  admissionRequestId: z.string(),
  slot: z.string(),
});

export const statusAppointmentDto = z.object({
  id: z.string(),
  status: z
    .string()
    .refine((status) => Object.values(AppointmentStatus).includes(status as AppointmentStatus), {
      message: 'Invalid status',
    }),
});

export const NO_HOME_VISITOR_AVAILABLE = 'no-home-visitor-available';

export const homeVisitorChangeAppointmentDto = z
  .object({
    slotId: z.string(),
    homeVisitorId: z.string(),
    appointmentId: z.string(),
  })
  .superRefine((data) => {
    if (data.homeVisitorId === NO_HOME_VISITOR_AVAILABLE && !data.appointmentId && !data.slotId) {
      return true;
    }
    return data.homeVisitorId && data.appointmentId && data.slotId;
  });
