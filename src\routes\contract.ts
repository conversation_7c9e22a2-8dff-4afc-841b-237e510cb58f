import { Router } from 'express';
import {
  getContracts,
  getTheNextNumber,
  addContract,
  addAssociatedContract,
  getPaymentsByVehicleId,
  getLastPaymeOfVehicleById,
  updatePaymentById,
  easyAddAssociatedContract,
  updateContractDoc,
  calculateWeeksForSemiNews,
} from '../controllers/contract';
import { verifyToken } from '../middlewares/verifyToken';
import { upload } from '../multer/multer';

const router = Router();

router.use(verifyToken);
router.get('/get', getContracts);
router.get('/get/vehiclePayments/:id', getPaymentsByVehicleId);
router.get('/get/vehiclePayments/last/:id', getLastPaymeOfVehicleById);
// getLastPaymeOfVehicleById
router.post('/next', getTheNextNumber);
router.post('/add', addContract);
router.post('/associated/add/easy', easyAddAssociatedContract);
router.post('/associated/add', upload.single('contract'), addAssociatedContract);
router.post('/semi-new/calculate-weeks', upload.single('invoiceFile'), calculateWeeksForSemiNews);
router.patch('/associated/update-file', upload.single('contract'), updateContractDoc);
router.patch('/updatePayment/:id', updatePaymentById);
export default router;
