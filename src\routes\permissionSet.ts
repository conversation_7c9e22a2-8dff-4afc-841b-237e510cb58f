import { Router } from 'express';
import {
  getPermissionMatrix,
  addPermissionSet,
  getAllPermissionSets,
  getPermissionSetById,
  editPermissionSet,
  deletePermissionSet,
  assignPermissionSet,
} from '../controllers/permissionSet';

const permissionSet = Router();

permissionSet.get('/getPermissionMatrix', getPermissionMatrix);
permissionSet.get('/getAllPermissionSets', getAllPermissionSets);
permissionSet.get('/:id', getPermissionSetById);
permissionSet.patch('/:id', editPermissionSet);
permissionSet.post('/create', addPermissionSet);
permissionSet.delete('/:id', deletePermissionSet);
permissionSet.patch('/assginPermissionSet/:id', assignPermissionSet);

export default permissionSet;
