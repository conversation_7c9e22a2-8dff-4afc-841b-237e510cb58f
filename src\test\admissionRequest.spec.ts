import app from '../app';
import request from 'supertest';
import jwt from 'jsonwebtoken';
import { createAdmissionRequestTest, createOcnUserTest } from './functionts';
import { accessTokenSecret } from '../constants';
import {
  admissionRequestResponse,
  createAdmissionRequestPayload,
  homeVisitAddressInformationPayload,
  homeVisitAutoMobileInformationPayload,
  homeVisitContactInformationPayload,
  homeVisitDebtInformationPayload,
  homeVisitFamilyInformationPayload,
  homeVisitOutcomeInformationPayload,
  homeVisitPersonalInformationPayload,
  homeVisitPersonalInformationResponse,
  homeVisitPropertyInformationPayload,
  homeVisitReferencesInformationPayload,
} from './request-payload/payload';

let userAdminId: string;
let userAdminRole: string;
let token: string;
let admissionRequest: Record<string, any>;

beforeAll(async () => {
  const ocnUser = await createOcnUserTest();
  userAdminId = ocnUser._id.toString();
  userAdminRole = ocnUser.role;
  token = jwt.sign({ userId: userAdminId, role: userAdminRole }, accessTokenSecret, {
    expiresIn: '5m',
  });
  admissionRequest = await createAdmissionRequestTest();
});

describe('Get admission Request by id /admission/requests', () => {
  let response: request.Response;

  it('should return 200 status code and get Admission Request By Id', async () => {
    response = await request(app)
      .get(`/admission/requests/${admissionRequest._id}`)
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
    expect(response.body.success).toEqual(true);
    expect(response.body.data.id).toEqual(admissionRequest._id.toString());
    expect(response.body.data.status).toEqual(admissionRequest.status);
    expect(response.body.data.createdAt).not.toBeNull();
    expect(response.body.data.updatedAt).not.toBeNull();
    expect(response.body.data.screenshots).toEqual([]);
    expect(response.body.data.homeVisit).toEqual(admissionRequestResponse.data.homeVisit);
    expect(response.body.data.source).toEqual(admissionRequestResponse.data.source);
    expect(response.body.data.clientIpAddress).toEqual(admissionRequestResponse.data.clientIpAddress);
  });
});

describe('Create Admission Request /admission/requests', () => {
  let response: request.Response;
  it('should return 200 status code and create an Admission Request', async () => {
    response = await request(app)
      .post(`/admission/requests`)
      .set('Authorization', `Bearer ${token}`)
      .send(createAdmissionRequestPayload);

    expect(response.status).toBe(200);
    expect(response.body.success).toEqual(true);
    expect(response.body.data).toHaveProperty('personalData');
    expect(response.body.data).toHaveProperty('documentsAnalysis');
    expect(response.body.data).toHaveProperty('palenca');
    expect(response.body.data).toHaveProperty('earningsAnalysis');
    expect(response.body.data).toHaveProperty('riskAnalysis');
    expect(response.body.data).toHaveProperty('homeVisit');
    expect(response.body.data).toHaveProperty('createdAt');
    expect(response.body.data).toHaveProperty('updatedAt');
    expect(response.body.data).toHaveProperty('screenshots');
    expect(response.body.data).toHaveProperty('source');
    expect(response.body.data).toHaveProperty('clientIpAddress');
    expect(response.body.data.personalData).toEqual(admissionRequestResponse.data.personalData);
    expect(response.body.data.documentsAnalysis).toEqual(admissionRequestResponse.data.documentsAnalysis);
    expect(response.body.data.earningsAnalysis).toEqual(admissionRequestResponse.data.earningsAnalysis);
    expect(response.body.data.riskAnalysis).toEqual(admissionRequestResponse.data.riskAnalysis);
    expect(response.body.data.homeVisit).toEqual(admissionRequestResponse.data.homeVisit);
    expect(response.body.data.createdAt).not.toBeNull();
    expect(response.body.data.updatedAt).not.toBeNull();
    expect(response.body.data.screenshots).toEqual([]);
  });
});

describe('Update Admission Request Home Visit /admission/requests/:id/home-visit', () => {
  let response: request.Response;
  it('should return 200 status code and update Admission Request  Personal Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitPersonalInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.personalData.firstName).toEqual(
      homeVisitPersonalInformationPayload.personalData.firstName
    );
    expect(response.body.data.personalData.lastName).toEqual(
      homeVisitPersonalInformationPayload.personalData.lastName
    );
    expect(response.body.data.personalData.nationality).toEqual(
      homeVisitPersonalInformationPayload.personalData.nationality
    );
    expect(response.body.data.homeVisit).toEqual(homeVisitPersonalInformationResponse.homeVisit);
  });

  it('should return 200 status code and update Admission Request  Contact Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitContactInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.personalData).not.toBeNull();
    expect(response.body.data.homeVisit).not.toBeNull();
  });

  it('should return 200 status code and update Admission Request Address Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitAddressInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.personalData).not.toBeNull();
    expect(response.body.data.homeVisit).not.toBeNull();
  });

  it('should return 200 status code and update Admission Request Family Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitFamilyInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.personalData).not.toBeNull();
    expect(response.body.data.homeVisit.homeVisitStepsStatus.family).toEqual(
      homeVisitFamilyInformationPayload.homeVisitData.homeVisitStepsStatus.family
    );
  });

  it('should return 200 status code and update Admission Request Property Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitPropertyInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.homeVisit.houseInformation).toEqual(
      homeVisitPropertyInformationPayload.homeVisitData.houseInformation
    );
    expect(response.body.data.homeVisit.homeVisitStepsStatus.property).toEqual(
      homeVisitPropertyInformationPayload.homeVisitData.homeVisitStepsStatus.property
    );
  });

  it('should return 200 status code and update Admission Request Automobile Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitAutoMobileInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.personalData.ownACar).toEqual(
      homeVisitAutoMobileInformationPayload.personalData.ownACar
    );
    expect(response.body.data.homeVisit.homeVisitStepsStatus.automobile).toEqual(
      homeVisitAutoMobileInformationPayload.homeVisitData.homeVisitStepsStatus.automobile
    );
  });

  it('should return 200 status code and update Admission Request Debt Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitDebtInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.personalData.ownDebt).toEqual(
      homeVisitDebtInformationPayload.personalData.ownDebt
    );
    expect(response.body.data.homeVisit.homeVisitStepsStatus.debt).toEqual(
      homeVisitDebtInformationPayload.homeVisitData.homeVisitStepsStatus.debt
    );
  });

  it('should return 200 status code and update Admission Request References Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitReferencesInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.personalData.references).toEqual(
      homeVisitReferencesInformationPayload.personalData.references
    );
    expect(response.body.data.homeVisit.homeVisitStepsStatus.references).toEqual(
      homeVisitReferencesInformationPayload.homeVisitData.homeVisitStepsStatus.references
    );
  });

  it('should return 200 status code and update Admission Request Outcome Information and Home Visit', async () => {
    response = await request(app)
      .patch(`/admission/requests/${admissionRequest._id}/home-visit`)
      .set('Authorization', `Bearer ${token}`)
      .send(homeVisitOutcomeInformationPayload);

    expect(response.status).toBe(200);
    expect(response.body.data.homeVisit.visitTime).toEqual(
      homeVisitOutcomeInformationPayload.homeVisitData.visitTime
    );
    expect(response.body.data.homeVisit.homeVisitStepsStatus.outcome).toEqual(
      homeVisitOutcomeInformationPayload.homeVisitData.homeVisitStepsStatus.outcome
    );
  });
});
