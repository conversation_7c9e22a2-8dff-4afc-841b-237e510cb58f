import { Queue } from 'bullmq';
import { QueueName, connectionOptions } from '../lib/queue';
import { logger } from '../lib/logger';
import { PalencaJobRetrieveEarnings, PalencaJobRetrieveMetrics } from '../domain/entities';

export const repoAddRetrieveEarningsJob = async (job: PalencaJobRetrieveEarnings) => {
  logger.info(
    `[repoAddRetrieveEarningsJob] Addig retrieve earnings job to queue with data: ${JSON.stringify(job)}`
  );
  const queue = new Queue(QueueName.retrieve_earnings, connectionOptions);
  await queue.add(QueueName.retrieve_earnings, {
    accountId: job.accountId,
    platform: job.platform,
    requestId: job.requestId,
  });
};

export const repoAddRetrieveMetricsJob = async (job: PalencaJobRetrieveMetrics) => {
  logger.info(
    `[repoAddRetrieveMetricsJob] Addig retrieve metrics job to queue with data: ${JSON.stringify(job)}`
  );
  const queue = new Queue(QueueName.retrieve_metrics, connectionOptions);
  await queue.add(QueueName.retrieve_metrics, {
    accountId: job.accountId,
    platform: job.platform,
    requestId: job.requestId,
  });
};
