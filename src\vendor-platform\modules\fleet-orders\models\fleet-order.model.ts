import vendorDB from '@/vendor-platform/db';
import { Types, Schema, Document } from 'mongoose';

export enum FleetOrderStatus {
  CREATED = 'created',
  SENT = 'sent',
  DISPERSION = 'dispersion',
  INVOICE_LETTER_REQUEST = 'invoice_letter_request',
  INVOICE_LETTER_ARRIVAL = 'invoice_letter_arrival',
  SUPPLIER_NOTIFICATION = 'supplier_notification',
  WAITING_FOR_CARS = 'waiting_for_cars',
  DELIVERED = 'delivered',
}

export enum EvidenceType {
  LOG = 'log',
  PHOTO = 'photo',
  PDF = 'pdf',
  DOCUMENT = 'document',
}

export interface IVehicleOrder {
  brand: string; // BYD, MG, etc.
  dealer: string;
  model: string;
  version: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
}

export interface IStatusHistory {
  status: FleetOrderStatus;
  timestamp: Date;
  userId: Types.ObjectId;
  evidence?: {
    type: EvidenceType;
    url?: string;
    description: string;
  };
  notes?: string;
}

export interface IDispersion {
  state: string;
  city: string;
  quantity: number;
  deliveryDate: Date;
  amount: number;
}

export interface IInvoiceLetters {
  requestedAt: Date;
  arrivedAt?: Date;
  evidence?: {
    type: EvidenceType;
    url: string;
  };
}

export interface ISupplierNotifications {
  sentAt: Date;
  evidence: {
    description: string;
    timestamp: Date;
  };
}

export interface IFleetOrder extends Document {
  orderNumber: string; // Auto-generado: "FO-YYYY-MM-001"
  month: number;
  year: number;
  status: FleetOrderStatus;

  // Información de la orden
  vehicles: IVehicleOrder[];
  totalUnits: number;
  totalAmount: number;

  // Emails de notificación
  notificationEmails: string[];

  // SLAs y fechas
  createdAt: Date;
  sentDeadline: Date; // Día 6 del mes
  dispersionDeadline: Date; // Día 22 del mes
  invoiceLetterRequestDeadline: Date; // Día 1 del siguiente mes
  invoiceLetterArrivalDeadline: Date; // Día 4 del siguiente mes

  // Estados y evidencias
  statusHistory: IStatusHistory[];

  // Dispersión
  dispersion?: IDispersion[];

  // Cartas factura
  invoiceLetters?: IInvoiceLetters;

  // Notificaciones a proveedores
  supplierNotifications?: ISupplierNotifications;

  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  updatedAt: Date;
}

const vehicleOrderSchema = new Schema<IVehicleOrder>(
  {
    brand: { type: String, required: true },
    dealer: { type: String, required: true },
    model: { type: String, required: true },
    version: { type: String, required: true },
    quantity: { type: Number, required: true, min: 1 },
    unitPrice: { type: Number, required: true, min: 0 },
    totalAmount: { type: Number, required: true, min: 0 },
  },
  { _id: false }
);

const evidenceSchema = new Schema(
  {
    type: { type: String, enum: Object.values(EvidenceType), required: true },
    url: { type: String },
    description: { type: String, required: true },
  },
  { _id: false }
);

const statusHistorySchema = new Schema<IStatusHistory>(
  {
    status: { type: String, enum: Object.values(FleetOrderStatus), required: true },
    timestamp: { type: Date, default: Date.now },
    userId: { type: Schema.Types.ObjectId, ref: 'Users', required: true },
    evidence: evidenceSchema,
    notes: { type: String },
  },
  { _id: false }
);

const dispersionSchema = new Schema<IDispersion>(
  {
    state: { type: String, required: true },
    city: { type: String, required: true },
    quantity: { type: Number, required: true, min: 1 },
    deliveryDate: { type: Date, required: true },
    amount: { type: Number, required: true, min: 0 },
  },
  { _id: false }
);

const invoiceLettersSchema = new Schema<IInvoiceLetters>(
  {
    requestedAt: { type: Date, required: true },
    arrivedAt: { type: Date },
    evidence: {
      type: evidenceSchema,
      required: function (this: IInvoiceLetters) {
        return !!this.arrivedAt;
      },
    },
  },
  { _id: false }
);

const supplierNotificationsSchema = new Schema<ISupplierNotifications>(
  {
    sentAt: { type: Date, required: true },
    evidence: {
      description: { type: String, required: true },
      timestamp: { type: Date, default: Date.now },
    },
  },
  { _id: false }
);

const fleetOrderSchema = new Schema<IFleetOrder>(
  {
    orderNumber: {
      type: String,
      required: true,
      unique: true,
    },

    month: {
      type: Number,
      required: true,
      min: 1,
      max: 12,
    },

    year: {
      type: Number,
      required: true,
      min: 2024,
    },

    status: {
      type: String,
      enum: Object.values(FleetOrderStatus),
      default: FleetOrderStatus.CREATED,
    },

    vehicles: [vehicleOrderSchema],

    totalUnits: {
      type: Number,
      required: true,
      min: 1,
    },

    totalAmount: {
      type: Number,
      required: true,
      min: 0,
    },

    notificationEmails: [
      {
        type: String,
        required: true,
        validate: {
          validator: function (email: string) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
          },
          message: 'Email format is invalid',
        },
      },
    ],

    sentDeadline: {
      type: Date,
      required: true,
    },

    dispersionDeadline: {
      type: Date,
      required: true,
    },

    invoiceLetterRequestDeadline: {
      type: Date,
      required: true,
    },

    invoiceLetterArrivalDeadline: {
      type: Date,
      required: true,
    },

    statusHistory: [statusHistorySchema],

    dispersion: [dispersionSchema],

    invoiceLetters: invoiceLettersSchema,

    supplierNotifications: supplierNotificationsSchema,

    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'Users',
      required: true,
    },

    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'Users',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Índices para búsquedas eficientes
fleetOrderSchema.index({ orderNumber: 1 }, { unique: true });
fleetOrderSchema.index({ month: 1, year: 1 });
fleetOrderSchema.index({ status: 1 });
fleetOrderSchema.index({ createdAt: 1 });
fleetOrderSchema.index({ sentDeadline: 1 });
fleetOrderSchema.index({ dispersionDeadline: 1 });
fleetOrderSchema.index({ invoiceLetterRequestDeadline: 1 });
fleetOrderSchema.index({ invoiceLetterArrivalDeadline: 1 });

// Virtual para popular el usuario que creó la orden
fleetOrderSchema.virtual('creator', {
  ref: 'Users',
  localField: 'createdBy',
  foreignField: '_id',
  justOne: true,
});

// Virtual para popular el usuario que actualizó la orden
fleetOrderSchema.virtual('updater', {
  ref: 'Users',
  localField: 'updatedBy',
  foreignField: '_id',
  justOne: true,
});

// Middleware para calcular totales antes de guardar
fleetOrderSchema.pre('save', function (next) {
  if (this.vehicles && this.vehicles.length > 0) {
    this.totalUnits = this.vehicles.reduce((sum, vehicle) => sum + vehicle.quantity, 0);
    this.totalAmount = this.vehicles.reduce((sum, vehicle) => sum + vehicle.totalAmount, 0);

    // Calcular totalAmount de cada vehículo si no está definido
    this.vehicles.forEach((vehicle) => {
      if (!vehicle.totalAmount || vehicle.totalAmount === 0) {
        vehicle.totalAmount = vehicle.quantity * vehicle.unitPrice;
      }
    });
  }
  next();
});

fleetOrderSchema.set('toJSON', { virtuals: true });
fleetOrderSchema.set('toObject', { virtuals: true });

const FleetOrder = vendorDB.model<IFleetOrder>('FleetOrder', fleetOrderSchema);

export default FleetOrder;
