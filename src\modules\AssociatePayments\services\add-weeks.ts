import axios from 'axios';
import { CustomError } from '../../../services/customErrors';
import { getCurrentDateObject } from '../../../services/timestamps';
import { MyRequest } from '../../../types&interfaces/interfaces';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import { nextMonday, parse, previousThursday } from 'date-fns';
import TempSuscriptionPayments from '../../TempSuscriptionPayments/model/tempSuscriptionPayment.model';
import { AssociateInstanceType } from '../../../models/associateSchema';
import { MainContractInstanceType } from '../../../models/mainContractSchema';
import { AssociatePaymentInstanceType } from '../../../models/associatePayments';
import moment from 'moment';

type AddWeeks = {
  req: MyRequest;
  associatePayment: AssociatePaymentInstanceType;
  mainContract: MainContractInstanceType;
  associate: AssociateInstanceType;
};
export async function addWeeksAdendum({ req, associatePayment, mainContract, associate }: AddWeeks) {
  if (associatePayment.adendumGenerated) {
    for (let j = 0; j < associatePayment.lengthAddedBefore; j++) {
      associatePayment.paymentsArray.pop();
    }
  }
  // if (!mainContract) return res.status(404).send({ message: 'Contrato principal no encontrado' });
  if (!mainContract) throw new CustomError('Contrato principal no encontrado', 404);
  associatePayment.lengthAddedBefore = req.body.totalAddedWeeks;

  (req.body.paymentsArray as (typeof associatePayment)['paymentsArray']).forEach((el, index) => {
    const found = req.body.listChange.find((w: any) => w.index === index);
    if (found) {
      associatePayment.paymentsArray[index] = {
        ...el,
        weeklyCost: found.weeklyRent,
      };
    } else {
      associatePayment.paymentsArray[index] = {
        ...el,
        weeklyCost: el.weeklyCost || mainContract?.weeklyRent,
      };
    }
  });

  const thereIs0Weeks = associatePayment.paymentsArray.some((el) => el.weeklyCost === 0);

  if (thereIs0Weeks) {
    const today = getCurrentDateObject();

    const weeks0 = associatePayment.paymentsArray.filter((el) => el.weeklyCost === 0);
    const first = weeks0[0];
    // const [day1, month1, year1] = first.day!.split('-');

    // const date1 = new Date(Number(year1), Number(month1) - 1, Number(day1));
    // const prevThurs = previousThursday(date1);
    const prevThurs = previousThursday(parse(first.day!, 'dd-MM-yyyy', new Date()));

    // validate today is after than previous thursday 5pm, if so, desactivate the subscription
    let status = 'active';
    if (today > prevThurs) {
      await axios.patch(
        `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
        {
          status: false,
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );
    } else {
      // if not,
      status = 'pending';
    }

    console.log('STATUS: ', status, 'pppppppppppppppppppppppppppppppppppppppppppp');
    console.log('prevThurs: ', prevThurs);
    const findLast0Week = associatePayment.paymentsArray.findLastIndexCustom((el) => el.weeklyCost === 0);

    if (findLast0Week !== -1) {
      const last0Week = associatePayment.paymentsArray[findLast0Week];

      const [day, month, year] = last0Week.day!.split('-');

      const date = new Date(Number(year), Number(month) - 1, Number(day));

      const addWeeksExpirationDate = nextMonday(date);

      const stopDate = parse(first.day!, 'dd-MM-yyyy', new Date());

      const tempSubscriptionPayment = new TempSuscriptionPayments({
        tempItems: [],
        associateId: associate._id,
        associatePaymentId: associatePayment._id,
        stockId: associatePayment.vehiclesId,
        region: associatePayment.region,
        suscriptionId: associate.clientId,
        status,
        stopDate: status === 'pending' ? stopDate : undefined,
        activationDate: addWeeksExpirationDate,
        adendumType: 'add-weeks',
      });

      await tempSubscriptionPayment.save();
    }
  }
}

export function generateDateNotUTC({ date = null, currentTime = false }: any = {}) {
  const today = date ? moment(date) : moment();
  const year = today.year();
  const month = today.month();
  const day = today.date();

  const hour = currentTime ? today.hour() : 6;
  const minute = currentTime ? today.minute() : 0;
  const second = currentTime ? today.second() : 0;
  return new Date(Date.UTC(year, month, day, hour, minute, second));
}
