/* eslint-disable max-params */
import { NextFunction, Request, Response } from 'express';
import {
  BusinessException,
  UnauthorizedException,
  UnprocessableEntityException,
  ForbiddenException,
  NotFoundException,
  ServiceUnavailableException,
  InternalServerErrorException,
  SlotNotFoundException,
  AppointmentNotFoundException,
  AppointmentAlreadyExistException,
  SlotIsNotAvailableException,
  AppointmentMaximumReScheduleException,
  SlotBookingTimeLimitException,
  ScheduleNotFoundException,
  MeetingLinkCreationException,
} from '../clean/errors/exceptions';
import { exceptionSerializer } from '../clean/presentation/serializers';
import { ZodError } from 'zod';
import { logger } from '../clean/lib/logger';

// This middleware is used to catch all the exceptions thrown by the application
// and serialize them as JSON responses and logs the error.
export const catchExceptionsMiddleWare = (err: Error, req: Request, res: Response, next: NextFunction) => {
  if (err) {
    if (err instanceof BusinessException) {
      res.status(400).json(exceptionSerializer(err));
    } else if (err instanceof UnauthorizedException) {
      res.status(401).json(exceptionSerializer(err));
    } else if (err instanceof UnprocessableEntityException) {
      res.status(422).json(exceptionSerializer(err));
    } else if (err instanceof ForbiddenException) {
      res.status(403).json(exceptionSerializer(err));
    } else if (err instanceof NotFoundException) {
      res.status(404).json(exceptionSerializer(err));
    } else if (err instanceof SlotNotFoundException) {
      res.status(404).json(exceptionSerializer(err));
    } else if (err instanceof AppointmentNotFoundException) {
      res.status(404).json(exceptionSerializer(err));
    } else if (err instanceof AppointmentAlreadyExistException) {
      res.status(400).json(exceptionSerializer(err));
    } else if (err instanceof SlotIsNotAvailableException) {
      res.status(400).json(exceptionSerializer(err));
    } else if (err instanceof AppointmentMaximumReScheduleException) {
      res.status(400).json(exceptionSerializer(err));
    } else if (err instanceof SlotBookingTimeLimitException) {
      res.status(400).json(exceptionSerializer(err));
    } else if (err instanceof ScheduleNotFoundException) {
      res.status(404).json(exceptionSerializer(err));
    } else if (err instanceof MeetingLinkCreationException) {
      res.status(400).json(exceptionSerializer(err));
    } else if (err instanceof ServiceUnavailableException) {
      res.status(503).json(exceptionSerializer(err));
    } else if (err instanceof ZodError) {
      res.status(400).json(exceptionSerializer(new UnprocessableEntityException(err)));
    } else if (err instanceof ServiceUnavailableException) {
      res.status(503).json(exceptionSerializer(err));
    } else if (err instanceof ZodError) {
      res.status(400).json(exceptionSerializer(new UnprocessableEntityException(err)));
    } else {
      logger.error(err);
      res.status(500).json(exceptionSerializer(new InternalServerErrorException()));
    }
  }
  next();
};
