import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  createCrew,
  getCrews,
  getCrewById,
  updateCrew,
  deleteCrew,
  getCrewsByCity,
} from '../controllers/crew.controller';

const crewRouter = Router();

crewRouter.post('/crews', verifyTokenVendorPlatform, errorHandlerV2(createCrew));
crewRouter.get('/crews', verifyTokenVendorPlatform, errorHandlerV2(getCrews));
crewRouter.get('/crews/:crewId', verifyTokenVendorPlatform, errorHandlerV2(getCrewById));
crewRouter.get('/cities/:cityId/crews', verifyTokenVendorPlatform, errorHandlerV2(getCrewsByCity));
crewRouter.put('/crews/:crewId', verifyTokenVendorPlatform, errorHandlerV2(updateCrew));
crewRouter.delete('/crews/:crewId', verifyTokenVendorPlatform, errorHandlerV2(deleteCrew));

export default crewRouter;
